<li class="nav-item" x-data="{ open: false }">    
    <a class="nav-link dropdown-toggle" href="#" role="button" @click.prevent="open = !open">
        <!-- <span>{{ Auth::user()->id; }}</span> -->
        {{ Auth::user()->client_nick }}
    </a>
    <div x-show="open" 
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="dropdown-menu dropdown-menu-end show"
         style="display: none;">
        <a class="dropdown-item" href="{{ route('dashboard') }}" @click="open = false" wire:navigate>Панель управления</a>
        <a class="dropdown-item" href="{{ route('profile') }}" @click="open = false" wire:navigate>Профиль</a>
        <div class="dropdown-divider"></div>
        <form action="{{ route('logout') }}" method="POST">
            @csrf
            <button type="submit" class="dropdown-item">Выйти</button>
        </form>
    </div>
</li>

