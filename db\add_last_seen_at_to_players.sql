DELIMITER $$

DROP PROCEDURE IF EXISTS `add_last_seen_at_to_players`$$

CREATE PROCEDURE `add_last_seen_at_to_players`()
BEGIN
    -- Добавление поля last_seen_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = 'players'
          AND COLUMN_NAME = 'last_seen_at'
    ) THEN
        ALTER TABLE players
        ADD COLUMN last_seen_at TIMESTAMP NULL AFTER updated_at;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL add_last_seen_at_to_players();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `add_last_seen_at_to_players`;