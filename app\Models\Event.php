<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $club_id
 * @property int|null $game_id
 * @property string|null $type
 * @property string|null $name
 * @property string|null $date_from
 * @property string|null $date_to
 * @property string|null $token
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereDateFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereDateTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereGameId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Event whereType($value)
 * @mixin \Eloquent
 */
class Event extends Model
{
    protected $table = 'events';  
      
    protected $fillable = [
        'club_id', 
        'game_id', 
        'type', 
        'name',
        'date_from',
        'date_to',
        'token',
    ];
}
