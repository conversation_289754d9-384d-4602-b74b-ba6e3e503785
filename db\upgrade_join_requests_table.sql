DELIMITER $$

DROP PROCEDURE IF EXISTS `upgrade_join_requests_table`$$

CREATE PROCEDURE `upgrade_join_requests_table`()
BEGIN
    -- Добавление поля created_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'join_requests'
          AND COLUMN_NAME = 'created_at'
    ) THEN
        ALTER TABLE rgtournament.join_requests
        ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
    END IF;

    -- Добавление поля updated_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'join_requests'
          AND COLUMN_NAME = 'updated_at'
    ) THEN
        ALTER TABLE rgtournament.join_requests
        ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL upgrade_join_requests_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `upgrade_join_requests_table`;