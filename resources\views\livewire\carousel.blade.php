<div>
    <div id="gameCarousel" 
         x-data="carousel({{ Js::from($games) }}, {{ Js::from($selectedGameId) }})" 
         class="container py-4">
        <div class="row justify-content-center align-items-center">
            <div class="col-auto">
                <button class="btn btn-outline-secondary" @click="prev">&larr; </button>
            </div>

            <div class="col-auto carousel-stage">
                <template x-for="(item, index) in games" :key="item.id">
                    <div
                        class="card text-center carousel-card"
                        :class="getItemPosition(index)"
                        @click="goTo(index)"
                    >
                        <img :src="item.image" class="card-img-top" :alt="item.title" style="height: 160px; object-fit: cover;">
                        <div class="card-body">
                            <h5 class="card-title" x-text="item.title"></h5>
                            <p class="card-text" x-text="item.description"></p>
                        </div>
                    </div>
                </template>
            </div>

            <div class="col-auto">
                <button class="btn btn-outline-secondary" @click="next"> &rarr;</button>
            </div>
        </div>
    </div>
</div>

