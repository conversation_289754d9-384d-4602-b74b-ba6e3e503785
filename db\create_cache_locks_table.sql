DELIMITER $$

DROP PROCEDURE IF EXISTS `create_cache_locks_table`$$

CREATE PROCEDURE `create_cache_locks_table`()
BEGIN
    -- Создание таблицы cache_locks, если она отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'cache_locks'
    ) THEN
        CREATE TABLE `cache_locks` (
          `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
          `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
          `expiration` int(11) NOT NULL,
          PRIMARY KEY (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL create_cache_locks_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `create_cache_locks_table`;