<?php

namespace App\Livewire\Auth;

use App\Models\Club;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Illuminate\Support\Facades\Cookie;

class Register extends Component
{
    public $name;
    public $email;
    public $password;
    public $password_confirmation;
    public $clubId = null;
    public ?string $clubNameDisplay = null;

    protected $rules = [
        'name' => ['required', 'string', 'max:255', 'unique:players,client_nick'],
        'email' => ['required', 'string', 'email', 'max:255', 'unique:players,email'],
        'password' => ['required', 'string', 'min:8', 'confirmed'],
    ];

    protected $messages = [
        'name.required' => 'Пожалуйста, введите имя пользователя',
        'name.string' => 'Имя должно быть текстом',
        'name.max' => 'Имя не должно превышать 255 символов',
        'name.unique' => 'Это имя пользователя уже занято',
        'email.required' => 'Пожалуйста, введите email',
        'email.string' => 'Email должен быть текстом',
        'email.email' => 'Пожалуйста, введите корректный email',
        'email.max' => 'Email не должен превышать 255 символов',
        'email.unique' => 'Этот email уже зарегистрирован',
        'password.required' => 'Пожалуйста, введите пароль',
        'password.string' => 'Пароль должен быть текстом',
        'password.min' => 'Пароль должен содержать минимум 8 символов',
        'password.confirmed' => 'Пароли не совпадают',
    ];

    public function mount()
    {
        // Пытаемся получить clubId из cookie
        $this->clubId = Cookie::get('club_id');
        
        if ($this->clubId) {
            $club = Club::where('club_id', $this->clubId)->first();
            if ($club) {
                $this->clubNameDisplay = $club->club_name;
            } else {
                // Если клуб не найден по ID из куки, сбрасываем clubId
                $this->clubId = null;
                $this->clubNameDisplay = null;
            }
        }

    }


    public function register() {
        $this->validate();

        // Проверяем, что client_nick или email заполнены
        if (empty($this->name) && empty($this->email)) {
            $this->addError('client_nick', 'Необходимо указать имя пользователя или email');
            return;
        }
        //Pa$$w0rd!
        $user = User::create([
            'client_nick' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password), // Хешируем пароль
            'club_id' => $this->clubId, // clubId уже установлен из куки
        ]);


        Auth::login($user);

        // После успешной регистрации сохраняем clubId в cookie
        Cookie::queue('club_id', $this->clubId, 60 * 24 * 30); // 30 дней

        return redirect()->intended('/dashboard');
    }

    public function render()
    {
        return view('livewire.auth.register');
    }
}









