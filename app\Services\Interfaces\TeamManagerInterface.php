<?php

namespace App\Services\Interfaces;

use Illuminate\Support\Collection;

interface TeamManagerInterface
{
    /**
     * Получает список команд пользователя для указанной игры.
     */
    public function getUserTeamsForGame(int $userId, ?int $gameId = null): Collection;

    /**
     * Расформировывает команду.
     */
    public function disbandTeam(int $teamId, int $userId): bool;

    /**
     * Исключает участника из команды.
     */
    public function kickMember(int $teamId, int $memberId, int $captainId): bool;

    /**
     * Проверяет, является ли пользователь капитаном команды.
     */
    public function isUserCaptainOfTeam(int $userId, int $teamId): bool;

    /**
     * Проверяет, состоит ли пользователь в команде для указанной игры.
     */
    public function hasUserTeamForGame(int $userId, int $gameId): bool;

    /**
     * Получает требуемый размер команды для игры.
     */
    public function getRequiredTeamSize(int $gameId): int;

    /**
     * Проверяет, полна ли команда.
     */
    public function isTeamFull(int $teamId): bool;
}