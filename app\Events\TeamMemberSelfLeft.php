<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel; // Добавляем импорт PrivateChannel
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Team; // Добавляем импорт модели Team
use Illuminate\Support\Facades\Log; // Добавляем импорт Log

class TeamMemberSelfLeft implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $teamId;
    public $playerId;
    public $captainId; // Добавляем свойство для ID капитана

    /**
     * Create a new event instance.
     */
    public function __construct(int $teamId, int $playerId)
    {
        $this->teamId = $teamId;
        $this->playerId = $playerId;
        // Получаем ID капитана команды
        $team = Team::find($teamId);
        $this->captainId = $team ? $team->captain_id : null;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        Log::info('TeamMemberSelfLeft: broadcastOn вызван', [
            'team_id' => $this->teamId,
            'player_id' => $this->playerId,
            'captain_id' => $this->captainId
        ]);

        $channels = [
            new Channel('team.' . $this->teamId),
            new Channel('player.' . $this->playerId), // Добавляем канал для покидающего игрока
        ];

        // Если есть капитан, добавляем его приватный канал
        if ($this->captainId) {
            $channels[] = new PrivateChannel('player.' . $this->captainId);
        }

        return $channels;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'team.member.self.left';
    }
}