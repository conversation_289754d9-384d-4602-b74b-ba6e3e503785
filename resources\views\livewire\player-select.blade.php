<div x-data="{
    open: false,
    search: '',
    selected: @entangle('selected'),
    options: @js($options),
    itemValue: '{{ $itemValue }}',
    itemText: '{{ $itemText }}',
    getSelectedText() {
        if (!this.selected) return '{{ $placeholder }}';
        const option = this.options.find(opt => opt[this.itemValue] == this.selected);
        return option ? option[this.itemText] : '{{ $placeholder }}';
    },
    getSelectedAvatar() {
        if (!this.selected) return '/images/default-avatar.png';
        const option = this.options.find(opt => opt[this.itemValue] == this.selected);
        return option?.avatar || '/images/default-avatar.png';
    },
    filteredOptions() {
        return this.options.filter(opt => 
            opt[this.itemText] && opt[this.itemText].toLowerCase().includes(this.search.toLowerCase())
        );
    }
}" class="relative">
    @if($label)
        <label for="{{ $name }}" class="form-label">
            {{ $label }} @if($required)<span class="text-danger">*</span>@endif
        </label>
    @endif

    <div class="form-control @error($name) is-invalid @enderror cursor-pointer"
         @click="open = !open"
         :class="{'border-primary': open}">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <template x-if="selected">
                    <img :src="getSelectedAvatar()" 
                         class="rounded-circle me-2" 
                         style="width: 24px; height: 24px; object-fit: cover;">
                </template>
                <span x-text="getSelectedText()" :class="{'text-muted': !selected}"></span>
            </div>
            <i class="ri-arrow-down-s-line" :class="{'rotate-180': open}"></i>
        </div>
    </div>

    <div x-show="open" 
         x-transition
         @click.away="open = false"
         class="position-absolute w-100 mt-1 bg-white border rounded shadow-sm z-index-dropdown" style="width: 93% !important;">
        <div class="p-2">
            <input type="text" 
                   class="form-control form-control-sm" 
                   placeholder="Поиск..." 
                   x-model="search"
                   @click.stop>
        </div>
        <div class="dropdown-menu-items ps-3" style="max-height: 200px; overflow-y: auto;">
            <template x-for="option in filteredOptions()" :key="option[itemValue]">
                <div @click.stop="selected = option[itemValue]; open = false"
                     class="dropdown-item cursor-pointer d-flex align-items-center"
                     :class="{'active': selected == option[itemValue]}">
                    <img :src="option.avatar || '/images/default-avatar.png'" 
                         class="rounded-circle me-2" 
                         style="width: 24px; height: 24px; object-fit: cover;">
                    <span x-text="option[itemText]"></span>
                </div>
            </template>
            <div x-show="filteredOptions().length === 0" class="dropdown-item text-muted">
                Ничего не найдено
            </div>
        </div>
    </div>
    
    @error($name) <div class="invalid-feedback d-block">{{ $message }}</div> @enderror

    <style>
        .z-index-dropdown {
            z-index: 1000;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .rotate-180 {
            transform: rotate(180deg);
        }
    </style>
</div> 