/* Переопределения для темной темы Bootstrap */
body {
    background-color: #232333 !important;
    color: #d1d2e8 !important; /* Светлее, чем было */
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    color: #ffffff !important; /* Белый для заголовков */
}

p, span, div, li {
    color: #d1d2e8 !important; /* Светлее, чем было */
}

.text-muted {
    color: #9fa1c5 !important; /* Светлее, чем было */
}

.card, .modal-content, .offcanvas, .dropdown-menu {
    background-color: #2b2c40 !important;
    color: #d1d2e8 !important; /* Светлее, чем было */
}

.card-header, .modal-header, .offcanvas-header {
    background-color: #323349 !important;
    border-color: #444564 !important;
}

.navbar, .footer {
    background-color: #2b2c40 !important;
}

/* Стили для таблиц */
.table {
    color: #a3a4cc !important;
}

.table thead th {
    background-color: #323349 !important;
    color: #cbcbe2 !important;
    border-color: #444564 !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
    background-color: rgba(0, 0, 0, 0.15) !important;
}

.table-hover > tbody > tr:hover > * {
    background-color: rgba(140, 87, 255, 0.1) !important;
    color: #fff !important;
}

.table td, .table th {
    border-color: #444564 !important;
}

/* Для таблиц с границами */
.table-bordered {
    border-color: #444564 !important;
}

.table-bordered td, .table-bordered th {
    border-color: #444564 !important;
}

.form-control, .form-select, .input-group-text {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    color: #d1d2e8 !important; /* Светлее, чем было */
}

.btn-light, .btn-outline-light {
    background-color: #444564 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
}

.text-dark {
    color: #ffffff !important; /* Заменяем темный текст на белый в темном режиме */
}

.border, .border-top, .border-bottom, .border-start, .border-end {
    border-color: #444564 !important;
}

.list-group-item {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
}

.nav-link, .dropdown-item {
    color: #d1d2e8 !important; /* Светлее, чем было */
}

.nav-link:hover, .dropdown-item:hover {
    background-color: #323349 !important;
    color: #fff !important;
}

.nav-tabs, .nav-pills {
    border-color: #444564 !important;
}

.nav-tabs .nav-link.active, .nav-pills .nav-link.active {
    background-color: #8c57ff !important;
    color: #fff !important;
}

.pagination .page-link {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
}

.pagination .page-item.active .page-link {
    background-color: #8c57ff !important;
    border-color: #8c57ff !important;
    color: #fff !important;
}

.alert {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
}

.alert-success {
    background-color: rgba(86, 202, 0, 0.2) !important;
    border-color: rgba(86, 202, 0, 0.3) !important;
}

.alert-danger {
    background-color: rgba(255, 76, 81, 0.2) !important;
    border-color: rgba(255, 76, 81, 0.3) !important;
}

.alert-warning {
    background-color: rgba(255, 180, 0, 0.2) !important;
    border-color: rgba(255, 180, 0, 0.3) !important;
}

.alert-info {
    background-color: rgba(22, 177, 255, 0.2) !important;
    border-color: rgba(22, 177, 255, 0.3) !important;
}

/* Стили для карусели */
.carousel-card {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.4) !important;
}

.carousel-card h1, .carousel-card h2, .carousel-card h3, 
.carousel-card h4, .carousel-card h5, .carousel-card h6 {
    color: #cbcbe2 !important;
}

.carousel-card p, .carousel-card span, .carousel-card div {
    color: #a3a4cc !important;
}

.carousel-card.active {
    background-color: #323349 !important;
    border-color: #8c57ff !important;
}

.carousel-card img {
    filter: brightness(0.85) contrast(1.1); /* Улучшает видимость изображений в темном режиме */
}

.carousel-control-prev-icon, .carousel-control-next-icon {
    filter: invert(1) grayscale(100) brightness(200%); /* Делает стрелки карусели видимыми */
}

/* Исправление для темного текста в компонентах */
.btn-primary, .btn-secondary, .btn-success, .btn-info, .btn-warning, .btn-danger {
    color: #ffffff !important;
}

.btn-light {
    color: #d1d2e8 !important;
}

/* Исправление для ссылок */
a {
    color: #a78bff !important; /* Светлее, чем основной цвет */
}

a:hover {
    color: #c4b3ff !important; /* Еще светлее при наведении */
}

/* Улучшения для карточек */
.card {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.2) !important;
}

.card-header {
    background-color: #323349 !important;
    border-color: #444564 !important;
    color: #ffffff !important;
}

.card-footer {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
}

/* Улучшения для кнопок */
.btn {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3) !important;
}

/* Улучшенные стили для кнопок primary */
.btn-primary {
    background-color: #8c57ff !important;
    border-color: #8c57ff !important;
    color: #ffffff !important;
    box-shadow: 0 0.125rem 0.25rem rgba(140, 87, 255, 0.4) !important;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #9d73ff !important;
    border-color: #9d73ff !important;
    box-shadow: 0 0.25rem 0.5rem rgba(140, 87, 255, 0.5) !important;
}

.btn-primary:active, .btn-primary.active {
    background-color: #7e4ee6 !important;
    border-color: #7e4ee6 !important;
}

/* Улучшенные стили для outline кнопок */
.btn-outline-primary {
    color: #a78bff !important;
    border-color: #8c57ff !important;
    background-color: transparent !important;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    color: #ffffff !important;
    background-color: #8c57ff !important;
    border-color: #8c57ff !important;
    box-shadow: 0 0.25rem 0.5rem rgba(140, 87, 255, 0.3) !important;
}

.btn-outline-primary:active, .btn-outline-primary.active {
    color: #ffffff !important;
    background-color: #7e4ee6 !important;
    border-color: #7e4ee6 !important;
}

/* Улучшения для форм */
.form-control:focus, .form-select:focus {
    border-color: #8c57ff !important;
    box-shadow: 0 0 0 0.25rem rgba(140, 87, 255, 0.25) !important;
}

/* Улучшения для бейджей */
.badge {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2) !important;
}

/* Улучшения для предупреждений */
.alert {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1) !important;
}

/* Исправления для проблем с контрастом */
.bg-light {
    background-color: #323349 !important; /* Заменяем светлый фон на темный */
    color: #d1d2e8 !important;
}

.bg-white {
    background-color: #2b2c40 !important; /* Заменяем белый фон на темный */
    color: #d1d2e8 !important;
}

/* Исправления для иконок */
.text-dark i, .text-dark svg {
    color: #ffffff !important;
}

/* Исправления для выпадающих меню */
.dropdown-menu {
    background-color: #323349 !important;
    border-color: #444564 !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3) !important;
}

.dropdown-item {
    color: #d1d2e8 !important;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: #444564 !important;
    color: #ffffff !important;
}

.dropdown-divider {
    border-color: #444564 !important;
}

/* Улучшенные стили для label к input */
label {
    color: #cbcbe2 !important; /* Более светлый цвет для лучшей видимости */
    font-weight: 500 !important; /* Немного жирнее для лучшей читаемости */
}

/* Стили для плавающих label (floating labels) */
.form-floating > label {
    color: #9fa1c5 !important;
    background-color: transparent !important;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    color: #a78bff !important; /* Цвет primary при фокусе */
    opacity: 1 !important;
}

/* Стили для outline floating labels */
.form-floating-outline label {
    color: #9fa1c5 !important;
}

.form-floating-outline .form-control:focus ~ label,
.form-floating-outline .form-control:focus ~ .form-label {
    color: #a78bff !important;
    background-color: #2b2c40 !important;
    opacity: 1 !important;
}

.form-floating-outline label::after,
.form-floating-outline > span::after {
    background-color: #2b2c40 !important; /* Фон для label в outline режиме */
}

/* Улучшение для label в чекбоксах и радио-кнопках */
.form-check-label {
    color: #d1d2e8 !important;
}


