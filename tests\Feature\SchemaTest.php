<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SchemaTest extends TestCase
{
    /**
     * Тест для проверки создания таблицы через Schema.
     */
    public function test_can_create_table_with_schema(): void
    {
        // Удаляем таблицу, если она существует
        Schema::dropIfExists('test_players');
        
        // Создаем таблицу
        Schema::create('test_players', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->timestamps();
        });
        
        // Проверяем, что таблица создана
        $this->assertTrue(Schema::hasTable('test_players'));
        
        // Вставляем данные
        DB::table('test_players')->insert([
            'name' => 'Test Player',
            'email' => '<EMAIL>',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что данные вставлены
        $result = DB::table('test_players')->where('name', 'Test Player')->first();
        
        $this->assertNotNull($result);
        $this->assertEquals('<EMAIL>', $result->email);
    }
}