<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Process;
use Tests\TestCase;

class ProcessAndUsersTest extends TestCase
{
    /**
     * Тест для проверки работы с фейковыми процессами и таблицей players.
     */
    public function test_process_fake_and_users_table(): void
    {
        // Создаем таблицу players, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_nick TEXT,
                email TEXT UNIQUE,
                password TEXT,
                remember_token TEXT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Вставляем данные
        DB::table('players')->insert([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Фейковые процессы
        Process::fake([
            'ls -la' => Process::result(
                output: 'Fake directory listing',
                errorOutput: '',
                exitCode: 0
            )
        ]);
        
        // Запускаем процесс
        $result = Process::run('ls -la');
        
        // Проверяем, что процесс был запущен
        Process::assertRan('ls -la');
        
        // Проверяем результат
        $this->assertEquals(0, $result->exitCode());
        $this->assertEquals('Fake directory listing', trim($result->output()));
        
        // Проверяем, что данные в таблице players существуют
        $this->assertDatabaseHas('players', [
            'client_nick' => 'Test User'
        ]);
    }
}