<div>
    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Всего матчей</h5>
                    <h2 class="mb-0">{{ $totalMatches }}</h2>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Победы</h5>
                    <h2 class="text-success mb-0">{{ $victories }}</h2>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Поражения</h5>
                    <h2 class="text-danger mb-0">{{ $defeats }}</h2>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Процент побед</h5>
                    <h2 class="mb-0">{{ $winRate }}%</h2>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">Статистика по играм</div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Игра</th>
                            <th>Всего матчей</th>
                            <th>Победы</th>
                            <th>Поражения</th>
                            <th>Процент побед</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($gameStats as $stat)
                            <tr>
                                <td>{{ $stat['game_type'] }}</td>
                                <td>{{ $stat['total'] }}</td>
                                <td class="text-success">{{ $stat['victories'] }}</td>
                                <td class="text-danger">{{ $stat['defeats'] }}</td>
                                <td>{{ $stat['win_rate'] }}%</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">Нет данных о матчах</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="text-center">
        <a href="{{ route('matches') }}" class="btn btn-primary">Просмотреть все матчи</a>
    </div>
</div>