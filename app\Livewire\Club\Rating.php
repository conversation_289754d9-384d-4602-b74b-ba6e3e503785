<?php

namespace App\Livewire\Club;

use Livewire\Component;
use App\Services\RatingService;
use Livewire\Attributes\On;
use App\Models\FullRating; // Добавлено

class Rating extends Component
{
    public $clubId;
    public $apiKey;
    public $ratings = [];

    protected $ratingService;

    public function boot(RatingService $ratingService)
    {
        $this->ratingService = $ratingService;
    }

    public function mount($clubId, $apiKey)
    {
        $this->clubId = $clubId;
        $this->apiKey = $apiKey;
        $this->loadRatings();
    }

    #[On('updateRating')]
    public function loadRatings()
    {
        // Проверка API ключа (простая заглушка, в реальном приложении нужна более надежная проверка)
        if ($this->apiKey !== 'YOUR_API_KEY_HERE') {
            $this->ratings = [];
            return;
        }

        // Получаем всех игроков клуба с их рейтингами
        $players = FullRating::with(['player', 'game'])
            ->where('club_id', $this->clubId)
            ->orderBy('game_rating', 'desc')
            ->get();

        $formattedRatings = [];
        foreach ($players as $rating) {
            $formattedRatings[] = [
                'player_name' => $rating->player->name ?? 'N/A',
                'game_name' => $rating->game->name ?? 'N/A',
                'rating' => $rating->game_rating,
            ];
        }
        $this->ratings = $formattedRatings;
    }

    public function render()
    {
        return view('livewire.club.rating', [
            'ratings' => $this->ratings,
        ]);
    }
}
