<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $club_id
 * @property int|null $player_id
 * @property int|null $season_id
 * @property string|null $game_id
 * @property int|null $game_rating
 * @property string|null $game_now
 * @property-read \App\Models\Club|null $club
 * @property-read \App\Models\Game|null $game
 * @property-read \App\Models\User|null $player
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating whereGameId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating whereGameNow($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating whereGameRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FullRating whereSeasonId($value)
 * @mixin \Eloquent
 */
class FullRating extends Model
{
    //use HasFactory;
    
    protected $table = 'rating';
    protected $fillable = [
        'club_id',
        'player_id',
        'season_id',
        'game_id', 
        'game_rating',
        'game_now',
    ];

    public function player()
    {
        return $this->belongsTo(User::class, 'player_id', 'id');
    }

    public function game()
    {
        return $this->belongsTo(Game::class, 'game_id', 'id');
    }

    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }
}
