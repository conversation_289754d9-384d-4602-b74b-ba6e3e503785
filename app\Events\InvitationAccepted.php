<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InvitationAccepted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public int $teamId,
        public int $captainId,
        public int $playerId
    ) {}

    public function broadcastOn()
    {
        return new PrivateChannel('player.' . $this->captainId);
    }

    public function broadcastWith()
    {
        return [
            'teamId' => $this->teamId,
            'captainId' => $this->captainId,
            'playerId' => $this->playerId
        ];
    }
}