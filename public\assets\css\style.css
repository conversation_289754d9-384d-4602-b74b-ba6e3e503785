.carousel-stage {
    position: relative;
    width: 100%;
    max-width: 700px;
    height: 300px;
    margin: 0 5rem 0 5rem;
}

.carousel-card {
    position: absolute;
    top: 0;
    width: 60%;
    transition: all 0.5s ease-in-out;
    opacity: 0;
    transform: scale(0.8);
    z-index: 0;
    cursor: pointer;
}

.carousel-card.active {
    left: 20%;
    opacity: 1;
    transform: scale(1) translateX(0);
    z-index: 3;
}

.carousel-card.left {
    left: 0;
    opacity: 0.8;
    transform: scale(0.9) translateX(-20%);
    z-index: 1;
}

.carousel-card.right {
    left: 40%;
    opacity: 0.8;
    transform: scale(0.9) translateX(20%);
    z-index: 1;
}

.carousel-card.hidden {
    opacity: 0;
    pointer-events: none;
}
@media (min-width: 992px) {
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        z-index: 9;
    }
}

.raring_block span {
    grid-area: 1 / 1;
    color: lawngreen;
    font-size: 40px;
    font-weight: bold;
    text-shadow: 1px 1px 13px #001414;
}

.rating_line span {
    position: absolute;
    color: #8c57ff;
    font-weight: bold;
    font-size: 34px;
    top: 58%;
    z-index: 2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.tooltip-alpine {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
    pointer-events: none; /* Чтобы тултип не перехватывал события мыши */
    width: max-content; /* Чтобы тултип подстраивался под размер текста */
}

.tooltip-alpine::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

/* Добавляем класс position-relative к кнопкам, если его еще нет */
.btn {
    position: relative;
}

.gradient-bar {
    width: 100%;
    height: 22px;
    background: linear-gradient(to right, 
        #ff0000 0%, /* красный - низкий рейтинг */
        #ffff00 40%, /* желтый - средний рейтинг */
        #00ff00 70%, /* зеленый - высокий рейтинг */
        #0000ff 100% /* синий - очень высокий рейтинг */
    );
    border-radius: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    padding-top: 1rem;
}

.team-rating{
    text-shadow: 1px 1px 13px #695c87 !important;
    top: 0 !important;
    left: 50%; 
    transform: translateX(-50%); 
    z-index: 2; 
    color: #8c57ff; 
    font-weight: bold; 
    font-size: 40px;
}
/* Добавьте дополнительные стили для темной темы, если необходимо */
.dark-style .custom-component {
    background-color: #2b2c40;
    color: #a3a4cc;
}

.dark-style .form-control {
    background-color: #2b2c40;
    border-color: #444564;
    color: #a3a4cc;
}

/* Принудительное применение темной темы */
html {
    background-color: #232333 !important;
}

body {
    background-color: #232333 !important;
    color: #a3a4cc !important;
}

.card {
    background-color: #2b2c40 !important;
}

.navbar {
    background-color: #2b2c40 !important;
}

.dropdown-menu {
    background-color: #2b2c40 !important;
    color: #a3a4cc !important;
}

.dropdown-item {
    color: #a3a4cc !important;
}

.dropdown-item:hover {
    background-color: #323349 !important;
}

.form-control, .form-select {
    background-color: #2b2c40 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
}

.btn-light {
    background-color: #444564 !important;
    border-color: #444564 !important;
    color: #a3a4cc !important;
}

.table {
    color: #a3a4cc !important;
}

.modal-content {
    background-color: #2b2c40 !important;
    color: #a3a4cc !important;
}
.nav-link.active {
    font-weight: 600;
    color: #fff !important;
    background-color: #323349 !important;
}

.table th {
    color: #918f95;
}

.table > :not(caption) > * > * {
    color: var(--bs-table-color-state, var(--bs-table-color-type, #898395));
}
.bg-warning {
    --bs-bg-opacity: 1;
    background-color: #ffab00 !important;
    color: #6d6777 !important;
}
input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
        -webkit-text-fill-color: #c1cbd8 !important;
        -webkit-box-shadow: 0 0 0px 1000px #232333 inset !important; 
        transition: background-color 5000s ease-in-out 0s;
               
}

.modal.show {
    display: block;
    overflow-x: hidden;
    overflow-y: auto;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    z-index: 1040;
}

.modal-backdrop.show {
    opacity: 0.5;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 1.75rem auto;
    max-width: 500px;
    z-index: 1050;
}

.modal-dialog.modal-xl {
    max-width: 1200px;
}

body.modal-open {
    overflow: hidden;
    padding-right: 17px; /* Компенсация ширины полосы прокрутки */
}
[x-tooltip] {
position: relative;
}
[x-tooltip]:before {
    content: attr(x-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s, visibility 0.2s;
    z-index: 1000;
}
[x-tooltip]:hover:before {
    visibility: visible;
    opacity: 1;
}
.tooltip-alpine {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
    pointer-events: none;
}

.tooltip-alpine::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}