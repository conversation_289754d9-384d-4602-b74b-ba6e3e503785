<?php

namespace App\Livewire\Auth;

use Livewire\Component;
use App\Services\TokenService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TokenManager extends Component
{
    // Свойства для текущего токена
    public ?string $currentToken = null;
    public ?array $currentTokenInfo = null;
    public bool $showCurrentTokenInfo = false;
    // Свойства для проверяемого токена
    public ?string $searchToken = null;
    public ?\App\Models\User $searchTokenInfo = null;
    public bool $showSearchTokenInfo = false;
    
    
    // Общие свойства
    public ?int $clubId = null;
    public int $expiryHours = 24;

    protected TokenService $tokenService;

    // Правила валидации
    protected $rules = [
        'searchToken' => 'required|string|min:10',
        'clubId' => 'required|integer|min:1',
        'expiryHours' => 'required|integer|min:1|max:168'
    ];

    public function boot(TokenService $tokenService)
    {
        $this->tokenService = $tokenService;
    }

    public function mount()
    {
        if (Auth::check()) {
            $this->clubId = Auth::user()->club_id;
            $this->getCurrentTokenInfo();
        }
    }

    public function getCurrentTokenInfo()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                $this->dispatch('showNotification', [
                    'type' => 'error',
                    'message' => 'Пользователь не авторизован'
                ]);
                return;
            }

            $this->currentTokenInfo = $this->tokenService->getTokenInfo($user);
            $this->showCurrentTokenInfo = true;

        } catch (\Exception $e) {
            Log::error('Failed to get token info', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при получении информации о токене'
            ]);
        }
    }

    public function validateToken()
    {
        try {
            $this->validate([
                'searchToken' => 'required|string|min:10',
                'clubId' => 'required|integer|min:1',
            ]);

            $user = Auth::user();
            if ($user->club_id != $this->clubId) {
                $this->dispatch('showNotification', [
                    'type' => 'error',
                    'message' => 'Вы не можете проверять токены для других клубов'
                ]);
                return;
            }

            $this->searchTokenInfo = $this->tokenService->validateToken(
                $this->searchToken,
                (int)$this->clubId
            );
            $this->showSearchTokenInfo = true;

            if ($this->searchTokenInfo) {
                $this->dispatch('showNotification', [
                    'type' => 'success',
                    'message' => 'Токен успешно проверен'
                ]);
            } else {
                $this->dispatch('showNotification', [
                    'type' => 'warning',
                    'message' => 'Токен не найден или не валиден'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Token validation failed', [
                'error' => $e->getMessage(),
                'club_id' => $this->clubId
            ]);

            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при проверке токена'
            ]);
        }
    }

    public function generateToken()
    {
        try {
            $this->validate(['expiryHours' => 'required|integer|min:1|max:168']);
            
            $user = Auth::user();
            if (!$user) {
                throw new \Exception('Пользователь не авторизован');
            }

           $token = $this->tokenService->generateToken($user, $user->club_id, $this->expiryHours);
           $this->getCurrentTokenInfo();

            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Токен успешно создан'
            ]);

        } catch (\Exception $e) {
            Log::error('Token generation failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при создании токена'
            ]);
        }
    }

    public function extendToken()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new \Exception('Пользователь не авторизован');
            }

            if ($this->tokenService->extendToken($user, $this->expiryHours)) {
                $this->getCurrentTokenInfo();
                $this->dispatch('showNotification', [
                    'type' => 'success',
                    'message' => 'Срок действия токена продлен'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Token extension failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при продлении токена'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.auth.token-manager');
    }
}