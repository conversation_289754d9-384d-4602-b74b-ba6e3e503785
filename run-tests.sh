#!/bin/bash

# Проверяем наличие файла дампа
if [ ! -f "rgtournament_copy_dump.sql" ]; then
    echo "Ошибка: Файл дампа rgtournament_copy_dump.sql не найден!"
    exit 1
fi

# Конвертируем MySQL дамп в SQLite схему, если файл схемы не существует
if [ ! -f "database/sqlite_schema.sql" ]; then
    echo "Конвертация MySQL дампа в SQLite схему..."
    php artisan db:convert-dump
fi

# Запускаем тесты
echo "Запуск тестов..."
php artisan test