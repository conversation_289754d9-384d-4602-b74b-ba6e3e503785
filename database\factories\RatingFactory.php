<?php

namespace Database\Factories;

use App\Models\Club;
use App\Models\Game;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Rating>
 */
class RatingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'club_id' => Club::inRandomOrder()->first()->id ?? Club::factory()->create()->id,
            'player_id' => User::inRandomOrder()->first()->id ?? User::factory()->create()->id, // Изменено с user_id на player_id
            'game_id' => Game::inRandomOrder()->first()->id ?? Game::factory()->create()->id,
            'game_rating' => fake()->numberBetween(100, 9999),
        ];
    }
}

