DELIMITER $$

DROP PROCEDURE IF EXISTS `upgrade_players_table`$$

CREATE PROCEDURE `upgrade_players_table`()
BEGIN
    -- Добавление индекса к полю client_nick, если он отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.STATISTICS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'players'
          AND INDEX_NAME = 'idx_client_nick'
    ) THEN
        ALTER TABLE rgtournament.players
        ADD INDEX idx_client_nick (client_nick);
    END IF;

    -- Удаление индекса email, если он существует
    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.STATISTICS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'players'
          AND INDEX_NAME = 'email'
    ) THEN
        ALTER TABLE rgtournament.players
        DROP INDEX email;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL upgrade_players_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `upgrade_players_table`;