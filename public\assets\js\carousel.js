document.addEventListener('alpine:init', () => {
    Alpine.data('carousel', (games, selectedGameId) => ({
        games: games,
        currentIndex: 0,
        lastSelectedGameId: null,

        init() {
            if (selectedGameId) {
                const selectedIndex = this.games.findIndex(game => game.id == selectedGameId);
                if (selectedIndex !== -1) {
                    this.currentIndex = selectedIndex;
                    this.lastSelectedGameId = selectedGameId; // Устанавливаем начальное значение
                    // Не вызываем updateItemIndex() при инициализации
                }
            }
        },

        getItemPosition(index) {
            const total = this.games.length;
            const prevIndex = (this.currentIndex - 1 + total) % total;
            const nextIndex = (this.currentIndex + 1) % total;

            if (index === this.currentIndex) return 'active';
            if (index === prevIndex) return 'left';
            if (index === nextIndex) return 'right';
            return 'hidden';
        },

        next() {
            this.currentIndex = (this.currentIndex + 1) % this.games.length;
            this.updateItemIndex();
        },

        prev() {
            this.currentIndex = (this.currentIndex - 1 + this.games.length) % this.games.length;
            this.updateItemIndex();
        },

        goTo(index) {
            this.currentIndex = index;
            this.updateItemIndex();
        },
        
        updateItemIndex() {
            const gameId = this.games[this.currentIndex].id;
            // Отправляем событие только если игра действительно изменилась
            if (this.lastSelectedGameId !== gameId) {
                this.lastSelectedGameId = gameId;
                console.log('Carousel: Отправляем gameSelected для игры', gameId);
                Livewire.dispatch('gameSelected', { gameId: gameId });
            }
        }
    }));
}); 