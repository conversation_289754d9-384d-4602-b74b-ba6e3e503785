<?php

namespace Tests\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

trait UsesSqliteTestDatabase
{
    /**
     * Настройка перед каждым тестом.
     */
    protected function setUpSqliteDatabase(): void
    {
        // Проверяем, что используется SQLite в памяти
        $this->assertEquals('sqlite', config('database.default'));
        $this->assertEquals(':memory:', config('database.connections.sqlite.database'));
        
        // Создаем необходимые таблицы для тестов
        $this->createTestTables();
    }
    
    /**
     * Очистка после каждого теста.
     */
    protected function tearDownSqliteDatabase(): void
    {
        // Удаляем все таблицы
        $this->dropTestTables();
    }
    
    /**
     * Создает необходимые таблицы для тестов.
     */
    protected function createTestTables()
    {
        // teams
        Schema::create('teams', function ($table) {
            $table->increments('id');
            $table->string('name', 100);
            $table->integer('game_id');
            $table->integer('captain_id');
            $table->integer('rating')->default(10);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->unique(['name', 'game_id']);
        });

        // live_match
        Schema::create('live_match', function ($table) {
            $table->increments('id');
            $table->integer('team1_id');
            $table->integer('team2_id');
            $table->enum('status', ['map_voting', 'ready_check', 'live'])->default('map_voting');
            $table->unsignedBigInteger('selected_map_id')->nullable();
            $table->integer('current_voting_team')->nullable();
            $table->enum('current_voter', ['team1', 'team2'])->default('team1');
        });

        // games
        Schema::create('games', function ($table) {
            $table->increments('id');
            $table->string('name', 255);
            $table->string('image', 255)->default('');
            $table->unsignedSmallInteger('team_size');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->unique('name');
        });

        // matchmaking
        Schema::create('matchmaking', function ($table) {
            $table->increments('id');
            $table->integer('team_id');
            $table->integer('game_id');
            $table->dateTime('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->enum('status', ['searching', 'matched'])->default('searching');
        });

        // cs2_maps
        Schema::create('cs2_maps', function ($table) {
            $table->increments('id');
            $table->string('name', 50);
            $table->string('image_url', 255)->nullable();
            $table->boolean('is_active')->default(true);
        });

        // map_votes
        Schema::create('map_votes', function ($table) {
            $table->integer('match_id');
            $table->integer('map_id');
            $table->integer('banned_by')->nullable();
            $table->primary(['match_id', 'map_id']);
        });

        // players
        Schema::create('players', function ($table) {
            $table->increments('id');
            $table->string('club_id', 100)->nullable();
            $table->string('client_id', 100)->nullable();
            $table->string('client_nick', 100)->nullable();
            $table->string('client_hash', 100)->nullable();
            $table->string('client_password', 100)->nullable();
            $table->string('auth_token', 100)->nullable();
            $table->dateTime('auth_token_from_date')->nullable();
            $table->string('client_status', 10)->default('web');
            $table->boolean('change_password')->default(0);
            $table->tinyInteger('remember_token')->nullable();
            $table->string('email', 255)->nullable();
            $table->timestamp('created_at')->nullable()->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->nullable()->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->string('avatar', 255)->nullable();
            $table->unique('email');
        });

        // team_members
        Schema::create('team_members', function ($table) {
            $table->increments('id');
            $table->integer('player_id');
            $table->integer('team_id');
            $table->enum('role', ['captain', 'member']);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->unique(['player_id', 'team_id', 'id']);
        });

        // team_invitations
        Schema::create('team_invitations', function ($table) {
            $table->bigIncrements('id');
            $table->integer('team_id');
            $table->integer('player_id');
            $table->integer('invited_by');
            $table->enum('status', ['pending', 'accepted', 'rejected'])->default('pending');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->unique(['team_id', 'player_id', 'status']);
        });

        // join_requests
        Schema::create('join_requests', function ($table) {
            $table->increments('id');
            $table->integer('player_id');
            $table->integer('team_id');
            $table->enum('status', ['pending', 'accepted', 'rejected'])->default('pending');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->unique(['player_id', 'team_id']);
        });

        // live_match_ready
        Schema::create('live_match_ready', function ($table) {
            $table->integer('match_id');
            $table->integer('player_id');
            $table->boolean('is_ready')->default(0);
            $table->primary(['match_id', 'player_id']);
        });

        // live_match_banned_maps
        Schema::create('live_match_banned_maps', function ($table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('match_id');
            $table->unsignedBigInteger('map_id');
            $table->unsignedBigInteger('team_id');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->index('map_id');
            $table->index('match_id');
            $table->index('team_id');
        });
    }
    
    /**
     * Удаляет все таблицы после теста.
     */
    protected function dropTestTables()
    {
        Schema::dropIfExists('live_match_banned_maps');
        Schema::dropIfExists('live_match');
        Schema::dropIfExists('cs2_maps');
        Schema::dropIfExists('team_members');
        Schema::dropIfExists('teams');
        Schema::dropIfExists('players');
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('games');
        Schema::dropIfExists('game_matches');
        Schema::dropIfExists('match_results');
    }
}




