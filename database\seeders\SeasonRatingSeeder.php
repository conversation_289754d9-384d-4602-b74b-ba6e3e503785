<?php

namespace Database\Seeders;

use App\Models\Club;
use App\Models\Game;
use App\Models\Rating;
use App\Models\Season;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SeasonRatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Получаем всех пользователей, игры и клубы
        $users = User::all();
        $games = Game::all();
        $clubs = Club::all();
        
        // Если клубов нет, создаем хотя бы один
        if ($clubs->isEmpty()) {
            $clubs = collect([Club::factory()->create()]);
        }
        
        // Получаем текущий активный сезон или создаем его, если не существует
        $currentSeason = Season::where('is_active', true)->first();
        if (!$currentSeason) {
            $currentSeason = Season::create([
                'name' => 'Сезон ' . date('Y'),
                'start_date' => now()->startOfMonth(),
                'end_date' => now()->addMonths(3)->endOfMonth(),
                'is_active' => true,
            ]);
        }
        
        // Для каждого пользователя создаем рейтинги для каждой игры в каждом клубе
        foreach ($users as $user) {
            foreach ($games as $game) {
                foreach ($clubs as $club) {
                    // Создаем общий рейтинг (без привязки к сезону)
                    $generalRating = rand(1000, 8000);
                    
                    // Проверяем, существует ли уже общий рейтинг
                    $existingGeneralRating = Rating::where('player_id', $user->id) // Изменено с user_id на player_id
                        ->where('game_id', $game->id)
                        ->where('club_id', $club->id)
                        ->whereNull('season_id')
                        ->first();
                    
                    if ($existingGeneralRating) {
                        $existingGeneralRating->update([
                            'game_rating' => $generalRating,
                        ]);
                    } else {
                        Rating::create([
                            'player_id' => $user->id, // Изменено с user_id на player_id
                            'game_id' => $game->id,
                            'club_id' => $club->id,
                            'game_rating' => $generalRating,
                        ]);
                    }
                    
                    // Создаем сезонный рейтинг
                    $seasonRating = rand(500, 5000);
                    
                    // Проверяем, существует ли уже сезонный рейтинг
                    $existingSeasonRating = Rating::where('player_id', $user->id) // Изменено с user_id на player_id
                        ->where('game_id', $game->id)
                        ->where('club_id', $club->id)
                        ->where('season_id', $currentSeason->id)
                        ->first();
                    
                    if ($existingSeasonRating) {
                        $existingSeasonRating->update([
                            'game_rating' => $seasonRating,
                        ]);
                    } else {
                        Rating::create([
                            'player_id' => $user->id, // Изменено с user_id на player_id
                            'game_id' => $game->id,
                            'club_id' => $club->id,
                            'season_id' => $currentSeason->id,
                            'game_rating' => $seasonRating,
                        ]);
                    }
                }
            }
        }
    }
}


