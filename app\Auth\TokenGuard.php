<?php

namespace App\Auth;

use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use App\Services\TokenService;
use Illuminate\Support\Facades\Log;

class TokenGuard implements Guard
{
    use GuardHelpers;

    protected $request;
    protected $tokenService;

    public function __construct(UserProvider $provider, Request $request, TokenService $tokenService)
    {
        $this->provider = $provider;
        $this->request = $request;
        $this->tokenService = $tokenService;
    }

    public function user()
    {
        if (!is_null($this->user)) {
            return $this->user;
        }

        $token = $this->request->query('auth_token');
        $clubId = $this->request->query('club');

        if ($token && $clubId) {
            $result = $this->tokenService->validateToken($token, (int)$clubId);
            
            if ($result instanceof \App\Models\User) {
                $this->user = $result;
            } else {
                // Логируем конкретную причину неудачи
                Log::warning('Token authentication failed', [
                    'reason' => $result,
                    'token_preview' => substr($token, 0, 8) . '...',
                    'club_id' => $clubId,
                    'ip' => $this->request->ip()
                ]);
            }
        }

        return $this->user;
    }

    public function validate(array $credentials = [])
    {
        return false;
    }
}
