<div>
    <div x-data="{ 
            open: @entangle('open'), // Привязываем состояние open к Livewire свойству
            init() {
                // Дополнительные обработчики, если нужны, для Alpine.js
                this.$watch('open', value => {
                    if (value) {
                        document.body.classList.add('modal-open');
                    } else {
                        document.body.classList.remove('modal-open');
                    }
                });
            }
        }"
        @keydown.escape.window="open = false"
        class="modal fade"
        :class="{ 'show': open }"
        style="display: none;"
        :style="open ? 'display: block;' : 'display: none;'"
        tabindex="-1"
        aria-labelledby="createMatchModalLabel"
        aria-hidden="true"
        wire:ignore.self>
        
        <div class="modal-backdrop fade" 
             :class="{ 'show': open }"
             x-show="open" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="open = false"></div>
             
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createMatchModalLabel">Создать новый матч</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="createMatch">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="gameType" class="form-label">Тип игры</label>
                                <select id="gameType" class="form-select" wire:model="newMatch.game_type" disabled>
                                    <option value="cs2">CS2</option>
                                </select>
                                @error('newMatch.game_type') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="sessionId" class="form-label">ID сессии</label>
                                <input type="text" id="sessionId" class="form-control" wire:model="newMatch.session_id">
                                @error('newMatch.session_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="matchId" class="form-label">ID матча</label>
                                <input type="text" id="matchId" class="form-control" wire:model="newMatch.match_id">
                                @error('newMatch.match_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Бан карт</h5>
                                <p class="text-muted">Выберите до {{ $maxBans }} карт для бана</p>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6>Доступные карты</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @if(count($availableMaps) > 0)
                                                @foreach($availableMaps as $map)
                                                    @if(!in_array($map['id'], $bannedMaps))
                                                        <div class="col-md-4 mb-3">
                                                            <div class="card h-100 position-relative">
                                                                @if(isset($map['image_url']) && $map['image_url'])
                                                                    <img src="{{ asset($map['image_url']) }}" class="card-img-top" alt="{{ $map['name'] }}">
                                                                @else
                                                                    <div class="card-img-top bg-secondary text-white d-flex align-items-center justify-content-center" style="height: 100px;">
                                                                        <span>Нет изображения</span>
                                                                    </div>
                                                                @endif
                                                                <div class="card-body position-absolute bottom-0 start-0 w-100" style="background: rgba(0, 0, 0, 0.7);">
                                                                    <h6 class="card-title text-center text-white mb-2">{{ $map['name'] }}</h6>
                                                                    <button type="button" class="btn btn-danger btn-sm w-100" wire:click="banMap({{ $map['id'] }})">
                                                                        Забанить
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @else
                                                <div class="col-12">
                                                    <div class="alert alert-info">
                                                        Нет доступных карт для бана
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6>Забаненные карты</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @if(count($bannedMaps) > 0)
                                                @foreach($availableMaps as $map)
                                                    @if(in_array($map['id'], $bannedMaps))
                                                        <div class="col-md-4 mb-3">
                                                            <div class="card h-100 position-relative">
                                                                @if(isset($map['image_url']) && $map['image_url'])
                                                                    <img src="{{ asset($map['image_url']) }}" class="card-img-top" alt="{{ $map['name'] }}">
                                                                @else
                                                                    <div class="card-img-top bg-secondary text-white d-flex align-items-center justify-content-center" style="height: 100px;">
                                                                        <span>Нет изображения</span>
                                                                    </div>
                                                                @endif
                                                                <div class="card-body text-center position-absolute bottom-0 start-0 w-100" style="background: rgba(0, 0, 0, 0.7);">
                                                                    <h6 class="card-title text-white mb-2">{{ $map['name'] }}</h6>
                                                                    <button type="button" class="btn btn-success btn-sm w-100" wire:click="unbanMap({{ $map['id'] }})">
                                                                        Разбанить
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @else
                                                <div class="col-12">
                                                    <div class="alert alert-info">
                                                        Нет забаненных карт
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-secondary me-2" @click="open = false">Отмена</button>
                            <button type="submit" class="btn btn-primary">Создать</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>