<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SimpleSeasonsTest extends TestCase
{
    /**
     * Простой тест для проверки работы с сезонами.
     */
    public function test_simple_seasons(): void
    {
        // Создаем необходимые таблицы
        $this->createTestTables();
        
        // Создаем сезон
        $seasonId = DB::table('seasons')->insertGetId([
            'name' => 'Сезон 1',
            'start_date' => now()->subMonth(),
            'end_date' => null,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что сезон создан
        $this->assertDatabaseHas('seasons', [
            'id' => $seasonId,
            'name' => 'Сезон 1',
            'is_active' => 1
        ]);
        
        // Завершаем текущий сезон
        DB::table('seasons')
            ->where('id', $seasonId)
            ->update([
                'is_active' => false,
                'end_date' => now(),
                'updated_at' => now()
            ]);
        
        // Создаем новый сезон
        $newSeasonId = DB::table('seasons')->insertGetId([
            'name' => 'Сезон 2',
            'start_date' => now(),
            'end_date' => null,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что старый сезон завершен
        $this->assertDatabaseHas('seasons', [
            'id' => $seasonId,
            'is_active' => 0
        ]);
        
        // Проверяем, что новый сезон создан
        $this->assertDatabaseHas('seasons', [
            'id' => $newSeasonId,
            'name' => 'Сезон 2',
            'is_active' => 1
        ]);
    }
    
    /**
     * Создает необходимые таблицы для тестов.
     */
    protected function createTestTables()
    {
        // Создаем таблицу seasons, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS seasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                start_date DATETIME NOT NULL,
                end_date DATETIME NULL,
                is_active BOOLEAN NOT NULL DEFAULT 0,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Создаем таблицу ratings, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                game_id INTEGER NOT NULL,
                club_id INTEGER NULL,
                season_id INTEGER NOT NULL,
                game_rating INTEGER NOT NULL DEFAULT 1000,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
    }
}