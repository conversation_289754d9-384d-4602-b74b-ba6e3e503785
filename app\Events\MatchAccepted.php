<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MatchAccepted  implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $matchId;
    public $fromCaptainId;
    public $toCaptainId;

    public function __construct($matchId, $fromCaptainId, $toCaptainId)
    {
        $this->matchId = $matchId;
        $this->fromCaptainId = $fromCaptainId;
        $this->toCaptainId = $toCaptainId;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('captain.' . $this->toCaptainId);
    }

    public function broadcastAs()
    {
        return 'match.accepted';
    }
}
