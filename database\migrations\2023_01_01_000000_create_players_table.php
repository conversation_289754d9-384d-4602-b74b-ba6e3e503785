<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('players', function (Blueprint $table) {
            $table->id();
            $table->string('club_id')->nullable();
            $table->string('client_id')->nullable();
            $table->string('client_nick')->nullable();
            $table->string('email')->nullable();
            $table->string('password')->nullable();
            $table->string('client_password')->nullable();
            $table->string('client_hash')->nullable();
            $table->string('auth_token')->nullable();
            $table->dateTime('auth_token_from_date')->nullable();
            $table->string('client_status')->default('web');
            $table->boolean('change_password')->default(false);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('players');
    }
};