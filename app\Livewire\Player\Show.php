<?php

namespace App\Livewire\Player;

use Livewire\Component;
use App\Models\User;
use App\Models\FullRating;
use App\Models\Season;
use Livewire\Attributes\Title;

#[Title('Профиль игрока')]
class Show extends Component
{
    public User $player;
    
    public function mount(User $player)
    {
        $this->player = $player;
    }
    
    public function render()
    {
        // Получаем текущий рейтинг игрока по всем играм
        $currentRatings = FullRating::with(['game', 'club'])
            ->where('player_id', $this->player->id)
            ->orderBy('game_rating', 'desc')
            ->get();
            
        // Получаем историю рейтинга в сезонах
        $seasonRatings = Season::with(['event'])
            ->where('player_id', $this->player->id)
            ->orderBy('rating', 'desc')
            ->get();
            
        return view('livewire.player.show', [
            'currentRatings' => $currentRatings,
            'seasonRatings' => $seasonRatings,
        ]);
    }
} 