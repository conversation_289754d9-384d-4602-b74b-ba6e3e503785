<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TeamInvitation;
use App\Models\Team;
use App\Models\User;

class CheckTeamData extends Command
{
    protected $signature = 'team:check-data';
    protected $description = 'Проверить данные команд и приглашений';

    public function handle()
    {
        $this->info('=== Проверка данных команд и приглашений ===');
        
        // Проверяем приглашения
        $pendingInvitations = TeamInvitation::where('status', 'pending')->get();
        $this->info("Активных приглашений: " . $pendingInvitations->count());
        
        foreach ($pendingInvitations as $invitation) {
            $team = Team::find($invitation->team_id);
            $user = User::find($invitation->player_id);
            
            $this->line("ID: {$invitation->id} | Команда: " . ($team ? $team->name : 'НЕ НАЙДЕНА') . 
                      " | Игрок: " . ($user ? $user->client_nick : 'НЕ НАЙДЕН'));
        }
        
        // Проверяем команды с приглашениями
        $this->info("\n=== Команды с активными приглашениями ===");
        $teams = Team::whereHas('invitations', function($q) {
            $q->where('status', 'pending');
        })->with(['invitations' => function($q) {
            $q->where('status', 'pending')->with('user');
        }])->get();
        
        foreach ($teams as $team) {
            $this->info("Команда: {$team->name} (ID: {$team->id})");
            foreach ($team->invitations as $invitation) {
                $this->line("  - Приглашение ID: {$invitation->id} | Игрок: " . 
                          ($invitation->user ? $invitation->user->client_nick : 'НЕ НАЙДЕН'));
            }
        }
        
        // Проверяем пользователей с null client_nick
        $this->info("\n=== Пользователи с проблемными именами ===");
        $problematicUsers = User::whereNull('client_nick')
            ->orWhere('client_nick', '')
            ->limit(10)
            ->get();
            
        $this->info("Пользователей с пустыми именами: " . $problematicUsers->count());
        foreach ($problematicUsers as $user) {
            $this->line("ID: {$user->id} | client_nick: " . ($user->client_nick ?: 'NULL'));
        }
        
        return 0;
    }
}
