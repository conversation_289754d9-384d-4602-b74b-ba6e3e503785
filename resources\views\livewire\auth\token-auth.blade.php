<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header text-center">
                    <h4 class="mb-0">Авторизация по токену</h4>
                </div>
                <div class="card-body text-center">
                    @if($status === 'checking' || $isProcessing)
                        <div class="mb-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Загрузка...</span>
                            </div>
                        </div>
                        <h5>Проверяем токен авторизации...</h5>
                        <p class="text-muted">Пожалуйста, подождите</p>
                        
                        @if($token && $clubId)
                            <div class="mt-3">
                                <small class="text-muted">
                                    Токен: {{ substr($token, 0, 8) }}...<br>
                                    Клуб ID: {{ $clubId }}
                                </small>
                            </div>
                        @endif

                    @elseif($status === 'success')
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-success">Авторизация успешна!</h5>
                        <p class="text-muted">Перенаправляем вас в систему...</p>

                    @elseif($status === 'error')
                        <div class="mb-4">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-warning">Отсутствуют параметры авторизации</h5>
                        <p class="text-muted">Ссылка для авторизации некорректна</p>
                        
                        <div class="mt-4">
                            <a href="{{ route('login') }}" class="btn btn-primary" wire:navigate>
                                Войти через логин/пароль
                            </a>
                            <a href="{{ route('home') }}" class="btn btn-secondary" wire:navigate>
                                На главную
                            </a>
                        </div>
                    @endif

                    @if($token && $clubId && $status !== 'success')
                        <div class="mt-4">
                            <button wire:click="authenticate" 
                                    class="btn btn-outline-primary"
                                    wire:loading.attr="disabled">
                                <span wire:loading.remove>Повторить попытку</span>
                                <span wire:loading>Проверяем...</span>
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('redirect-to-team', () => {
                setTimeout(() => {
                    window.location.href = '{{ route("team") }}';
                }, 1500);
            });
        });
    </script>
</div>