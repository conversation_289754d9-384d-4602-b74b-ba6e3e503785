<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int|null $event_id
 * @property int|null $player_id
 * @property int|null $rating
 * @property-read \App\Models\Event|null $event
 * @property-read \App\Models\User|null $player
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Season whereRating($value)
 * @mixin \Eloquent
 */
class Season extends Model
{
    use HasFactory;
    protected $table = 'events_season_rating';
    
    protected $fillable = [
        'event_id',
        'start_date',
        'player_id',
        'rating'
    ];

    public function event():BelongsTo
    {
        return $this->belongsTo(Event::class, 'event_id');
    }
    
    public function player():BelongsTo
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}