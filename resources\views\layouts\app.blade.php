<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    @livewireStyles
</head>
<body>
    @livewireScripts
    @include('layouts.header')

    <main class="py-4">
        @yield('content')
    </main>

    @include('layouts.footer')
    <div x-data="{
        message: '',
        type: '',
        show: false,
        init() {
            Livewire.on('showNotification', ({ message, type }) => {
                this.message = message;
                this.type = type;
                this.show = true;
                setTimeout(() => this.show = false, 5000);
            });
        }
    }" x-bind:style="show ? 'display: block;' : 'display: none;'" class="alert" :class="{ 'alert-success': type === 'success', 'alert-danger': type === 'error' }" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
        <span x-text="message"></span>
    </div>
</body>
</html>