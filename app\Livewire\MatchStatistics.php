<?php

namespace App\Livewire;

use App\Models\Dota2;
use Livewire\Component;
use App\Models\GameMatch;
use App\Models\MatchResult;
use App\Models\Pubg;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MatchStatistics extends Component
{
    public $totalMatches = 0;
    public $victories = 0;
    public $defeats = 0;
    public $winRate = 0;
    public $gameStats = [];
    
    public function mount()
    {
        $this->loadStatistics();
    }
    
    public function loadStatisticsOld()
    {
        $userId = Auth::id();
        
        // Общее количество матчей
        $this->totalMatches = GameMatch::where('player_id', $userId)->count() + 
        Dota2::where('player_id', $userId)->count() + Pubg::where('player_id', $userId)->count();
        
        // Победы и поражения
        $results = MatchResult::where('player_id', $userId)
            ->select(
                DB::raw('SUM(CASE WHEN victory = 1 THEN 1 ELSE 0 END) as victories'),
                DB::raw('SUM(CASE WHEN victory = 0 THEN 1 ELSE 0 END) as defeats')
            )
            ->first();
            
        $this->victories = $results->victories ?? 0;
        $this->defeats = $results->defeats ?? 0;
        
        // Процент побед
        if ($this->totalMatches > 0) {
            $this->winRate = round(($this->victories / $this->totalMatches) * 100, 1);
        }
        
        // Статистика по играм
        $gameTypes = GameMatch::where('player_id', $userId)
            ->select('game_type')
            ->distinct()
            ->pluck('game_type');
            
        foreach ($gameTypes as $gameType) {
            $totalByGame = GameMatch::where('player_id', $userId)
                ->where('game_type', $gameType)
                ->count();
                
            $victoriesByGame = MatchResult::join('game_matches', 'match_results.match_id', '=', 'game_matches.id')
                ->where('match_results.player_id', $userId)
                ->where('game_matches.game_type', $gameType)
                ->where('match_results.victory', 1)
                ->count();
                
            $winRateByGame = $totalByGame > 0 ? round(($victoriesByGame / $totalByGame) * 100, 1) : 0;
            
            $this->gameStats[] = [
                'game_type' => $gameType,
                'total' => $totalByGame,
                'victories' => $victoriesByGame,
                'defeats' => $totalByGame - $victoriesByGame,
                'win_rate' => $winRateByGame
            ];
        }
    }

    public function loadStatistics()
    {
        $userId = Auth::id();

        // Общее количество матчей
        $this->totalMatches = GameMatch::where('player_id', $userId)->count() +
                                Dota2::where('player_id', $userId)->count() +
                                Pubg::where('player_id', $userId)->count();

        // Победы и поражения
        $totalVictories = 0;
        $totalDefeats = 0;

        // Подсчет побед и поражений из таблицы matches
        $resultsGeneral = GameMatch::where('player_id', $userId)
            ->select(
                DB::raw('SUM(CASE WHEN victory = 1 THEN 1 ELSE 0 END) as victories'),
                DB::raw('SUM(CASE WHEN victory = 0 THEN 1 ELSE 0 END) as defeats')
            )
            ->first();

        $totalVictories += $resultsGeneral->victories ?? 0;
        $totalDefeats += $resultsGeneral->defeats ?? 0;

        // Подсчет побед и поражений из таблицы matches_dota2
        $resultsDota2 = Dota2::where('player_id', $userId)
            ->select(
                DB::raw('SUM(CASE WHEN victory = 1 THEN 1 ELSE 0 END) as victories'),
                DB::raw('SUM(CASE WHEN victory = 0 THEN 1 ELSE 0 END) as defeats')
            )
            ->first();

        $totalVictories += $resultsDota2->victories ?? 0;
        $totalDefeats += $resultsDota2->defeats ?? 0;

        // Подсчет побед и поражений из таблицы matches_pubg
        $resultsPubg = Pubg::where('player_id', $userId)
            ->select(
                DB::raw('SUM(CASE WHEN victory = 1 THEN 1 ELSE 0 END) as victories'),
                DB::raw('SUM(CASE WHEN victory = 0 THEN 1 ELSE 0 END) as defeats')
            )
            ->first();

        $totalVictories += $resultsPubg->victories ?? 0;
        $totalDefeats += $resultsPubg->defeats ?? 0;

        $this->victories = $totalVictories;
        $this->defeats = $totalDefeats;

        // Процент побед
        if ($this->totalMatches > 0) {
            $this->winRate = round(($this->victories / $this->totalMatches) * 100, 1);
        } else {
            $this->winRate = 0;
        }

        // Статистика по играм (теперь нужно определять тип игры по таблице)
        $this->gameStats = [];

        // Статистика по общим матчам
        $totalGeneral = GameMatch::where('player_id', $userId)->count();
        $victoriesGeneral = GameMatch::where('player_id', $userId)->where('victory', 1)->count();
        $winRateGeneral = $totalGeneral > 0 ? round(($victoriesGeneral / $totalGeneral) * 100, 1) : 0;
        $this->gameStats[] = [
            'game_type' => 'CS2', // Или какое-то другое название для общих матчей
            'total' => $totalGeneral,
            'victories' => $victoriesGeneral,
            'defeats' => $totalGeneral - $victoriesGeneral,
            'win_rate' => $winRateGeneral,
        ];

        // Статистика по Dota 2
        $totalDota2 = Dota2::where('player_id', $userId)->count();
        $victoriesDota2 = Dota2::where('player_id', $userId)->where('victory', 1)->count();
        $winRateDota2 = $totalDota2 > 0 ? round(($victoriesDota2 / $totalDota2) * 100, 1) : 0;
        $this->gameStats[] = [
            'game_type' => 'Dota 2',
            'total' => $totalDota2,
            'victories' => $victoriesDota2,
            'defeats' => $totalDota2 - $victoriesDota2,
            'win_rate' => $winRateDota2,
        ];

        // Статистика по PUBG
        $totalPubg = Pubg::where('player_id', $userId)->count();
        $victoriesPubg = Pubg::where('player_id', $userId)->where('victory', 1)->count();
        $winRatePubg = $totalPubg > 0 ? round(($victoriesPubg / $totalPubg) * 100, 1) : 0;
        $this->gameStats[] = [
            'game_type' => 'PUBG',
            'total' => $totalPubg,
            'victories' => $victoriesPubg,
            'defeats' => $totalPubg - $victoriesPubg,
            'win_rate' => $winRatePubg,
        ];
    }
    
    public function render()
    {
        return view('livewire.match-statistics');
    }
}