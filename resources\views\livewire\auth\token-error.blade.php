<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Ошибка авторизации по токену
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger mt-4 text-center">
                        <strong>Можете обратиться в 
                            <a href="https://rentalgames.ru/">поддержу</a>                            
                            на сайте.</strong>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Детали ошибки:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Тип ошибки:</strong> {{ $errorType }}</li>
                                <li><strong>Клуб ID:</strong> {{ $clubId }}</li>
                                @if($token)
                                    <li><strong>Токен:</strong> {{ substr($token, 0, 8) }}...</li>
                                @endif
                                @if($tokenDate)
                                    <li><strong>Срок действия до:</strong> 
                                        {{ \Carbon\Carbon::parse($tokenDate)->format('d.m.Y H:i') }}
                                        <small class="text-muted">
                                            ({{ \Carbon\Carbon::parse($tokenDate)->locale('ru')->diffForHumans() }})
                                        </small>
                                    </li>
                                @endif
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Возможные причины:</h6>
                            <ul class="small text-muted">
                                @if($errorType === 'invalid_token')
                                    <li>Токен не найден в системе</li>
                                    <li>Возможно, токен был отозван</li>
                                    <li>Проверьте правильность ссылки</li>
                                @elseif($errorType === 'invalid_club')
                                    <li>Токен создан для другого клуба</li>
                                    <li>Проверьте правильность ссылки</li>
                                    @if($tokenDate)
                                        <li class="text-info">
                                            <strong>Токен действителен до {{ \Carbon\Carbon::parse($tokenDate)->format('d.m.Y H:i') }}</strong>
                                        </li>
                                    @endif
                                @elseif($errorType === 'token_expired')
                                    <li>Срок действия токена истек</li>
                                    <li>Обратитесь к администратору для получения нового токена</li>
                                    @if($tokenDate)
                                        <li class="text-danger">
                                            <strong>Токен истек {{ \Carbon\Carbon::parse($tokenDate)->locale('ru')->diffForHumans() }}</strong>
                                        </li>
                                    @endif
                                @else
                                    <li>Временная техническая ошибка</li>
                                    <li>Попробуйте повторить через несколько минут</li>
                                    @if($tokenDate)
                                        <li class="text-info">
                                            Токен действителен до {{ \Carbon\Carbon::parse($tokenDate)->format('d.m.Y H:i') }}
                                        </li>
                                    @endif
                                @endif
                            </ul>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

