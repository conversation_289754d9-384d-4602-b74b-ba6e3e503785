{{-- resources/views/livewire/create-team.blade.php --}}
<div>
    @section('title', 'Создание команды')
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-body">
                        <a href="{{ route('team') }}" class="btn btn-sm btn-outline-primary" wire:navigate="">
                            <i class="ri-arrow-left-line me-1"></i> Вернуться
                        </a>                        

                        <livewire:carousel />

                        @include('livewire.notification')

                        {{-- Включаем компонент списка команд --}}
                        <livewire:team-list :gameId="$selectedGameId" wire:key="team-list-{{ $selectedGameId }}" />

                        <script>
                            window.Laravel = window.Laravel || {};
                            window.Laravel.userId = {{ auth()->id() ?? 'null' }};
                        </script>
                        {{-- Включаем компонент списка полученных приглашений --}}
                        <livewire:received-invitations-list wire:key="received-invitations-list-{{ now()->timestamp }}" />
                        
                        {{-- Включаем компонент списка отправленных приглашений --}}
                        <livewire:team.sent-invitations-list wire:key="sent-invitations-list-{{ now()->timestamp }}" />

                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Модальные окна, которые не привязаны к конкретному списку команд/приглашений --}}
    <livewire:modals.create-team-modal :selectedGameId="$selectedGameId" wire:key="create-team-modal" />
    <livewire:modals.invite-player-modal />
    <livewire:modals.create-match-modal />

    <script>
        document.addEventListener('livewire:initialized', () => {
            // Обработчики для закрытия модальных окон (если они вызываются из разных мест)
            Livewire.on('closeInviteModal', () => {
                console.log('🔄 Событие closeInviteModal получено');
                window.dispatchEvent(new CustomEvent('closeInviteModal'));
            });

            Livewire.on('closeCreateTeamModal', () => {
                console.log('🔄 Событие closeCreateTeamModal получено');
                window.dispatchEvent(new CustomEvent('closeCreateTeamModal'));
            });

            Livewire.on('showNotification', (data) => {
                console.log('📢 Событие showNotification получено:', data);
                window.dispatchEvent(new CustomEvent('showNotification', { detail: data }));
            });

            Livewire.on('refreshTeamList', (data) => {
                console.log('🔄 Событие refreshTeamList получено в браузере:', data);
            });

            Livewire.on('refreshReceivedInvitations', (data) => {
                console.log('🔄 Событие refreshReceivedInvitations получено в браузере:', data);
            });
        });
    </script>
</div>
