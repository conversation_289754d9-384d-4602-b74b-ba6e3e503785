<?php

namespace App\Services\Interfaces;

use Illuminate\Support\Collection;

interface RequestManagerInterface
{
    /**
     * Принимает заявку на вступление в команду.
     */
    public function acceptRequest(int $requestId, int $captainId): bool;

    /**
     * Отклоняет заявку на вступление в команду.
     */
    public function rejectRequest(int $requestId, int $captainId): bool;

    /**
     * Получает список ожидающих заявок на вступление для команды.
     */
    public function getPendingRequestsForTeam(int $teamId): Collection;
}