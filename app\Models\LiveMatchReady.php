<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $match_id
 * @property int $player_id
 * @property bool $is_ready
 * @property-read \App\Models\LiveMatch|null $match
 * @property-read \App\Models\User|null $player
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchReady newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchReady newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchReady query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchReady whereIsReady($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchReady whereMatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchReady wherePlayerId($value)
 * @mixin \Eloquent
 */
class LiveMatchReady extends Model
{
    protected $table = 'live_match_ready';
    
    // Отключаем timestamps, так как их нет в таблице
    public $timestamps = false;
    
    // Убираем составной первичный ключ, так как Laravel его не поддерживает
    // protected $primaryKey = ['match_id', 'player_id'];
    // public $incrementing = false;
    
    protected $fillable = [
        'match_id',
        'player_id',
        'is_ready'
    ];
    
    protected $casts = [
        'is_ready' => 'boolean'
    ];
    
    // Связь с матчем
    public function match()
    {
        return $this->belongsTo(LiveMatch::class, 'match_id');
    }
    
    // Связь с игроком
    public function player()
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}
