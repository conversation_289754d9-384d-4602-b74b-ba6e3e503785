<?php

namespace App\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\ServiceProvider;

class ScheduleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->app->booted(function () {
             // $schedule = $this->app->make(Schedule::class);

            // Запуск обработки результатов матчей Dota 2 каждые 5 минут
            // $schedule->command('matches:process dota2')->everyFiveMinutes();

            // Запуск обработки результатов матчей PUBG каждые 5 минут
            // $schedule->command('matches:process pubg')->everyFiveMinutes();

            // Пример запуска команды Artisan каждый день в определенное время
            // $schedule->command('emails:send')->dailyAt('8:00');

            // Пример запуска Closure каждый час
            // $schedule->call(function () {
            //     \Illuminate\Support\Facades\Log::info('Ежечасная задача выполнена.');
            // })->hourly();
        });

        $this->callAfterResolving(Schedule::class, function (Schedule $schedule) {
           
            
            // Проверка статуса сезонов каждый день в полночь
            $schedule->command('season:check')->dailyAt('00:00');
        });
    }
}


