<?php

namespace App\Livewire\Auth;

use Livewire\Component;
use App\Services\TokenService;
use Illuminate\Support\Facades\Log;

class TokenError extends Component
{
    public string $errorType = '';
    public string $errorMessage = '';
    public string $token = '';
    public int $clubId = 0;
    public bool $canRetry = false;
    public ?string $tokenDate = null;

    protected TokenService $tokenService;

    public function boot(TokenService $tokenService)
    {
        $this->tokenService = $tokenService;
    }

    public function mount($errorType = '', $token = '', $clubId = 0)
    {
        // Если параметры не переданы напрямую, берем из request
        if (empty($errorType)) {
            $errorType = request()->get('errorType', '');
        }
        if (empty($token)) {
            $token = request()->get('token', '');
        }
        if (empty($clubId)) {
            $clubId = (int)request()->get('clubId', 0);
        }

        $this->errorType = $errorType;
        $this->token = $token;
        $this->clubId = $clubId;
        
        // Получаем информацию о токене из БД для отображения даты
        $this->getTokenInfo();
        
        Log::info('TokenError component mounted', [
            'errorType' => $this->errorType,
            'token_preview' => $this->token ? substr($this->token, 0, 8) . '...' : null,
            'clubId' => $this->clubId,
            'token_date' => $this->tokenDate
        ]);
        
        $this->errorMessage = match($errorType) {
            'invalid_token' => 'Неверный токен авторизации',
            'invalid_club' => 'Токен не действителен для данного клуба',
            'token_expired' => 'Срок действия токена истек',
            'token_mismatch' => 'Токен не совпадает',
            'user_not_found' => 'Пользователь не найден',
            'exception' => 'Ошибка при проверке токена',
            default => 'Ошибка авторизации'
        };

        // Можно повторить только при технических ошибках
        $this->canRetry = in_array($errorType, ['exception', 'token_mismatch']);
    }


    public function render()
    {
        return view('livewire.auth.token-error');
    }

    private function getTokenInfo()
    {
        try {
            if (!empty($this->token)) {
                // Ищем пользователя по токену для получения даты окончания
                $user = \App\Models\User::where('auth_token', $this->token)->first();
                
                if ($user && $user->auth_token_from_date) {
                    $this->tokenDate = $user->auth_token_from_date;
                    
                    Log::debug('Token info found', [
                        'user_id' => $user->id,
                        'token_date' => $this->tokenDate,
                        'user_club' => $user->club_id
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get token info for error page', [
                'error' => $e->getMessage(),
                'token_preview' => substr($this->token, 0, 8) . '...'
            ]);
        }
    }
}

