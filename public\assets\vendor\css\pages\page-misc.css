.misc-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  justify-content: center;
  position: relative;
  padding: 1.25rem;
}

.misc-bg {
  inline-size: 100%;
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
}

.misc-object,
.misc-object-right {
  position: absolute;
  z-index: 1;
}

.misc-object {
  bottom: 6%;
  left: 3%;
}

.misc-object-right {
  bottom: 7%;
  right: 3%;
}

.misc-model {
  position: relative;
  bottom: 3rem;
}
