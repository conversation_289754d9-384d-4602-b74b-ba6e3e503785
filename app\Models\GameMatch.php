<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int|null $club_id
 * @property int|null $player_id
 * @property \Illuminate\Support\Carbon|null $date
 * @property string|null $log
 * @property string|null $log_gameover
 * @property int|null $victory
 * @property int|null $score
 * @property string|null $math_score
 * @property int|null $current_voting_team
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Club|null $club
 * @property-read \App\Models\MatchResult|null $matchResult
 * @property-read \App\Models\User|null $player
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereCurrentVotingTeam($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereLog($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereLogGameover($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereMathScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GameMatch whereVictory($value)
 * @mixin \Eloquent
 */
class GameMatch extends Model
{
    use HasFactory;
    protected $table = 'matches';

    /**
     * Атрибуты, которые можно массово назначать.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'club_id',
        'player_id',
        'date',
        'log',
        'log_gameover',
        'victory',
        'score',
        'math_score',
        'current_voting_team',
        'game_type',
    ];

    /**
     * Атрибуты, которые должны быть приведены к определенным типам.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'datetime',
        'victory' => 'integer',
        'score' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'current_voting_team' => 'integer',        
    ];

    /**
     * Получить игрока, связанного с матчем.
     */
    public function player(): BelongsTo
    {
        return $this->belongsTo(User::class, 'player_id');
    }

    /**
     * Получить клуб, связанный с матчем.
     */
    public function club(): BelongsTo
    {
        return $this->belongsTo(Club::class, 'club_id');
    }

    /**
     * Получить результат матча, связанный с матчем.
     */
    public function matchResult(): HasOne
    {
        return $this->hasOne(MatchResult::class, 'match_id');
    }
}


