<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_matches', function (Blueprint $table) {
            $table->id();
            $table->string('game_type');
            $table->string('session_id');
            $table->string('match_id')->nullable();
            $table->integer('score')->nullable();
            $table->string('match_score', 45)->nullable();
            $table->datetime('date_scan')->nullable();
            $table->integer('col_scan')->default(0);
            $table->foreignId('user_id')->constrained('players');
            $table->timestamps();

            // Добавляем индексы для оптимизации запросов
            $table->index('game_type');
            $table->index('date_scan');
            $table->index(['user_id', 'game_type']);
            $table->index(['created_at', 'game_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_matches');
    }
};



