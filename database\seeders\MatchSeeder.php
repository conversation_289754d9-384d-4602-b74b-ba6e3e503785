<?php

namespace Database\Seeders;

use App\Models\GameMatch;
use App\Models\MatchResult;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class MatchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Получаем пользователя или создаем нового, если нет ни одного
        $user = User::first() ?? User::factory()->create();
        
        // Создаем тестовые матчи Dota 2
        for ($i = 1; $i <= 5; $i++) {
            $match = GameMatch::create([
                'club_id' => 0,
                'player_id' => $user->id,
                'date' => Carbon::now()->subDays(rand(1, 30)),
                'log' => json_encode(['game' => 'Dota 2', 'match_id' => '1000' . $i]),
                'log_gameover' => json_encode(['game' => 'Dota 2', 'match_id' => '1000' . $i, 'score' => rand(10, 30) . ':' . rand(10, 30)]),
                'victory' => rand(0, 1),
                'score' => rand(50, 100),
                'math_score' => rand(10, 30) . ':' . rand(10, 30),
                'current_voting_team' => null,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
            
            // Создаем результат матча
            MatchResult::create([
                'match_id' => $match->id,
                'player_id' => $user->id,
                'victory' => rand(0, 1),
                'round' => 1,
                'add_score' => $match->score,
                'details' => json_encode(['test' => 'data']),
            ]);
        }
        
        // Создаем тестовые матчи PUBG
        for ($i = 1; $i <= 5; $i++) {
            $match = GameMatch::create([
                'club_id' => 0,
                'player_id' => $user->id,
                'date' => Carbon::now()->subDays(rand(1, 30)),
                'log' => json_encode(['game' => 'PUBG', 'match_id' => '2000' . $i]),
                'log_gameover' => json_encode(['game' => 'PUBG', 'match_id' => '2000' . $i, 'place' => rand(1, 20)]),
                'victory' => rand(0, 1),
                'score' => rand(40, 90),
                'math_score' => 'Place: ' . rand(1, 20),
                'current_voting_team' => null,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
            
            // Создаем результат матча
            MatchResult::create([
                'match_id' => $match->id,
                'player_id' => $user->id,
                'victory' => $match->score > 70 ? 1 : 0, // Победа, если счет больше 70
                'round' => 1,
                'add_score' => $match->score,
                'details' => json_encode(['test' => 'data']),
            ]);
        }
        
        // Создаем тестовые матчи CS2
        for ($i = 1; $i <= 5; $i++) {
            $logData = '{"Auth":{},"Provider":{"Name":"Counter-Strike: Global Offensive","AppID":730,"Version":14029,"SteamID":"76561198029495790"},"Map":{"Mode":2,"Name":"de_anubis","Phase":1,"Round":0,"CTStatistics":{"Score":0,"Name":"","Flag":"","ConsecutiveRoundLosses":1,"RemainingTimeouts":3,"MatchesWonThisSeries":3},"TStatistics":{"Score":0,"Name":"","Flag":"","ConsecutiveRoundLosses":1,"RemainingTimeouts":3,"MatchesWonThisSeries":3},"NumberOfMatchesToWinSeries":0,"RoundWins":{}},"Round":{"Phase":0,"BombState":-1,"WinningTeam":-1},"Player":{"SteamID":"76561198029495790","Name":"НачальникГолубей","XPOverloadLevel":-1,"Clan":"","ObserverSlot":4,"Team":2,"Activity":0,"State":{"Health":100,"Armor":0,"HasHelmet":false,"FlashAmount":0,"SmokedAmount":0,"BurningAmount":0,"Money":800,"RoundKills":0,"RoundHSKills":0,"RoundTotalDamage":-1,"EquipmentValue":200,"HasDefuseKit":false},"Weapons":[],"MatchStats":{"Kills":0,"Assists":0,"Deaths":0,"MVPs":0,"Score":0},"SpectationTarget":"","Position":{"X":0.0,"Y":0.0,"Z":0.0},"ForwardDirection":{"X":0.0,"Y":0.0,"Z":0.0}},"PhaseCountdowns":null,"AllPlayers":null,"AllGrenades":null,"Bomb":null,"TournamentDraft":null}';
            $logGameoverData = '{"Auth":{},"Provider":{"Name":"Counter-Strike: Global Offensive","AppID":730,"Version":14029,"SteamID":"76561198029495790"},"Map":{"Mode":2,"Name":"de_anubis","Phase":6,"Round":20,"CTStatistics":{"Score":7,"Name":"","Flag":"","ConsecutiveRoundLosses":5,"RemainingTimeouts":3,"MatchesWonThisSeries":3},"TStatistics":{"Score":13,"Name":"","Flag":"","ConsecutiveRoundLosses":0,"RemainingTimeouts":3,"MatchesWonThisSeries":3},"NumberOfMatchesToWinSeries":0,"RoundWins":{"1":3,"2":4,"3":1,"4":3,"5":3,"6":0,"7":0,"8":3,"9":1,"10":3,"11":3,"12":0,"13":3,"14":0,"15":3,"16":1,"17":0,"18":0,"19":1,"20":0}},"Round":{"Phase":0,"BombState":-1,"WinningTeam":1},"Player":{"SteamID":"76561198029495790","Name":"НачальникГолубей","XPOverloadLevel":-1,"Clan":"","ObserverSlot":9,"Team":1,"Activity":0,"State":{"Health":100,"Armor":100,"HasHelmet":true,"FlashAmount":0,"SmokedAmount":0,"BurningAmount":0,"Money":9050,"RoundKills":3,"RoundHSKills":0,"RoundTotalDamage":-1,"EquipmentValue":4800,"HasDefuseKit":false},"Weapons":[],"MatchStats":{"Kills":23,"Assists":5,"Deaths":10,"MVPs":6,"Score":56},"SpectationTarget":"","Position":{"X":0.0,"Y":0.0,"Z":0.0},"ForwardDirection":{"X":0.0,"Y":0.0,"Z":0.0}},"PhaseCountdowns":null,"AllPlayers":null,"AllGrenades":null,"Bomb":null,"TournamentDraft":null}';

            $match = GameMatch::create([
                'club_id' => 0,
                'player_id' => $user->id,
                'date' => Carbon::now()->subDays(rand(1, 30)),
                'log' => $logData,
                'log_gameover' => $logGameoverData,
                'victory' => rand(0, 1),
                'score' => rand(45, 95),
                'math_score' => rand(5, 16) . ':' . rand(0, 15),
                'current_voting_team' => null,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
            
            // Создаем результат матча с деталями, специфичными для CS2
            MatchResult::create([
                'match_id' => $match->id,
                'player_id' => $user->id,
                'victory' => rand(0, 1),
                'round' => rand(16, 30), // Общее количество раундов
                'add_score' => $match->score,
                'details' => json_encode([
                    'kills' => rand(5, 30),
                    'deaths' => rand(5, 20),
                    'assists' => rand(2, 15),
                    'headshots' => rand(3, 15),
                    'rounds_won' => rand(5, 16),
                    'map' => ['de_dust2', 'de_mirage', 'de_inferno', 'de_nuke', 'de_overpass'][rand(0, 4)],
                ]),
            ]);
        }
    }
}
