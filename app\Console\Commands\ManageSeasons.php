<?php

namespace App\Console\Commands;

use App\Models\Season;
use App\Services\RatingService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ManageSeasons extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seasons:manage {action=status : Action to perform (status, create, end)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage rating seasons';

    /**
     * Execute the console command.
     */
    public function handle(RatingService $ratingService)
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->showStatus();
                break;
            case 'create':
                $this->createSeason();
                break;
            case 'end':
                $this->endCurrentSeason($ratingService);
                break;
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }

        return 0;
    }

    /**
     * Show current season status
     */
    protected function showStatus()
    {
        $currentSeason = Season::where('is_active', true)->first();
        
        if (!$currentSeason) {
            $this->warn('No active season found.');
            return;
        }
        
        $this->info("Current active season: {$currentSeason->name}");
        $this->info("Started: {$currentSeason->start_date}");
        $this->info("Ends: " . ($currentSeason->end_date ?? 'Not set'));
        
        // Показать статистику по сезону
        $ratingsCount = $currentSeason->ratings()->count();
        $this->info("Total ratings in this season: {$ratingsCount}");
    }

    /**
     * Create a new season
     */
    protected function createSeason()
    {
        // Проверяем, есть ли активный сезон
        $currentSeason = Season::where('is_active', true)->first();
        
        if ($currentSeason) {
            if (!$this->confirm('There is already an active season. Do you want to end it and create a new one?')) {
                return;
            }
            
            // Завершаем текущий сезон
            $currentSeason->is_active = false;
            $currentSeason->end_date = now();
            $currentSeason->save();
        }
        
        // Запрашиваем имя нового сезона
        $seasonName = $this->ask('Enter the name for the new season', 'Season ' . (Season::count() + 1));
        
        // Создаем новый сезон
        $newSeason = Season::create([
            'name' => $seasonName,
            'start_date' => now(),
            'is_active' => true,
        ]);
        
        $this->info("New season '{$newSeason->name}' created successfully!");
        
        // Если был предыдущий сезон, предлагаем перенести рейтинги
        if ($currentSeason && $this->confirm('Do you want to initialize new season ratings based on previous season?')) {
            app(RatingService::class)->resetSeasonRatings($newSeason->id);
            $this->info('Ratings initialized for the new season.');
        }
    }

    /**
     * End the current season
     */
    protected function endCurrentSeason(RatingService $ratingService)
    {
        $currentSeason = Season::where('is_active', true)->first();
        
        if (!$currentSeason) {
            $this->error('No active season found to end.');
            return;
        }
        
        if (!$this->confirm("Are you sure you want to end the current season '{$currentSeason->name}'?")) {
            return;
        }
        
        // Завершаем текущий сезон
        $currentSeason->is_active = false;
        $currentSeason->end_date = now();
        $currentSeason->save();
        
        $this->info("Season '{$currentSeason->name}' has been ended.");
        
        // Предлагаем создать новый сезон
        if ($this->confirm('Do you want to create a new season now?')) {
            $seasonName = $this->ask('Enter the name for the new season', 'Season ' . (Season::count() + 1));
            
            $newSeason = Season::create([
                'name' => $seasonName,
                'start_date' => now(),
                'is_active' => true,
            ]);
            
            $this->info("New season '{$newSeason->name}' created successfully!");
            
            // Предлагаем перенести рейтинги
            if ($this->confirm('Do you want to initialize new season ratings based on previous season?')) {
                $ratingService->resetSeasonRatings($newSeason->id);
                $this->info('Ratings initialized for the new season.');
            }
        }
    }
}