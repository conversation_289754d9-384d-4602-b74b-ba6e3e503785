<div>
    <div class="container py-4">
        @include('livewire.notification')
    </div>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Вход</div>

                <div class="card-body">
                    <form wire:submit="userLogin">
                        @csrf
                        <div class="mb-3">
                            <label for="login" class="form-label">Имя пользователя или Email</label>
                            <input wire:model="login" type="text" class="form-control @error('login') is-invalid @enderror" id="login">
                            @error('login') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Пароль</label>
                            <input wire:model="password" type="password" class="form-control @error('password') is-invalid @enderror" id="password">
                            @error('password') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3 form-check">
                            <input wire:model="remember" type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">Запомнить меня</label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <button type="submit" class="btn btn-primary">Войти</button>
                            <a href="{{ route('password.request') }}" wire:navigate>Забыли пароль?</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


