DELIMITER $$

DROP PROCEDURE IF EXISTS `upgrade_teams_table`$$

CREATE PROCEDURE `upgrade_teams_table`()
BEGIN
    -- Добавление поля created_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'teams'
          AND COLUMN_NAME = 'created_at'
    ) THEN
        ALTER TABLE rgtournament.teams
        ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
    END IF;

    -- Добавление поля updated_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'teams'
          AND COLUMN_NAME = 'updated_at'
    ) THEN
        ALTER TABLE rgtournament.teams
        ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL upgrade_teams_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `upgrade_teams_table`;