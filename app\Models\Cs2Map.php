<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $name
 * @property string|null $image_url
 * @property bool|null $is_active
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LiveMatchBannedMap> $bans
 * @property-read int|null $bans_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Cs2Map whereName($value)
 * @mixin \Eloquent
 */
class Cs2Map extends Model
{
    protected $table = 'cs2_maps';
    
    protected $fillable = [
        'name',
        'image_url',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public $timestamps = false;

    /**
     * Связь с банами карт
     */
    public function bans()
    {
        return $this->hasMany(LiveMatchBannedMap::class, 'map_id');
    }
}