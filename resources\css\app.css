@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Стили для модального окна с Alpine.js */
.modal.show {
    display: block;
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 1050;
}

.modal-backdrop.show {
    opacity: 0.5;
    z-index: 1040;
}

.modal-dialog {
    position: relative;
    z-index: 1050;
    margin: 1.75rem auto;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
}

/* Для темной темы */
.dark-style .modal-content {
    background-color: #2b2c40;
    color: #d1d2e8;
}

.dark-style .modal-header {
    background-color: #323349;
    border-color: #444564;
}

.dark-style .modal-footer {
    background-color: #2b2c40;
    border-color: #444564;
}


