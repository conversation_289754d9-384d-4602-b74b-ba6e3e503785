<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\GameMatch;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Title;

#[Title('Результаты матчей')]
class MatchResults extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    public $gameType = 'all';
    public $dateFrom = '';
    public $dateTo = '';
    public $resultStatus = 'all'; // 'all', 'victory', 'defeat'
    public $selectedMatch = null;
    public $selectedMatchLog = null;
    public $selectedMatchLogGameover = null;
    
    public function showDetails($matchId)
    {
        $this->selectedMatch = GameMatch::with('player')->find($matchId);
        if ($this->selectedMatch) {
            $this->selectedMatchLog = json_decode($this->selectedMatch->log, true);
            $this->selectedMatchLogGameover = json_decode($this->selectedMatch->log_gameover, true);
        }
    }
    
    public function closeDetails()
    {
        $this->selectedMatch = null;
        $this->selectedMatchLog = null;
        $this->selectedMatchLogGameover = null;
    }
    
    public function updatedGameType()
    {
        $this->resetPage();
    }
    
    public function updatedDateFrom()
    {
        $this->resetPage();
    }
    
    public function updatedDateTo()
    {
        $this->resetPage();
    }
    
    public function updatedResultStatus()
    {
        $this->resetPage();
    }

    // Метод для определения модели по типу игры
    protected function getModelByGameType()
    {
        return match ($this->gameType) {
            '1' => \App\Models\GameMatch::class,
            '2' => \App\Models\Dota2::class,
            '3' => \App\Models\Pubg::class,
            default => \App\Models\GameMatch::class, // или null, если нужно ничего не показывать
        };
    }
    
    /**
     * Получить название игры по типу
     */
    private function getGameName($gameType)
    {
        return match($gameType) {
            'cs2' => 'CS2',
            'dota2' => 'Dota 2',
            'pubg' => 'PUBG',
            default => 'CS2'
        };
    }

    public function render()
    {
        $modelClass = $this->getModelByGameType();        

        // Если тип all, можно взять из всех моделей — но чаще логичнее просто GameMatch
        $query = $modelClass::query();

        $query->where('player_id', Auth::id());

        // Применяем фильтр game_type только если модель GameMatch
        // if ($this->gameType !== 'all' && $modelClass === \App\Models\GameMatch::class) {
        //     $query->where('game_type', $this->gameType);
        // }

        if ($this->dateFrom) {
            $query->whereDate('date', '>=', $this->dateFrom);
        }

        if ($this->dateTo) {
            $query->whereDate('date', '<=', $this->dateTo);
        }

        if ($this->resultStatus !== 'all') {
            $query->where('victory', $this->resultStatus === 'victory' ? 1 : 0);
        }              

        $matches = $query->orderBy('date', 'desc')->paginate(10);

        return view('livewire.match-results', [
            'matches' => $matches,
            'getGameName' => [$this, 'getGameName']
        ]);
    }
}



