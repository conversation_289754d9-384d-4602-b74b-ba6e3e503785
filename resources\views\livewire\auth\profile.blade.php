<div>
    <div class="row">
        
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-sm-6 pb-5">
                            <div class="avatar avatar-xl mb-3 position-relative">
                                @if($user->avatar)
                                    <img src="{{ asset('images/avatars/' . $user->avatar) }}" 
                                         alt="{{ $user->client_nick ?? $user->name ?? 'Пользователь' }}" 
                                         class="w-px-100 h-auto rounded-circle" />
                                @else
                                    <img src="https://ui-avatars.com/api/?name={{ urlencode($user->client_nick ?? $user->name ?? 'Пользователь') }}&background=random" 
                                         alt="{{ $user->client_nick ?? $user->name ?? 'Пользователь' }}" 
                                         class="w-px-100 h-auto rounded-circle" />
                                @endif
                                
                                <div class="position-absolute bottom-0 end-0 d-flex gap-2" style="transform: translate(110%, 170%);">
                                    <label for="avatar-upload" class="bg-primary rounded-circle p-2 cursor-pointer" style="cursor: pointer;">
                                        <i class="ri-camera-fill text-white"></i>
                                        <input type="file" 
                                               id="avatar-upload" 
                                               wire:model="avatar" 
                                               class="d-none" 
                                               accept="image/*"
                                               wire:loading.attr="disabled"
                                               wire:target="avatar" />
                                    </label>
                                    
                                    @if($user->avatar)
                                        <button type="button" 
                                                class="bg-danger rounded-circle p-2 border-0" 
                                                style="cursor: pointer;"
                                                wire:click="deleteAvatar"
                                                wire:loading.attr="disabled"
                                                wire:target="deleteAvatar">
                                            <i class="ri-delete-bin-fill text-white"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 py-5">
                            <h5 class="mb-1">{{ $user->client_nick ?? $user->name ?? 'Пользователь' }}</h5>
                        <p class="text-muted mb-3">{{ $user->email }}</p>
                        <div class="d-flex justify-content-center">
                            <button type="button" class="btn btn-primary me-2" wire:click="$refresh">Обновить</button>
                            <button type="button" class="btn btn-outline-danger" wire:click="logout">Выйти</button>
                        </div>
                        </div>
                    </div>
                </div>
                @error('avatar') <span class="text-danger">{{ $message }}</span> @enderror

                <div wire:loading wire:target="avatar" class="mt-2">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <span class="ms-2">Загрузка аватара...</span>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Детали профиля</h5>
                    <button type="button" class="btn btn-primary btn-icon" wire:click="toggleEditMode">
                        <i class="ri-pencil-line"></i>
                    </button>
                </div>
                <div class="card-body">                    
                    @if($editMode)
                    <form wire:submit.prevent="saveProfile">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">Полное имя</h6>
                            </div>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" wire:model="name" name="name">
                                @error('name') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        <hr>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">Email</h6>
                            </div>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" wire:model="email">
                                @error('email') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        <hr>
                        
                        <!-- Секция изменения пароля с Alpine.js -->
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">Текущий пароль</h6>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-password-toggle" x-data="{ show: false }">
                                    <div class="input-group">
                                        <input :type="show ? 'text' : 'password'" class="form-control" wire:model="current_password">
                                        <span class="input-group-text cursor-pointer" @click="show = !show">
                                            <i :class="show ? 'ri-eye-off-line' : 'ri-eye-line'"></i>
                                        </span>
                                    </div>
                                    @error('current_password') <span class="text-danger">{{ $message }}</span> @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">Новый пароль</h6>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-password-toggle" x-data="{ show: false }">
                                    <div class="input-group">
                                        <input :type="show ? 'text' : 'password'" class="form-control" wire:model="password">
                                        <span class="input-group-text cursor-pointer" @click="show = !show">
                                            <i :class="show ? 'ri-eye-off-line' : 'ri-eye-line'"></i>
                                        </span>
                                    </div>
                                    @error('password') <span class="text-danger">{{ $message }}</span> @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <h6 class="mb-0">Подтверждение пароля</h6>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-password-toggle" x-data="{ show: false }">
                                    <div class="input-group">
                                        <input :type="show ? 'text' : 'password'" class="form-control" wire:model="password_confirmation">
                                        <span class="input-group-text cursor-pointer" @click="show = !show">
                                            <i :class="show ? 'ri-eye-off-line' : 'ri-eye-line'"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        
                        <div class="row">
                            <div class="col-sm-12 text-end">
                                <button type="submit" class="btn btn-primary">Сохранить</button>
                                <button type="button" class="btn btn-secondary ms-2" wire:click="toggleEditMode">Отмена</button>
                            </div>
                        </div>
                    </form>
                @else
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <h6 class="mb-0">Полное имя</h6>
                        </div>
                        <div class="col-sm-9 text-secondary">
                            {{ $user->client_nick ?? $user->name ?? 'Не указано' }}
                        </div>
                    </div>
                    <hr>
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <h6 class="mb-0">Email</h6>
                        </div>
                        <div class="col-sm-9 text-secondary">
                            {{ $user->email }}
                        </div>
                    </div>
                    <hr>
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <h6 class="mb-0">Дата регистрации</h6>
                        </div>
                        <div class="col-sm-9 text-secondary">
                            {{ $user->created_at?->format('d.m.Y H:i') }}
                        </div>
                    </div>
                    <hr>
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <h6 class="mb-0">Последнее обновление</h6>
                        </div>
                        <div class="col-sm-9 text-secondary">
                            {{ $user->updated_at?->format('d.m.Y H:i') }}
                        </div>
                    </div>
                @endif
                </div>
            </div>
        </div>
    </div>
    <style>
        .avatar-upload-icon {
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: #696cff;
            border-radius: 50%;
            padding: 6px;
            cursor: pointer;
        }
    </style>    
</div>





