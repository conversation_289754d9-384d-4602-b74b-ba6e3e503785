<div>
  <livewire:carousel />
  <div class="row">
    <div class="col-sm-8 offset-2">
     @include('livewire.notification')

      <div class="d-flex justify-content-end mb-3">
          <select wire:model.live="perPage" class="form-select form-select-sm" style="width: auto;">
              <option value="10">10 записей</option>
              <option value="20">20 записей</option>
              <option value="50">50 записей</option>
              <option value="100">100 записей</option>
          </select>
      </div>

      <div class="card">
        <div class="card-body">
          <table class="table">
            <thead>
              <tr>
                <th scope="col">#</th>
                <th scope="col">Game</th>
                <th scope="col">Gaming Club</th>
                <th scope="col">Player</th>
                <th scope="col">Rating</th>
              </tr>
            </thead>
            <tbody>
              @foreach($ratings as $rating)
              <tr>
                <th scope="row">
                  <span class="badge rounded-pill"
                    style="background-color: 
                    @if($loop->index == 0) gold
                    @elseif($loop->index == 1) silver
                    @elseif($loop->index == 2) tan
                    @else #8592a3
                    @endif
                    ">
                    RG
                  </span>
                </th>
                <td>{{ $rating->game->name ?? 'N/A' }}</td>
                <td>{{ $rating->club->name ?? 'N/A' }}</td>
                <td>{{ $rating->player->client_nick ?? 'N/A' }}</td>
                <td>{{ $rating->game_rating }}</td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    
    <div class="d-flex justify-content-center mt-4">
      {{ $ratings->links() }}
    </div>
  </div>
</div>








