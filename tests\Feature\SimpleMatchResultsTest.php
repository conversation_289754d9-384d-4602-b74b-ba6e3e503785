<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SimpleMatchResultsTest extends TestCase
{
    /**
     * Простой тест для проверки результатов матчей.
     */
    public function test_simple_match_results(): void
    {
        // Создаем необходимые таблицы
        $this->createTestTables();
        
        // Вставляем игру
        DB::table('games')->insert([
            'name' => 'CS2',
            'code' => 'cs2',
            'image' => 'games/cs2.jpg',
            'team_size' => 5,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Вставляем пользователя
        $playerId = DB::table('players')->insertGetId([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Вставляем матч
        $matchId = DB::table('game_matches')->insertGetId([
            'game_type' => 'cs2',
            'session_id' => 'test-session-123',
            'match_id' => 'test-match-123',
            'score' => 85,
            'date_scan' => now(),
            'col_scan' => 1,
            'player_id' => $playerId,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Вставляем результат матча
        DB::table('match_results')->insert([
            'match_id' => $matchId,
            'victory' => true,
            'add_score' => 85,
            'details' => json_encode(['kills' => 20, 'deaths' => 10]),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что данные вставлены
        $this->assertDatabaseHas('game_matches', [
            'game_type' => 'cs2',
            'session_id' => 'test-session-123'
        ]);
        
        $this->assertDatabaseHas('match_results', [
            'match_id' => $matchId,
            'victory' => 1
        ]);
    }
    
    /**
     * Создает необходимые таблицы для тестов.
     */
    protected function createTestTables()
    {
        // Создаем таблицу players, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_nick TEXT,
                email TEXT UNIQUE,
                password TEXT,
                remember_token TEXT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Создаем таблицу games, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT,
                image TEXT NOT NULL DEFAULT "",
                team_size INTEGER NOT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Создаем таблицу game_matches, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS game_matches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_type TEXT NOT NULL,
                session_id TEXT NOT NULL,
                match_id TEXT,
                score INTEGER,
                match_score TEXT,
                date_scan DATETIME,
                col_scan INTEGER NOT NULL DEFAULT 0,
                player_id INTEGER,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Создаем таблицу match_results, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS match_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id INTEGER NOT NULL,
                victory BOOLEAN NOT NULL DEFAULT 0,
                round INTEGER NOT NULL DEFAULT 1,
                add_score INTEGER NOT NULL DEFAULT 0,
                details TEXT,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
    }
}