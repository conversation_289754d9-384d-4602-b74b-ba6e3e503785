<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SqliteTest extends TestCase
{
    /**
     * Базовый тест для проверки работы SQLite.
     */
    public function test_sqlite_database_works(): void
    {
        // Проверяем, что используется SQLite
        $this->assertEquals('sqlite', config('database.default'));
        
        // Создаем простую таблицу
        DB::statement('CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)');
        
        // Вставляем данные
        DB::insert('INSERT INTO test_table (name) VALUES (?)', ['Test Name']);
        
        // Проверяем, что данные вставлены
        $result = DB::select('SELECT * FROM test_table WHERE name = ?', ['Test Name']);
        
        $this->assertCount(1, $result);
        $this->assertEquals('Test Name', $result[0]->name);
    }
}
