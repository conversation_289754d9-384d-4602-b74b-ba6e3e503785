<!DOCTYPE html>
<html>
<head>
    <title>Тест функционала команд</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    @livewireStyles
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h3>Тест функционала команд и приглашений</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Информация:</strong><br>
                    Текущий пользователь: {{ auth()->user()->client_nick ?? 'Не авторизован' }} (ID: {{ auth()->id() ?? 'N/A' }})<br>
                    Эта страница тестирует функционал создания команд, приглашений и отмены приглашений.
                </div>
                
                @if(auth()->check())
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Компонент CreateTeam</h5>
                            <div class="border p-3 mb-4">
                                @livewire('create-team', ['defaultGameId' => 1])
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Логи действий</h5>
                            <div id="actionLog" class="border p-3" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                                Инициализация...<br>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Тестовые действия</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="testInvitePlayer()">
                                    Тест приглашения игрока
                                </button>
                                <button class="btn btn-warning" onclick="testCancelInvitation()">
                                    Тест отмены приглашения
                                </button>
                                <button class="btn btn-info" onclick="refreshComponents()">
                                    Обновить компоненты
                                </button>
                                <button class="btn btn-secondary" onclick="clearLogs()">
                                    Очистить логи
                                </button>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="alert alert-warning">
                        Для тестирования функционала необходимо авторизоваться.
                        <a href="/login" class="btn btn-primary ms-2">Войти</a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @livewireScripts
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('actionLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function testInvitePlayer() {
            log('🧪 Тестируем приглашение игрока...');
            // Здесь можно добавить логику для автоматического тестирования
            log('ℹ️ Откройте модальное окно приглашения и проверьте список игроков');
        }
        
        function testCancelInvitation() {
            log('🧪 Тестируем отмену приглашения...');
            log('ℹ️ Найдите активное приглашение и попробуйте его отменить');
        }
        
        function refreshComponents() {
            log('🔄 Обновляем компоненты...');
            Livewire.dispatch('refreshTeamList', { gameId: 1 });
            Livewire.dispatch('refreshReceivedInvitations');
            log('✅ Команды обновления отправлены');
        }
        
        function clearLogs() {
            document.getElementById('actionLog').innerHTML = 'Логи очищены...<br>';
        }
        
        // Слушаем события Livewire
        document.addEventListener('livewire:init', () => {
            log('✅ Livewire инициализирован');
            
            Livewire.on('showNotification', (event) => {
                const data = event[0] || event;
                log(`📢 Уведомление [${data.type}]: ${data.message}`);
            });
            
            Livewire.on('refreshTeamList', (event) => {
                log('🔄 Получено событие refreshTeamList');
            });
            
            Livewire.on('refreshReceivedInvitations', (event) => {
                log('🔄 Получено событие refreshReceivedInvitations');
            });
            
            Livewire.on('closeInviteModal', (event) => {
                log('❌ Модальное окно приглашения закрыто');
            });
        });
        
        // Отслеживаем изменения в DOM для отладки
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('alert')) {
                            log('🔍 Обнаружено новое уведомление в DOM');
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        log('🚀 Тестовая страница загружена');
    </script>
</body>
</html>
