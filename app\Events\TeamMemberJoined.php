<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TeamMemberJoined
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $teamId;

    /**
     * Create a new event instance.
     */
    public function __construct($teamId)
    {
        $this->teamId = $teamId;
    }
}