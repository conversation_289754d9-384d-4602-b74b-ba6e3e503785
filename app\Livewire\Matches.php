<?php

namespace App\Livewire;

use App\Models\GameMatch;
use App\Models\Cs2Map;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Title;

#[Title('Матчи')]
class Matches extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    public $gameType = 'all';
    public $dateFrom = '';
    public $dateTo = '';
    public $resultStatus = 'all';
    public $selectedMatch = null;
    
    public $newMatch = [
        'game_type' => 'cs2',
        'session_id' => '',
        'match_id' => ''
    ];
    
    public $showCreateForm = false;
    public $bannedMaps = [];
    public $maxBans = 3;

    public function mount()
    {
        $this->bannedMaps = session()->get('banned_maps', []);
    }

    public function showCreateForm()
    {
        $this->showCreateForm = true;
        $this->resetNewMatch();
    }
    
    public function hideCreateForm()
    {
        $this->showCreateForm = false;
        $this->resetNewMatch();
    }
    
    public function resetNewMatch()
    {
        $this->newMatch = [
            'game_type' => 'cs2',
            'session_id' => '',
            'match_id' => ''
        ];
        session()->forget('banned_maps');
        $this->bannedMaps = [];
    }

    public function banMap($mapId)
    {
        if (count($this->bannedMaps) >= $this->maxBans) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Достигнут лимит банов карт'
            ]);
            return;
        }

        if (!in_array($mapId, $this->bannedMaps)) {
            $this->bannedMaps[] = $mapId;
            session()->put('banned_maps', $this->bannedMaps);
        }
    }

    public function unbanMap($mapId)
    {
        $this->bannedMaps = array_diff($this->bannedMaps, [$mapId]);
        session()->put('banned_maps', $this->bannedMaps);
    }
    
    public function createMatch()
    {
        $this->validate([
            'newMatch.game_type' => 'required|in:cs2,dota2,pubg',
            'newMatch.session_id' => 'required|string',
            'newMatch.match_id' => 'required|string'
        ]);

        $match = GameMatch::create([
            'game_type' => $this->newMatch['game_type'],
            'player_id' => Auth::id(),
            'date' => now(),
            'log' => json_encode([
                'session_id' => $this->newMatch['session_id'],
                'match_id' => $this->newMatch['match_id']
            ])
        ]);

        $this->reset(['newMatch', 'bannedMaps']);
        session()->forget('banned_maps');
        $this->hideCreateForm();

        $this->dispatch('showNotification', [
            'type' => 'success',
            'message' => 'Матч успешно создан'
        ]);
    }
    
    public function showDetails($matchId)
    {
        $this->selectedMatch = GameMatch::with('player')->find($matchId);
    }
    
    public function closeDetails()
    {
        $this->selectedMatch = null;
    }
    
    public function updatedGameType()
    {
        $this->resetPage();
    }
    
    public function updatedDateFrom()
    {
        $this->resetPage();
    }
    
    public function updatedDateTo()
    {
        $this->resetPage();
    }
    
    public function updatedResultStatus()
    {
        $this->resetPage();
    }
    
    /**
     * Получить название игры по типу
     */
    private function getGameName($gameType)
    {
        return match($gameType) {
            'cs2' => 'CS2',
            'dota2' => 'Dota 2',
            'pubg' => 'PUBG',
            default => 'CS2'
        };
    }

    public function render()
    {
        $query = GameMatch::query()
            ->when($this->gameType !== 'all', function($q) {
                return $q->where('game_type', $this->gameType);
            })
            ->when($this->dateFrom, function($q) {
                return $q->where('date', '>=', $this->dateFrom);
            })
            ->when($this->dateTo, function($q) {
                return $q->where('date', '<=', $this->dateTo);
            })
            ->when($this->resultStatus !== 'all', function($q) {
                return $q->where('victory', $this->resultStatus);
            })
            ->with(['player:id,client_nick,avatar'])
            ->orderBy('date', 'desc');

        $matches = $query->paginate(10);

        $availableMaps = Cs2Map::where('is_active', true)->get();
        
        return view('livewire.matches', [
            'matches' => $matches,
            'availableMaps' => $availableMaps,
            'getGameName' => [$this, 'getGameName'],
            'gameTypes' => [
                'cs2' => 'CS2',
                'dota2' => 'Dota 2',
                'pubg' => 'PUBG'
            ]
        ]);
    }
}