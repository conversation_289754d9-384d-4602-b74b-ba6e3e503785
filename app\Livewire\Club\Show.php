<?php

namespace App\Livewire\Club;

use Livewire\Component;
use App\Models\Club;
use App\Models\FullRating;
use Livewire\Attributes\Title;

#[Title('Профиль клуба')]
class Show extends Component
{
    public Club $club;
    
    public function mount(Club $club)
    {
        $this->club = $club;
    }
    
    public function render()
    {
        // Получаем всех игроков клуба с их рейтингами
        $players = FullRating::with(['player', 'game'])
            ->where('club_id', $this->club->club_id)
            ->orderBy('game_rating', 'desc')
            ->get()
            ->groupBy('player_id');
            
        return view('livewire.club.show', [
            'players' => $players,
        ]);
    }
} 