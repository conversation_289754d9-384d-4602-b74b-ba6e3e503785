<?php

namespace Database\Seeders;

use App\Models\Game;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
        
        // Создаем дополнительных пользователей
        //User::factory(20)->create();

        // Вызываем сидер для игр
        $this->call([
            GameSeeder::class,
            // ClubSeeder::class,
            // TeamSeeder::class,
            // RatingSeeder::class,
            // MatchSeeder::class,
            // RatingSeeder::class,
            // SeasonRatingSeeder::class, 
        ]);
    }
}





