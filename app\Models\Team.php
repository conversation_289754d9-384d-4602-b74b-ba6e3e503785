<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $name
 * @property int $game_id
 * @property int $captain_id
 * @property int|null $rating
 * @property bool $is_searching
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $captain
 * @property-read \App\Models\Game|null $game
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TeamInvitation> $invitations
 * @property-read int|null $invitations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\JoinRequest> $joinRequests
 * @property-read int|null $join_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\GameMatch> $matches
 * @property-read int|null $matches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TeamMember> $members
 * @property-read int|null $members_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\JoinRequest> $requests
 * @property-read int|null $requests_count
 * @method static \Database\Factories\TeamFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereCaptainId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereGameId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Team extends Model
{
    use HasFactory;
    
    protected $fillable = ['name', 'game_id', 'captain_id', 'rating', 'is_searching'];

    public function game(): BelongsTo
    {
        return $this->belongsTo(Game::class);
    }

    public function captain(): BelongsTo
    {
        return $this->belongsTo(User::class, 'captain_id');
    }

    public function members(): HasMany
    {
        return $this->hasMany(TeamMember::class);
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(TeamInvitation::class)->where('status', 'pending');
    }

    public function requests(): HasMany
    {
        return $this->hasMany(JoinRequest::class);
    }

    // Проверить, является ли пользователь членом команды
    public function hasMember($userId)
    {
        return $this->members()->where('player_id', $userId)->exists();
    }

    // Получить роль пользователя в команде
    public function getMemberRole($userId)
    {
        $member = $this->members()->where('player_id', $userId)->first();
        return $member ? $member->role : null;
    }

    // Получить всех участников команды с их данными
    public function getMembersWithDetails()
    {
        return $this->members()->with('user')->get();
    }

    // Получить все активные приглашения команды
    public function getPendingInvitations()
    {
        return $this->invitations()->where('status', 'pending')->with('user')->get();
    }

    // Получить все активные заявки команды
    public function getPendingRequests()
    {
        return $this->requests()->where('status', 'pending')->with('user')->get();
    }

    // Проверить, есть ли у команды активные приглашения для пользователя
    public function hasPendingInvitationFor($userId)
    {
        return $this->invitations()
            ->where('player_id', $userId)
            ->where('status', 'pending')
            ->exists();
    }

    // Проверить, есть ли у команды активные заявки от пользователя
    public function hasPendingRequestFrom($userId)
    {
        return $this->requests()
            ->where('player_id', $userId)
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Получить заявки на вступление в команду
     */
    public function joinRequests()
    {
        return $this->hasMany(JoinRequest::class);
    }

    /**
     * Получить матчи команды
     */
    public function matches()
    {
        return $this->hasMany(GameMatch::class, 'club_id', 'id');
    }
}




