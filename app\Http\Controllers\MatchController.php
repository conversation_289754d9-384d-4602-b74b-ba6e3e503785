<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\GameMatch;
use App\Models\MatchResult;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MatchController extends Controller
{
    /**
     * Начать новый матч
     */
    public function start(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'game_type' => 'required|string',
            'session_id' => 'required|string',
            'match_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $match = new GameMatch();
            $match->game_type = $request->game_type;
            $match->session_id = $request->session_id;
            $match->match_id = $request->match_id;
            $match->player_id = Auth::id();
            $match->date_scan = now();
            $match->col_scan = 0;
            $match->created_at = now();
            $match->updated_at = now();
            $match->save();

            return response()->json([
                'success' => true,
                'match_id' => $match->id,
                'message' => 'Матч успешно создан'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при создании матча: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Добавить результат матча
     */
    public function result(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'match_id' => 'required|exists:game_matches,id',
            'victory' => 'required|boolean',
            'score' => 'required|integer',
            'details' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Получаем матч
            $match = GameMatch::findOrFail($request->match_id);
            
            // Проверяем, принадлежит ли матч текущему пользователю
            if ($match->player_id != Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Доступ запрещен'
                ], 403);
            }
            
            // Создаем результат матча
            $result = new MatchResult();
            $result->match_id = $match->id;
            $result->player_id = Auth::id();
            $result->victory = $request->victory;
            $result->add_score = $request->score;
            $result->details = json_encode($request->details);
            $result->created_at = now();
            $result->updated_at = now();
            $result->save();
            
            // Обновляем матч
            $match->score = $request->score;
            $match->col_scan = 1;
            $match->updated_at = now();
            $match->save();

            return response()->json([
                'success' => true,
                'message' => 'Результат матча успешно сохранен'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при сохранении результата матча: ' . $e->getMessage()
            ], 500);
        }
    }
}





