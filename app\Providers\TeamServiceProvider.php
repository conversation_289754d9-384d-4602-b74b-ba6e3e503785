<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Interfaces\TeamManagerInterface;
use App\Services\Interfaces\InvitationManagerInterface;
use App\Services\Interfaces\RequestManagerInterface;
use App\Services\TeamService;
use App\Services\InvitationService;
use App\Services\RequestService;

class TeamServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Привязываем интерфейс TeamManagerInterface к его реализации TeamService
        $this->app->singleton(TeamManagerInterface::class, TeamService::class);

        // Привязываем интерфейс InvitationManagerInterface к его реализации InvitationService
        // Обрати внимание: InvitationService зависит от TeamManagerInterface,
        // Laravel автоматически разрешит эту зависимость благодаря предыдущей привязке.
        $this->app->singleton(InvitationManagerInterface::class, InvitationService::class);

        // Привязываем интерфейс RequestManagerInterface к его реализации RequestService
        // Аналогично, RequestService зависит от TeamManagerInterface, Laravel справится с этим.
        $this->app->singleton(RequestManagerInterface::class, RequestService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}