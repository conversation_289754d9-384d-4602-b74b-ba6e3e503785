<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Team;
use App\Models\TeamInvitation;
use App\Models\TeamMember;
use App\Models\JoinRequest;
use App\Models\User;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use App\Events\TeamMemberJoined;
use App\Events\TeamMemberLeft;
use App\Events\JoinRequestSent; // Добавляем новое событие
use Illuminate\Support\Facades\Log;

#[Title('Поиск команды')]
class FindTeam extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    public $selectedGameId = 1;
    public $searchTerm = '';
    public $sentRequests = []; // Для хранения team_id отправленных заявок (для проверки наличия)
    public $sentJoinRequests; // Для хранения объектов JoinRequest (для отображения)
    public $receivedInvitations = [];
    
    #[On('gameChanged')]
    public function updateSelectedGame($gameId)
    {
        $this->selectedGameId = $gameId;
        
        // Проверяем, является ли пользователь членом команды для выбранной игры
        
        $this->resetPage();
        $this->loadSentRequests();
        $this->loadReceivedInvitations();
    }
    
    public function mount()
    {
        $this->loadSentRequests();
        $this->loadReceivedInvitations();
    }
    
    #[On('loadSentRequests')]
    public function loadSentRequests()
    {
        if (Auth::check()) {
            $this->sentJoinRequests = JoinRequest::where('player_id', Auth::id())
                ->where('status', 'pending')
                ->with('team')
                ->get();
        } else {
            $this->sentJoinRequests = collect([]);
        }
                
        $this->sentRequests = $this->sentJoinRequests->pluck('team_id')->toArray();
    }

    public function getListeners()
    {
        return [
            "echo-private:player." . Auth::id() . ",join.request.sent" => 'loadSentRequests',
            "echo-private:player." . Auth::id() . ",join.request.rejected" => 'loadSentRequests',
            "echo-private:player." . Auth::id() . ",join.request.accepted" => 'loadSentRequests',
        ];
    }
    
    /**
     * Загрузить список приглашений, полученных пользователем
     */
    public function loadReceivedInvitations()
    {
        if (Auth::check()) {
            $this->receivedInvitations = TeamInvitation::where('player_id', Auth::id())
                ->where('status', 'pending')
                ->with(['team', 'team.captain'])
                ->get();
        }
    }
    
    /**
     * Принять приглашение в команду
     */
    public function acceptInvitation($invitationId)
    {
        $invitation = TeamInvitation::find($invitationId);
        
        if (!$invitation || $invitation->player_id !== Auth::id()) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        // Проверяем, есть ли уже команда у пользователя для этой игры
        $team = Team::find($invitation->team_id);
        if (User::find(Auth::id())->hasTeamForGame($team->game_id)) {
            session()->flash('error', 'Вы уже состоите в команде для этой игры');
            return;
        }
        
        // Удаляем все существующие приглашения для этой команды и пользователя
        TeamInvitation::where('team_id', $invitation->team_id)
            ->where('player_id', Auth::id())
            ->where('id', '!=', $invitationId) // Не удаляем текущее приглашение
            ->delete();
        
        // Добавляем пользователя в команду через модель TeamMember
        TeamMember::create([
            'team_id' => $invitation->team_id,
            'player_id' => Auth::id(), // Используем player_id вместо user_id
            'role' => 'member'
        ]);
        
        // Удаляем приглашение вместо обновления статуса
        $invitation->delete();
        
        // Обновляем список приглашений
        $this->loadReceivedInvitations();
        
        // Отправляем событие через broadcasting
        event(new \App\Events\TeamMemberJoined($invitation->team_id));
        
        session()->flash('success', 'Вы успешно присоединились к команде');
    }
    
    /**
     * Отклонить приглашение в команду
     */
    public function declineInvitation($invitationId)
    {
        $invitation = TeamInvitation::find($invitationId);
        
        if (!$invitation || $invitation->player_id !== Auth::id()) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        // Обновляем статус приглашения
        $invitation->update(['status' => 'declined']);
        
        // Обновляем список приглашений
        $this->loadReceivedInvitations();
        
        session()->flash('success', 'Приглашение отклонено');
    }
    
    public function sendJoinRequest($teamId)
    {
        if (!Auth::check()) {
            session()->flash('error', 'Необходимо войти в систему');
            return;
        }

        // Получаем команду для проверки игры
        $team = Team::find($teamId);
        if (!$team) {
            session()->flash('error', 'Команда не найдена');
            return;
        }

        // Проверяем, не состоит ли пользователь уже в команде для этой игры
        $memberExists = TeamMember::whereHas('team', function($query) use ($team) {
            $query->where('game_id', $team->game_id);
        })->where('player_id', Auth::id())->exists();
        
        $captainExists = Team::where('captain_id', Auth::id())->where('game_id', $team->game_id)->exists();

        if ($memberExists || $captainExists) {
            Log::info('User already in team check', [
                'user_id' => Auth::id(),
                'game_id' => $team->game_id,
                'member_exists' => $memberExists,
                'captain_exists' => $captainExists
            ]);
            session()->flash('error', 'Вы уже состоите в команде для этой игры');
            return;
        }

        // Проверяем, не отправлял ли пользователь уже заявку в эту команду (любого статуса)
        $existingRequest = JoinRequest::where('player_id', Auth::id())
            ->where('team_id', $teamId)
            ->first();
            
        // Проверяем количество активных заявок пользователя для этой игры
        $activeRequestsCount = JoinRequest::where('player_id', Auth::id())
            ->whereHas('team', function($query) use ($team) {
                $query->where('game_id', $team->game_id);
            })
            ->whereIn('status', ['pending', 'accepted'])
            ->count();
            
        if ($activeRequestsCount >= 3) {
            session()->flash('error', 'Вы не можете отправить больше 3 заявок на вступление в команды для одной игры');
            return;
        }

        if ($existingRequest) {
            if ($existingRequest->status === 'pending') {
                session()->flash('error', 'Вы уже отправили заявку в эту команду');
                return;
            } else {
                // Если заявка была отклонена или принята, обновляем её статус на pending
                $existingRequest->update([
                    'status' => 'pending',
                    'updated_at' => now()
                ]);
            }
        } else {
            // Если заявки нет, создаем новую
            JoinRequest::create([
                'player_id' => Auth::id(),
                'team_id' => $teamId,
                'status' => 'pending'
            ]);
        }
        
        // Обновляем список отправленных заявок
        $this->loadSentRequests();
        
        // Отправляем событие, чтобы TeamList обновил список заявок для капитана
        event(new JoinRequestSent($teamId, Auth::id()));

        session()->flash('success', 'Заявка на вступление в команду отправлена');
    }
    
    public function cancelRequest($teamId)
    {
        // Удаляем заявку
        JoinRequest::where('player_id', Auth::id())
            ->where('team_id', $teamId)
            ->where('status', 'pending')
            ->delete();
            
        // Обновляем список отправленных заявок
        $this->loadSentRequests();
        
        session()->flash('success', 'Заявка отменена');
    }
    
    /**
     * Выйти из команды
     */
    public function leaveTeam($teamId)
    {
        // Проверяем, состоит ли пользователь в этой команде
        $teamMember = \App\Models\TeamMember::where('team_id', $teamId)
            ->where('player_id', Auth::id())
            ->first();
        
        if (!$teamMember) {
            session()->flash('error', 'Вы не состоите в этой команде');
            return;
        }
        
        // Проверяем, не является ли пользователь капитаном
        if ($teamMember->role === 'captain') {
            session()->flash('error', 'Капитан не может покинуть команду. Сначала передайте права капитана другому участнику или расформируйте команду.');
            return;
        }
        
        // Удаляем пользователя из команды
        $teamMember->delete();
        
        // Удаляем или обновляем все принятые приглашения для этой команды
        TeamInvitation::where('team_id', $teamId)
            ->where('player_id', Auth::id())
            ->where('status', 'accepted')
            ->delete(); // или можно изменить статус на 'left'
        
        // Обновляем списки команд и приглашений
        $this->loadReceivedInvitations();
        
        // Отправляем событие об изменении состава команды
        event(new \App\Events\TeamMemberLeft($teamId));
        
        session()->flash('success', 'Вы успешно покинули команду');
    }

    #[On('gameChanged')]
    public function gameChange($gameId)
    {
        $this->selectedGameId = $gameId;
    }
    
    public function render()
    {
        $teamsQuery = Team::query();
        
        // Получаем размер команды для выбранной игры
        $game = \App\Models\Game::find($this->selectedGameId);
        $maxTeamSize = $game ? $game->team_size : 0; // Устанавливаем 0 или другое значение по умолчанию, если игра не найдена
        
        // Фильтруем по выбранной игре
        if ($this->selectedGameId) {
            $teamsQuery->where('game_id', $this->selectedGameId);
        }
        
        // Фильтруем по поисковому запросу
        if ($this->searchTerm) {
            $teamsQuery->where('name', 'like', '%' . $this->searchTerm . '%');
        }
        
        // Исключаем команды, в которых пользователь уже состоит
        if (Auth::check()) {
            $userTeamIds = User::find(Auth::id())->teams()->pluck('team_id')->toArray();
            if (!empty($userTeamIds)) {
                $teamsQuery->whereNotIn('id', $userTeamIds);
            }
        }
        
        $teams = $teamsQuery->with(['captain', 'members'])
            ->withCount('members')
            ->having('members_count', '<', $maxTeamSize) // Используем team_size из модели Game $maxTeamSize
            ->orderBy('rating', 'desc')
            ->paginate(10);
            
        return view('livewire.find-team', [
            'teams' => $teams
        ]);
    }
}





















