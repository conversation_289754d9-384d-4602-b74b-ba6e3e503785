<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Title;
use Livewire\Attributes\On;
use Livewire\Attributes\Lazy;
use App\Models\GameMatch;
use App\Models\Rating;
use App\Models\Club;
use App\Models\Season;
use App\Models\FullRating;
use App\Models\SeasonRating;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Events\JoinRequestSent;

#[Title('Команда игрока')]
#[Lazy] // Добавляем ленивую загрузку компонента
class Team extends Component
{
    public $client_nick;
    public $selectedGameId = 1; // CS2
    public $selectedSeasonId = 1; // Текущий сезон
    public $isLoading = false; // Индикатор загрузки
    public $selectedMap = ''; // Добавляем свойство для карты
    public $activeGame = false;
    public $activeGameImage = '';
    public $showActiveMatchView = false;
    public $team;

    #[On('gameChanged')]
    public function updateSelectedGame($gameId)
    {
        $this->isLoading = true;
        $this->selectedGameId = $gameId;
        session(['selectedGameId' => $gameId]);

        // Загружаем команду для новой выбранной игры
        $this->loadUserTeam();
        
        $this->syncStateWithSession();
        $this->setDefaultMapForGame($gameId);
        $this->clearUserCache(); // Очищаем кэш при смене игры
        $this->isLoading = false;
    }

    public function handleJoinRequestSent()
    {
        $this->loadUserTeam();
        $this->clearUserCache();
    }

    /**
     * Get the number of pending join requests for the current team
     */
    public function getPendingRequestsCountProperty()
    {
        if (!$this->team) {
            return 0;
        }

        return $this->team->joinRequests()
            ->where('status', 'pending')
            ->count();
    }

    public function mount()
    {
        Log::info('Проверка сессии в Team', [
            'session_id' => session()->getId(),
            'all_session' => session()->all()
        ]);

        $this->client_nick = Auth::user()->client_nick;
        $this->selectedGameId = session('selectedGameId', 1); // По умолчанию CS2

        // Загружаем команду пользователя для выбранной игры
        $this->loadUserTeam();

        $this->syncStateWithSession();
        $this->setDefaultMapForGame($this->selectedGameId);
    }
    
    /**
     * Загружает команду пользователя для выбранной игры
     */
    protected function loadUserTeam()
    {
        $this->team = Auth::user()->teams()
            ->where('game_id', $this->selectedGameId)
            ->first();
    }

    public function saveName()
    {
        $this->validate([
            'client_nick' => 'required|string|min:3|max:255',
        ], [
            'client_nick.required' => 'Пожалуйста, введите имя игрока',
            'client_nick.string' => 'Имя должно быть текстом',
            'client_nick.min' => 'Имя должно содержать минимум 3 символа',
            'client_nick.max' => 'Имя не должно превышать 255 символов',
        ]);

        /** @var \App\Models\User $user */
        $user = Auth::user();

        if ($user && $user->client_nick !== $this->client_nick) {
            $user->client_nick = $this->client_nick;
            $user->save();

            session()->flash('success', 'Имя успешно обновлено');

            $this->clearUserCache();
        }

        // Вызываем событие для переинициализации tooltips
        $this->dispatch('nameSaved');
    }

    public function changeUser()
    {
        return $this->redirect('/login', navigate: true);
    }

    public function createTam()
    {
        return $this->redirect('/create/team', navigate:true);
    }

    public function gotoRating($type)
    {
        $position = $this->getPlayerRank($type === 'club');

        $url = route('rating', [
            'type' => $type,
            'highlight' => Auth::id(),
            'scroll' => $position
        ]);

        return $this->redirect($url, navigate: true);
    }

    // Кэшируем результат на 5 минут
    public function getPlayerRank($forClub = false)
    {
        $userId = Auth::id();
        $clubId = Auth::user()->club_id ?? 0;
        $cacheKey = "player_rank_{$userId}_{$this->selectedGameId}" . ($forClub ? "_club_{$clubId}" : "");

        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($userId, $clubId, $forClub) {
            $currentPlayerRating = $this->getPlayerRating($forClub);

            if (is_null($currentPlayerRating)) {
                return null;
            }

            $query = FullRating::where('game_id', $this->selectedGameId);

            if ($forClub) {
                $query->where('club_id', $clubId);
            }

            $rank = $query->select('player_id', DB::raw('SUM(game_rating) as total_rating'))
                ->groupBy('player_id')
                ->having('total_rating', '>', $currentPlayerRating)
                ->count();

            return $rank + 1;
        });
    }

    protected function getPlayerRating($forClub = false)
    {
        $userId = Auth::id();
        $clubId = Auth::user()->club_id ?? 0;
        $cacheKey = "player_rating_{$userId}_{$this->selectedGameId}" . ($forClub ? "_club_{$clubId}" : "");

        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($userId, $clubId, $forClub) {
            $query = FullRating::where('player_id', $userId)
                ->where('game_id', $this->selectedGameId);

            if ($forClub) {
                $query->where('club_id', $clubId);
            }

            return $query->sum('game_rating');
        });
    }

    // Метод для очистки кэша пользователя
    protected function clearUserCache()
    {
        $userId = Auth::id();
        $gameIds = [1, 2, 3]; // ID игр
        $clubId = Auth::user()->club_id ?? 0;

        foreach ($gameIds as $gameId) {
            Cache::forget("player_rating_{$userId}_{$gameId}");
            Cache::forget("player_rank_{$userId}_{$gameId}");
            Cache::forget("player_rating_{$userId}_{$gameId}_club_{$clubId}");
            Cache::forget("player_rank_{$userId}_{$gameId}_club_{$clubId}");
            Cache::forget("team_data_{$userId}_{$gameId}");
        }
    }

    public function render()
    {
        $cacheKey = "team_data_" . Auth::id() . "_" . $this->selectedGameId;

        $data = Cache::remember($cacheKey, now()->addHour(), function () {
            $userId = Auth::id();

            return [
                'recentMatches' => $this->getRecentMatches($userId),
                'teamRating' => $this->getPlayerRating(),
                'seasonRating' => $this->getPlayerRank(true),
                'playerRank' => $this->getPlayerRank(),
            ];
        });

        return view('livewire.team', array_merge($data, [
            'isLoading' => $this->isLoading,
        ]));
    }

    private function getRecentMatches($userId)
    {
        $model = match ((int)$this->selectedGameId) {
            1 => \App\Models\GameMatch::class,
            2 => \App\Models\Dota2::class,
            3 => \App\Models\Pubg::class,
            default => null,
        };

        if (!$model) {
            return collect([]);
        }

        $query = $model::where('player_id', $userId)
            ->orderBy('date', 'desc')
            ->take(3);

        if ($this->selectedGameId == 1) {
            $query->with('matchResult');
        }

        return $query->get()->each(function ($match) {
            if (!($match->date instanceof \Carbon\Carbon)) {
                $match->date = \Carbon\Carbon::parse($match->date);
            }
        });
    }

    private function syncStateWithSession()
    {
        // Проверяем, нужно ли показывать вид активного матча
        $this->showActiveMatchView = session()->has('show_active_match_view_until') &&
            session('show_active_match_view_until') === 'show_active_match_view' &&
            $this->selectedGameId == session('activeInActiveGameId');

        // Проверяем, активна ли игра
        $this->activeGame = session('activeGame', false);
        if ($this->showActiveMatchView) {
            $this->setActiveGameImage($this->selectedGameId);
        } else {
            $this->activeGameImage = ''; // Сбрасываем изображение, если вид активного матча не отображается
        }
    }
    
    public function getListeners()
    {
        return [
            "echo-private:player." . Auth::id() . ",join.request.sent" => 'handleJoinRequestSent',
        ];
    }

    private function setDefaultMapForGame($gameId)
    {
        $this->selectedMap = match ((int)$gameId) {
            1 => 'Dust II', // CS2
            2 => 'Ancient', // Dota 2
            3 => 'Erangel', // PUBG
            default => '',
        };
    }

    private function setActiveGameImage($gameId)
    {
        $this->activeGameImage = match ((int)$gameId) {
            1 => asset('active/cs2.png'),
            2 => asset('active/dota2.webp'),
            3 => asset('active/pubg.webp'),
            default => '',
        };
    }
}










