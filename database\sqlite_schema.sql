PRAGMA foreign_keys = OFF;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS teams (
id INTEGER NOT NULL AUTOINCREMENT,
name TEXT NOT NULL,
description TEXT DEFAULT NULL,
game_id INTEGER NOT NULL,
captain_id INTEGER NOT NULL,
rating INTEGER DEFAULT 10,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS live_match (
id INTEGER NOT NULL AUTOINCREMENT,
team1_id INTEGER NOT NULL,
team2_id INTEGER NOT NULL,
status enum ('map_voting', 'ready_check', 'live') DEFAULT 'map_voting',
current_voter enum ('team1', 'team2') NOT NULL DEFAULT 'team1',
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS games (
id INTEGER NOT NULL AUTOINCREMENT,
name TEXT NOT NULL,
image TEXT NOT NULL DEFAULT '',
team_size INTEGER NOT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS matchmaking (
id INTEGER NOT NULL AUTOINCREMENT,
team_id INTEGER NOT NULL,
game_id INTEGER NOT NULL,
created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
status enum ('searching', 'matched') DEFAULT 'searching',
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS cs2_maps (
id INTEGER NOT NULL AUTOINCREMENT,
name TEXT NOT NULL,
image_url TEXT DEFAULT NULL,
is_active tinyINTEGER DEFAULT 1,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS map_votes (
match_id INTEGER NOT NULL,
map_id INTEGER NOT NULL,
banned_by INTEGER DEFAULT NULL,
PRIMARY KEY (match_id, map_id),
);
CREATE TABLE IF NOT EXISTS players (
id INTEGER NOT NULL AUTOINCREMENT,
club_id TEXT DEFAULT NULL,
client_id TEXT DEFAULT NULL,
client_nick TEXT DEFAULT NULL,
email TEXT NOT NULL DEFAULT '',
client_hash TEXT DEFAULT NULL,
client_password TEXT DEFAULT NULL,
password TEXT NOT NULL DEFAULT '',
auth_token TEXT DEFAULT NULL,
auth_token_from_date DATETIME DEFAULT NULL,
client_status TEXT DEFAULT 'web',
change_password tinyINTEGER DEFAULT 0,
remember_token TEXT DEFAULT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS team_requests (
id bigINTEGER NOT NULL AUTOINCREMENT,
team_id INTEGER NOT NULL,
player_id INTEGER NOT NULL,
status enum ('pending', 'accepted', 'rejected') NOT NULL DEFAULT 'pending',
message TEXT DEFAULT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS team_members (
id bigINTEGER NOT NULL AUTOINCREMENT,
team_id INTEGER NOT NULL,
player_id INTEGER NOT NULL,
role enum ('captain', 'member') NOT NULL DEFAULT 'member',
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS join_requests (
id INTEGER NOT NULL AUTOINCREMENT,
player_id INTEGER NOT NULL,
team_id INTEGER NOT NULL,
status enum ('pending', 'accepted', 'rejected') DEFAULT 'pending',
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS __table_transfer_progress (
name TEXT NOT NULL,
lsn bigINTEGER DEFAULT NULL,
status enum ('SnapshotWait', 'SyncWait', 'InSync') DEFAULT NULL,
PRIMARY KEY (name),
);
CREATE TABLE IF NOT EXISTS team_invitations (
id bigINTEGER NOT NULL AUTOINCREMENT,
team_id INTEGER NOT NULL,
player_id INTEGER NOT NULL,
invited_by INTEGER NOT NULL,
status enum ('pending', 'accepted', 'rejected') NOT NULL DEFAULT 'pending',
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS sessions (
id TEXT NOT NULL,
user_id bigINTEGER DEFAULT NULL,
ip_address TEXT DEFAULT NULL,
user_agent TEXT DEFAULT NULL,
payload TEXT NOT NULL,
last_activity INTEGER NOT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS seasons (
id bigINTEGER NOT NULL AUTOINCREMENT,
name TEXT NOT NULL,
start_date DATETIME NOT NULL,
end_date DATETIME DEFAULT NULL,
is_active tinyINTEGER NOT NULL DEFAULT 1,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS script_log (
id INTEGER NOT NULL AUTOINCREMENT,
date DATETIME DEFAULT NULL,
script_name TEXT DEFAULT NULL,
log TEXT DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS rating (
id INTEGER NOT NULL AUTOINCREMENT,
club_id INTEGER DEFAULT NULL,
user_id INTEGER NOT NULL,
player_id INTEGER NOT NULL,
game_id TEXT DEFAULT NULL,
game_rating INTEGER DEFAULT NULL,
season_id bigINTEGER DEFAULT NULL,
game_now TEXT DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS personal_access_tokens (
id bigINTEGER NOT NULL AUTOINCREMENT,
tokenable_type TEXT NOT NULL,
tokenable_id bigINTEGER NOT NULL,
name TEXT NOT NULL,
token TEXT NOT NULL,
abilities TEXT DEFAULT NULL,
last_used_at TIMESTAMP NULL DEFAULT NULL,
expires_at TIMESTAMP NULL DEFAULT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS password_reset_tokens (
email TEXT NOT NULL,
token TEXT NOT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (email),
);
CREATE TABLE IF NOT EXISTS password_resets (
email TEXT NOT NULL,
token TEXT NOT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
);
CREATE TABLE IF NOT EXISTS migrations (
id INTEGER NOT NULL AUTOINCREMENT,
migration TEXT NOT NULL,
batch INTEGER NOT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS match_results (
id bigINTEGER NOT NULL AUTOINCREMENT,
match_id bigINTEGER NOT NULL,
user_id INTEGER NOT NULL,
player_id INTEGER DEFAULT NULL,
victory tinyINTEGER NOT NULL DEFAULT 0,
round INTEGER NOT NULL DEFAULT 1,
start_math tinyINTEGER NOT NULL DEFAULT 0,
add_score INTEGER NOT NULL DEFAULT 0,
details json DEFAULT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS matches_pubg (
id bigINTEGER NOT NULL AUTOINCREMENT,
club_id INTEGER DEFAULT 0,
player_id INTEGER DEFAULT NULL,
date DATETIME DEFAULT NULL,
log TEXT DEFAULT NULL,
log_gameover TEXT DEFAULT NULL,
victory INTEGER DEFAULT NULL,
score INTEGER DEFAULT NULL,
math_score TEXT DEFAULT '0/0',
date_scan DATETIME DEFAULT NULL,
col_scan INTEGER DEFAULT 0,
gamemode TEXT DEFAULT NULL,
mapname TEXT DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS matches_dota2 (
id bigINTEGER NOT NULL AUTOINCREMENT,
club_id INTEGER DEFAULT 0,
player_id INTEGER DEFAULT NULL,
date DATETIME DEFAULT NULL,
log longTEXT DEFAULT NULL,
log_gameover longTEXT DEFAULT NULL,
victory INTEGER DEFAULT NULL,
score INTEGER DEFAULT NULL,
math_score TEXT DEFAULT NULL,
match_id bigINTEGER DEFAULT NULL,
hero_id INTEGER DEFAULT NULL,
date_scan DATETIME DEFAULT NULL,
col_scan INTEGER DEFAULT 0,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS matches (
id bigINTEGER NOT NULL AUTOINCREMENT,
club_id INTEGER DEFAULT 0,
player_id INTEGER DEFAULT NULL,
date DATETIME DEFAULT NULL,
log TEXT DEFAULT NULL,
log_gameover TEXT DEFAULT NULL,
victory INTEGER DEFAULT 0,
score INTEGER DEFAULT 0,
math_score TEXT DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS live_match_ready (
match_id INTEGER NOT NULL,
player_id INTEGER NOT NULL,
is_ready tinyINTEGER NOT NULL DEFAULT 0,
PRIMARY KEY (match_id, player_id),
);
CREATE TABLE IF NOT EXISTS job_batches (
id TEXT NOT NULL,
name TEXT NOT NULL,
total_jobs INTEGER NOT NULL,
pending_jobs INTEGER NOT NULL,
failed_jobs INTEGER NOT NULL,
failed_job_ids longTEXT NOT NULL,
options mediumTEXT DEFAULT NULL,
cancelled_at INTEGER DEFAULT NULL,
created_at INTEGER NOT NULL,
finished_at INTEGER DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS jobs (
id bigINTEGER NOT NULL AUTOINCREMENT,
queue TEXT NOT NULL,
payload longTEXT NOT NULL,
attempts tinyINTEGER NOT NULL,
reserved_at INTEGER DEFAULT NULL,
available_at INTEGER NOT NULL,
created_at INTEGER NOT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS game_matches (
id bigINTEGER NOT NULL AUTOINCREMENT,
game_type TEXT NOT NULL,
session_id TEXT NOT NULL,
match_id TEXT DEFAULT NULL,
score INTEGER DEFAULT NULL,
match_score TEXT DEFAULT NULL,
date_scan DATETIME DEFAULT NULL,
col_scan INTEGER NOT NULL DEFAULT 0,
user_id INTEGER NOT NULL,
player_id INTEGER DEFAULT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS features (
id bigINTEGER NOT NULL AUTOINCREMENT,
name TEXT NOT NULL,
scope TEXT NOT NULL,
value TEXT NOT NULL,
created_at TIMESTAMP NULL DEFAULT NULL,
updated_at TIMESTAMP NULL DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS failed_jobs (
id bigINTEGER NOT NULL AUTOINCREMENT,
uuid TEXT NOT NULL,
`connection` TEXT NOT NULL,
queue TEXT NOT NULL,
payload longTEXT NOT NULL,
exception longTEXT NOT NULL,
failed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS events_season_rating (
id INTEGER NOT NULL AUTOINCREMENT,
event_id INTEGER DEFAULT NULL,
player_id INTEGER DEFAULT NULL,
rating INTEGER DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS events (
id INTEGER NOT NULL AUTOINCREMENT,
club_id INTEGER DEFAULT NULL,
game_id TEXT DEFAULT NULL,
type TEXT DEFAULT NULL,
name TEXT DEFAULT NULL,
date_from DATETIME DEFAULT NULL,
date_to DATETIME DEFAULT NULL,
token TEXT DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS clubs (
id INTEGER NOT NULL AUTOINCREMENT,
club_id INTEGER DEFAULT NULL,
club_name TEXT DEFAULT NULL,
PRIMARY KEY (id),
);
CREATE TABLE IF NOT EXISTS cache_locks (
`key` TEXT NOT NULL,
owner TEXT NOT NULL,
expiration INTEGER NOT NULL,
PRIMARY KEY (`key`),
);
CREATE TABLE IF NOT EXISTS cache (
`key` TEXT NOT NULL,
value mediumTEXT NOT NULL,
expiration INTEGER NOT NULL,
PRIMARY KEY (`key`),
);
INSERT INTO teams VALUES
INSERT INTO games VALUES
INSERT INTO players VALUES
INSERT INTO team_requests VALUES
INSERT INTO team_members VALUES
INSERT INTO team_invitations VALUES
INSERT INTO seasons VALUES
COMMIT;
PRAGMA foreign_keys = ON;