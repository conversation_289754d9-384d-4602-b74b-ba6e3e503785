<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Broadcasting\PrivateChannel;

class InvitationDeclined implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $teamId;
    public int $captainId;

    public function __construct(int $teamId, int $captainId)
    {
        $this->teamId = $teamId;
        $this->captainId = $captainId;
    }

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('player.' . $this->captainId),
        ];
    }

    public function broadcastAs(): string
    {
        return 'invitation.declined';
    }
}