<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Team;

class JoinRequestSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $teamId;
    public $team;
    public $playerId;

    public function __construct($teamId, $playerId = null)
    {
        $this->teamId = $teamId;
        $this->playerId = $playerId;
        $this->team = Team::find($teamId);
    }

    public function broadcastOn()
    {
        // Отправляем событие капитану команды
        return new PrivateChannel('player.' . $this->team->captain_id);
    }

    public function broadcastAs()
    {
        return 'join.request.sent';
    }
}
