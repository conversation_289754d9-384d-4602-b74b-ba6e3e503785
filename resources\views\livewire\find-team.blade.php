<div>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <a href="{{ route('create.team') }}" class="btn btn-sm btn-outline-primary" wire:navigate>
                            <i class="ri-arrow-left-line me-1"></i> Вернуться
                        </a>
                    </div>
                    <div class="card-body">
                        <livewire:carousel />
                        @if($selectedGameId)
                            <!-- Отображение команд пользователя -->
                            @if(\App\Models\User::find(\Auth::id())->hasTeamForGame($selectedGameId))
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Ваша команда</h6>
                                    </div>
                                    <div class="card-body">
                                        @php
                                            $userTeam = \App\Models\User::find(\Auth::id())->getTeamForGame($selectedGameId);
                                            $membership = \App\Models\User::find(\Auth::id())->getTeamMembershipForGame($selectedGameId);
                                        @endphp
                                        @if($userTeam)
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5>{{ $userTeam->name }}</h5>
                                                <span class="badge {{ $membership && $membership->role == 'captain' ? 'bg-danger' : 'bg-info' }}">
                                                    {{ $membership && $membership->role == 'captain' ? 'Капитан' : 'Участник' }}
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <p class="mb-1"><strong>Капитан:</strong> 
                                                        @if(isset($userTeam->captain))
                                                            {{ $userTeam->captain->client_nick }}
                                                        @else
                                                            Неизвестно
                                                        @endif
                                                    </p>
                                                    <p class="mb-0"><strong>Участников:</strong> {{ $userTeam->members_count }} / {{ $userTeam->game->team_size }}</p>
                                                </div>
                                                <div>
                                                    @if($membership && $membership->role == 'captain')
                                                        <a href="{{ route('create.team') }}" class="btn btn-primary" wire:navigate>
                                                            <i class="ri-settings-line me-1"></i> Управление командой
                                                        </a>
                                                    @else
                                                        <button class="btn btn-danger" wire:click="leaveTeam({{ $userTeam->id }})"
                                                                onclick="return confirm('Вы уверены, что хотите покинуть команду?')">
                                                            <i class="ri-logout-box-line me-1"></i> Покинуть команду
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif
    
                            <!-- Отображение отправленных заявок -->
                            @if(count($sentJoinRequests) > 0)
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Отправленные заявки</h6>
                                    </div>
                                    <ul class="list-group list-group-flush">
                                        @foreach($sentJoinRequests as $joinRequest)
                                            <li class="list-group-item">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <strong>{{ $joinRequest->team->name }}</strong>
                                                        <span class="text-muted ms-2">Статус: {{ $joinRequest->status == 'pending' ? 'Ожидает ответа' : $joinRequest->status }}</span>
                                                    </div>
                                                    <div>
                                                        <button class="btn btn-sm btn-warning" wire:click="cancelRequest({{ $joinRequest->team->id }})">
                                                            <i class="ri-close-line me-1"></i> Отменить заявку
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <!-- Отображение приглашений -->
                            @if(count($receivedInvitations) > 0)
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Приглашения в команды</h6>
                                    </div>
                                    <ul class="list-group list-group-flush">
                                        @foreach($receivedInvitations as $invitation)
                                            <li class="list-group-item">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <strong>{{ $invitation->team->name }}</strong>
                                                        <span class="text-muted ms-2">Капитан: {{ $invitation->team->captain->client_nick }}</span>
                                                    </div>
                                                    <div>
                                                        <button class="btn btn-sm btn-success me-2" wire:click="acceptInvitation({{ $invitation->id }})">
                                                            <i class="ri-check-line me-1"></i> Принять
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" wire:click="declineInvitation({{ $invitation->id }})">
                                                            <i class="ri-close-line me-1"></i> Отклонить
                                                        </button>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <div class="mb-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Поиск по названию команды" 
                                           wire:model.live.debounce.300ms="searchTerm">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="container py-4">
                                @include('livewire.notification')
                            </div>
                            
                            <div class="teams-list">
                                @forelse($teams as $team)
                                    <div class="card mb-3">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">{{ $team->name }}</h5>
                                            <span class="badge bg-primary">Рейтинг: {{ $team->rating }}</span>
                                        </div>
                                        <div class="card-body">
                                            @if($team->description)
                                                <p>{{ $team->description }}</p>
                                                <hr>
                                            @endif
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <p class="mb-1"><strong>Капитан:</strong> 
                                                        @if(isset($team->captain))
                                                            {{ $team->captain->client_nick }}
                                                        @else
                                                            Неизвестно
                                                        @endif
                                                    </p>
                                                    <p class="mb-0"><strong>Участников:</strong> {{ $team->members_count }} / {{ $team->game->team_size }}</p>
                                                </div>
                                                
                                                <div>
                                                    @if(in_array($team->id, $sentRequests))
                                                        <button class="btn btn-warning" wire:click="cancelRequest({{ $team->id }})">
                                                            <i class="ri-close-line me-1"></i> Отменить заявку
                                                        </button>
                                                    @else
                                                        <button class="btn btn-primary" wire:click="sendJoinRequest({{ $team->id }})">
                                                            <i class="ri-user-add-line me-1"></i> Подать заявку
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="alert alert-info text-center">
                                        <p class="mb-0">Команды не найдены. Попробуйте изменить параметры поиска или <a href="{{ route('create.team') }}" wire:navigate>создайте свою команду</a>.</p>
                                    </div>
                                @endforelse
                            </div>
                            
                            <div class="d-flex justify-content-center mt-4">
                                {{ $teams->links() }}
                            </div>
                        @else
                            <div class="alert alert-warning text-center">
                                <p class="mb-0">Пожалуйста, выберите игру для поиска команды.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@script
<script>
window.Echo.private(`player.{{ auth()->id() }}`)
    .listen('.join.request.rejected', (e) => {
        Livewire.dispatch('loadSentRequests');
        Livewire.dispatch('showNotification', {
            type: 'warning',
            message: 'Ваша заявка в команду "' + e.team.name + '" была отклонена'
        });
    })
    .listen('.join.request.accepted', (e) => {
        Livewire.dispatch('loadSentRequests');
        Livewire.dispatch('showNotification', {
            type: 'success',
            message: 'Ваша заявка в команду "' + e.team.name + '" была принята!'
        });
    });
</script>
@endscript




