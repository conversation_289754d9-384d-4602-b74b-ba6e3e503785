<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Создаем новую таблицу с нужной структурой
        Schema::create('team_members_new', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('team_id');
            $table->enum('role', ['captain', 'member']);
            $table->timestamps();

            $table->unique(['user_id', 'team_id']);
            $table->foreign('user_id')->references('id')->on('users')->onUpdate('restrict')->onDelete('restrict');
            $table->foreign('team_id')->references('id')->on('teams')->onUpdate('restrict')->onDelete('restrict');
        });

        // Копируем данные из старой таблицы в новую
        DB::statement('INSERT INTO team_members_new (user_id, team_id, role, created_at, updated_at) 
                      SELECT user_id, team_id, role, created_at, updated_at FROM team_members');

        // Удаляем старую таблицу
        Schema::dropIfExists('team_members');

        // Переименовываем новую таблицу
        Schema::rename('team_members_new', 'team_members');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Создаем временную таблицу со старой структурой
        Schema::create('team_members_old', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('team_id');
            $table->enum('role', ['captain', 'member']);
            $table->timestamps();

            $table->primary(['user_id', 'team_id']);
            $table->foreign('user_id')->references('id')->on('users')->onUpdate('restrict')->onDelete('restrict');
            $table->foreign('team_id')->references('id')->on('teams')->onUpdate('restrict')->onDelete('restrict');
        });

        // Копируем данные из текущей таблицы в старую
        DB::statement('INSERT INTO team_members_old (user_id, team_id, role, created_at, updated_at) 
                      SELECT user_id, team_id, role, created_at, updated_at FROM team_members');

        // Удаляем текущую таблицу
        Schema::dropIfExists('team_members');

        // Переименовываем старую таблицу
        Schema::rename('team_members_old', 'team_members');
    }
};