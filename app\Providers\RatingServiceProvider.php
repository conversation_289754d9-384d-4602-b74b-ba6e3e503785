<?php

namespace App\Providers;

use App\Services\RatingService;
use Illuminate\Support\ServiceProvider;
use Laravel\Pennant\Feature;
use Illuminate\Support\Lottery;

class RatingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(RatingService::class, function ($app) {
            return new RatingService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Определяем фичу для новой системы рейтинга
        Feature::define('enhanced-rating', fn () => true);
        
        // Определяем фичу для сезонного сброса рейтинга
        Feature::define('seasonal-reset', fn () => true);
        
        // Определяем фичу для замедления роста рейтинга
        Feature::define('rating-slowdown', fn () => true);
    }
}