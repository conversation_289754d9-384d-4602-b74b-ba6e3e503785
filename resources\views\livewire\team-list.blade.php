<div>  
    {{-- Компонент уведомлений --}}
    @include('livewire.notification')  
     
    @if ($gameId) {{-- Проверяем, выбрана ли игра --}}
        @if ($hasTeamInGame)
            <div class="mt-4">
                @foreach ($userTeams as $team)
                <div class="d-flex justify-content-center gap-2 mb-4">
                    @php
                        $requiredSize = app(
                            \App\Services\Interfaces\TeamManagerInterface::class,
                        )->getRequiredTeamSize($team['game_id']);
                        $isTeamFull = count($teamMembers[$team['id']] ?? []) >= $requiredSize;
                    @endphp

                    @if ($team['captain_id'] == $currentUserId)
                        <div class="position-relative d-inline-block" x-data="{ showTooltip: false }"
                            @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                            @focus="showTooltip = true" @blur="showTooltip = false">
                            {{-- <button type="button" class="btn btn-success"
                                wire:click="createNewMatch({{ $team['id'] }})"
                                @if (!$isTeamFull) disabled @endif>
                                <i class="ri-game-line me-1"></i> Создать новый матч
                            </button> --}}
                            @if (!$isTeamFull)
                                <div x-show="showTooltip" class="tooltip-alpine" x-transition>Для создания
                                    матча необходимо {{ $requiredSize }} игроков в команде</div>
                            @endif
                        </div>
                        <div class="position-relative d-inline-block" x-data="{ showTooltip: false }"
                            @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                            @focus="showTooltip = true" @blur="showTooltip = false">
                            <button class="btn btn-success"
                                wire:click="findOpponent({{ $team['id'] }})"
                                @if (!$isTeamFull) disabled @endif>
                                <i class="ri-user-add-line me-1"></i> Найти соперника
                            </button>
                            @if (!$isTeamFull)
                                <div x-show="showTooltip" class="tooltip-alpine" x-transition>Для поиска
                                    соперника необходимо {{ $requiredSize }} игроков в команде</div>
                            @endif
                        </div>  
                    @endif
                    
                </div>

                <h4 class="text-center mb-4">Ваша команда в игре {{ $gameName }}</h4>
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ $team['name'] }}
                                @if ($team['captain_id'] == $currentUserId)
                                    <small class="text-muted">(Вы - Капитан)</small>
                                @else
                                    <small class="text-muted">(Капитан: {{ $team['captain']['client_nick'] }})</small>
                                @endif
                            </h5>
                            {{-- Используем $currentUserId для определения роли --}}
                            <span class="badge bg-primary">
                                @if ($team['captain_id'] == $currentUserId)
                                    Капитан
                                @else
                                    Участник
                                @endif
                            </span>
                        </div>
                        <div class="card-body">
                            @if (isset($team['description']) && $team['description'])
                                <p class="card-text">{{ $team['description'] }}</p>
                                <hr>
                            @endif

                            <h6 class="mb-3">Статистика команды</h6>
                            <div class="row mb-4">
                                <div class="col-md-4 text-center">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h3>{{ $teamStats[$team['id']]['rating'] ?? 0 }}</h3>
                                            <small>Рейтинг</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h3>{{ $teamStats[$team['id']]['matches'] ?? 0 }}</h3>
                                            <small>Матчей</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h3>{{ $teamStats[$team['id']]['wins'] ?? 0 }}</h3>
                                            <small>Побед</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h6 class="mb-3">Участники команды</h6>
                            <ul class="list-group">
                                @foreach ($teamMembers[$team['id']] ?? [] as $member)
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            @if (isset($member['user']['avatar']))
                                                <img src="{{ asset('images/avatars/' . $member['user']['avatar']) }}" alt="Аватар"
                                                    class="rounded-circle me-2" style="width: 24px; height: 24px;">
                                            @else
                                                <svg class="rounded-circle me-2 bg-secondary"
                                                    style="width: 24px; height: 24px;" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path
                                                        d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                                </svg>
                                            @endif
                                            <span class="me-2 {{ $member['user']['is_online'] ? 'bg-success' : 'bg-secondary' }}"
                                                style="
                                                    display: inline-block;
                                                    width: 10px;
                                                    height: 10px;
                                                    border-radius: 50%;
                                                "
                                                title="{{ $member['user']['is_online'] ? 'Онлайн' : 'Офлайн' }}">
                                            </span>
                                            {{ $member['user']['client_nick'] }}                                            
                                        </div>
                                        <div class="d-flex align-items-center">
                                            
                                            <span
                                                class="badge {{ $member['role'] == 'captain' ? 'bg-danger' : 'bg-info' }} rounded-pill me-2">
                                                {{ $member['role'] == 'captain' ? 'Капитан' : 'Участник' }}
                                            </span>

                                            {{-- Проверка на то, является ли текущий пользователь капитаном этой команды --}}
                                            @if ($team['captain_id'] == $currentUserId && $member['role'] != 'captain')
                                                <div class="position-relative d-inline-block" x-data="{ showTooltip: false }"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                                    @focus="showTooltip = true" @blur="showTooltip = false">
                                                    <button class="btn btn-sm btn-outline-danger"
                                                        wire:click="$dispatch('open-kick-confirm-modal', { teamId: {{ $team['id'] }}, userId: {{ $member['player_id'] }} })">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                    <div x-show="showTooltip" class="tooltip-alpine" x-transition>Исключить игрока из команды</div>
                                                </div>
                                            @endif
                                        </div>
                                    </li>
                                @endforeach
                            </ul>

                            {{-- Управление командой для капитана --}}
                            @if ($team['captain_id'] == $currentUserId)
                                <h6 class="mb-3 mt-4">Управление командой</h6>                               

                                {{-- Заявки на вступление --}}
                                @if (isset($pendingRequests[$team['id']]) && count($pendingRequests[$team['id']]) > 0)
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Заявки на вступление</h6>
                                        </div>
                                        <ul class="list-group list-group-flush">
                                            @foreach ($pendingRequests[$team['id']] as $request)
                                                <li
                                                    class="list-group-item d-flex justify-content-between align-items-center">
                                                    <div>
                                                        @if (isset($request['user']['avatar']))
                                                            <img src="{{ asset($request['user']['avatar']) }}"
                                                                alt="Аватар" class="rounded-circle me-2"
                                                                style="width: 24px; height: 24px;">
                                                        @else
                                                            <svg class="rounded-circle me-2 bg-secondary"
                                                                style="width: 24px; height: 24px;" fill="currentColor"
                                                                viewBox="0 0 24 24">
                                                                <path
                                                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                                            </svg>
                                                        @endif
                                                        {{ $request['user']['client_nick'] }}
                                                        <span class="badge bg-info text-dark ms-2">Хочет вступить</span>
                                                    </div>
                                                    <div>
                                                        <button class="btn btn-sm btn-success me-2"
                                                            wire:click="acceptRequest({{ $request['id'] }})">
                                                            <i class="ri-check-line"></i> Принять
                                                        </button>
                                                        <button class="btn btn-sm btn-danger"
                                                            wire:click="rejectRequest({{ $request['id'] }})">
                                                            <i class="ri-close-line"></i> Отклонить
                                                        </button>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif


                                 <div class="d-flex justify-content-end gap-2 mt-3 flex-wrap">
                                     @if (!$isTeamFull)
                                         <button class="btn btn-primary" wire:click="invitePlayer({{ $team['id'] }})">
                                             <i class="ri-user-add-line me-1"></i> Пригласить игрока
                                         </button>
                                     @endif
                                     <button class="btn btn-danger"
                                         wire:click="$dispatch('open-disband-confirm-modal', { teamId: {{ $team['id'] }} })">
                                         <i class="ri-delete-bin-line me-1"></i> Расформировать команду
                                     </button>
                                 </div>
                             @endif

                             {{-- Кнопка "Покинуть команду" для обычных участников --}}
                             @if ($team['captain_id'] != $currentUserId)
                                 <div class="d-flex justify-content-end mt-4">
                                     <button class="btn btn-outline-danger"
                                         wire:click="$dispatch('open-leave-confirm-modal', { teamId: {{ $team['id'] }} })">
                                         <i class="ri-logout-box-line me-1"></i> Покинуть команду
                                     </button>
                                 </div>
                             @endif
                         </div>
                     </div>
                 @endforeach
             </div>
         @else
            <div class="mt-4 text-center">
                <p>У вас нет команды в игре {{ $gameName }}.</p>
                <div class="d-flex justify-content-center gap-2">
                    {{-- Кнопки "Найти команду" и "Создать команду" --}}
                    <button wire:click="$dispatch('findTeamInGame', { gameId: {{ json_encode($gameId) }} })"
                        class="btn btn-primary">Найти команду</button>
                    <button type="button" class="btn btn-success" wire:click="triggerCreateTeamModal">
                        Создать команду
                    </button>
                </div>
            </div>
        @endif
    @else
        <div class="mt-4 text-center">
            <p>Выберите игру, чтобы увидеть информацию о команде или создать новую.</p>
        </div>
    @endif
    
    {{-- Модальные окна подтверждения (вынесены в TeamList, так как содержат логику kickMember и disbandTeam) --}}
    
    {{-- Модальное окно для исключения участника --}}
    <div x-data="{
        open: false,
        teamId: null,
        userId: null,
        init() {
            window.addEventListener('open-kick-confirm-modal', (e) => {
                this.teamId = e.detail.teamId;
                this.userId = e.detail.userId;
                this.open = true;
                document.body.classList.add('modal-open');
            });
            this.$watch('open', value => {
                if (!value) {
                    document.body.classList.remove('modal-open');
                }
            });
        }
    }" @keydown.escape.window="open = false" class="modal fade" :class="{ 'show': open }"
        style="display: none;" :style="open ? 'display: block;' : 'display: none;'" tabindex="-1" aria-hidden="true"
        wire:ignore.self>
        <div class="modal-backdrop fade" :class="{ 'show': open }" x-show="open"
            x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" @click="open = false"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Подтверждение исключения</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Вы уверены, что хотите исключить этого участника из команды?</p>
                    <p class="text-danger">Это действие нельзя отменить.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                    <button type="button" class="btn btn-danger"
                        @click="open = false; $wire.kickMember(teamId, userId)">
                        Исключить
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    {{-- Модальное окно для расформирования команды --}}
    <div x-data="{
        open: false,
        teamId: null,
        init() {
            window.addEventListener('open-disband-confirm-modal', (e) => {
                this.teamId = e.detail.teamId;
                this.open = true;
                document.body.classList.add('modal-open');
            });
            this.$watch('open', value => {
                if (!value) {
                    document.body.classList.remove('modal-open');
                }
            });
        }
    }" @keydown.escape.window="open = false" class="modal fade" :class="{ 'show': open }"
        style="display: none;" :style="open ? 'display: block;' : 'display: none;'" tabindex="-1" aria-hidden="true"
        wire:ignore.self>
        <div class="modal-backdrop fade" :class="{ 'show': open }" x-show="open"
            x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" @click="open = false"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Подтверждение расформирования</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Вы уверены, что хотите расформировать команду?</p>
                    <p class="text-danger">Это действие нельзя отменить. Все участники будут удалены из команды.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                    <button type="button" class="btn btn-danger" @click="open = false; $wire.disbandTeam(teamId)">
                        Расформировать
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    {{-- Модальное окно для выхода из команды --}}
    <div x-data="{
        open: false,
        teamId: null,
        init() {
            console.log('Leave Team Modal Alpine.js initialized'); 
            window.addEventListener('open-leave-confirm-modal', (e) => {
                console.log('open-leave-confirm-modal event received', e.detail);
                this.teamId = e.detail.teamId;
                this.open = true;
                document.body.classList.add('modal-open');
            });
            this.$watch('open', value => {
                if (!value) {
                    document.body.classList.remove('modal-open');
                }
            });
        }
    }" @keydown.escape.window="open = false" class="modal fade" :class="{ 'show': open }"
        style="display: none;" :style="open ? 'display: block;' : 'display: none;'" tabindex="-1" aria-hidden="true"
        wire:ignore.self>
        <div class="modal-backdrop fade" :class="{ 'show': open }" x-show="open"
            x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" @click="open = false"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Подтверждение выхода</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Вы уверены, что хотите покинуть эту команду?</p>
                    <p class="text-danger">Это действие нельзя отменить.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                    <button type="button" class="btn btn-danger" @click="open = false; $wire.leaveTeam(teamId)">
                        Покинуть
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>