<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * Базовый тест для проверки работы SQLite.
     */
    public function test_sqlite_database_works(): void
    {
        // Проверяем, что таблица games существует и содержит данные
        $this->assertDatabaseHas('games', [
            'name' => 'CS2'
        ]);
        
        // Проверяем, что можем добавить запись в таблицу
        \DB::table('players')->insert([
            'client_nick' => 'test_player',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что запись добавлена
        $this->assertDatabaseHas('players', [
            'client_nick' => 'test_player'
        ]);
    }
}
