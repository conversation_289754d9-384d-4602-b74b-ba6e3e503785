[class^='ri-'],
[class*=' ri-'] {
  font-family: 'remixicon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1;
  vertical-align: middle;
  &:before {
    display: inline-block;
  }
  &.ri-24px::before {
    font-size: 24px;
  }
}

.ri-10px {
  &,
  &:before {
    font-size: 10px;
  }
}
.ri-12px {
  &,
  &:before {
    font-size: 12px;
  }
}
.ri-14px {
  &,
  &:before {
    font-size: 14px;
  }
}
.ri-16px {
  &,
  &:before {
    font-size: 16px;
  }
}
.ri-18px {
  &,
  &:before {
    font-size: 18px;
  }
}
.ri-20px {
  &,
  &:before {
    font-size: 20px;
  }
}
.ri-22px {
  &,
  &:before {
    font-size: 22px;
  }
}
.ri-24px {
  &,
  &:before {
    font-size: 24px;
  }
}
.ri-26px {
  &,
  &:before {
    font-size: 26px;
  }
}
.ri-28px {
  &,
  &:before {
    font-size: 28px;
  }
}
.ri-30px {
  &,
  &:before {
    font-size: 30px;
  }
}
.ri-32px {
  &,
  &:before {
    font-size: 32px;
  }
}
.ri-36px {
  &,
  &:before {
    font-size: 36px;
  }
}
.ri-40px {
  &,
  &:before {
    font-size: 40px;
  }
}
.ri-42px {
  &,
  &:before {
    font-size: 42px;
  }
}
.ri-48px {
  &,
  &:before {
    font-size: 48px;
  }
}

.ri-rotate-45:before {
  transform: rotate(45deg);
}

.ri-rotate-90:before {
  transform: rotate(90deg);
}

.ri-rotate-135:before {
  transform: rotate(135deg);
}

.ri-rotate-180:before {
  transform: rotate(180deg);
}

.ri-rotate-225:before {
  transform: rotate(225deg);
}

.ri-rotate-270:before {
  transform: rotate(270deg);
}

.ri-rotate-315:before {
  transform: rotate(315deg);
}

.ri-flip-h:before {
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: 'FlipH';
}

.ri-flip-v:before {
  transform: scaleY(-1);
  filter: FlipV;
  -ms-filter: 'FlipV';
}
.ri-spin:before {
  animation: ri-spin 2s infinite linear;
}

@keyframes ri-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}
@import '../node_modules/remixicon/fonts/remixicon';
