<?php

namespace App\Services;

use App\Services\Interfaces\RequestManagerInterface;
use App\Services\Interfaces\TeamManagerInterface; // Нам понадобится TeamService для проверки капитана
use App\Models\JoinRequest;
use App\Models\TeamMember;
use App\Models\Team; // Для проверки игры команды
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class RequestService implements RequestManagerInterface
{
    protected TeamManagerInterface $teamService;

    public function __construct(TeamManagerInterface $teamService)
    {
        $this->teamService = $teamService;
    }

    /**
     * Принимает заявку на вступление в команду.
     *
     * @param int $requestId ID заявки
     * @param int $captainId ID капитана, который принимает заявку
     * @return bool
     */
    public function acceptRequest(int $requestId, int $captainId): bool
    {
        try {
            DB::beginTransaction();
            
            $request = JoinRequest::find($requestId);

            if (!$request) {
                throw new \Exception('Заявка не найдена.');
            }

            if (!$this->teamService->isUserCaptainOfTeam($captainId, $request->team_id)) {
                throw new \Exception('Только капитан может принимать заявки.');
            }

            $playerId = $request->player_id;
            $teamId = $request->team_id;
            $team = Team::find($teamId);

            // Проверка, не полна ли команда
            if ($this->teamService->isTeamFull($teamId)) {
                throw new \Exception('Команда уже полная.');
            }

            // Проверяем, не состоит ли игрок уже в команде для этой игры
            if ($this->teamService->hasUserTeamForGame($playerId, $team->game_id)) {
                throw new \Exception('Игрок уже состоит в команде для этой игры.');
            }

            // Добавляем игрока в команду
            TeamMember::create([
                'team_id' => $teamId,
                'player_id' => $playerId,
                'role' => 'member'
            ]);

            // Обновляем статус заявки
            $request->status = 'accepted';
            $request->save();

            // Удаляем все остальные ожидающие приглашения для этого игрока в этой игре
            \App\Models\TeamInvitation::where('player_id', $playerId)
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->delete();

            // Отклоняем все ожидающие заявки от этого игрока в другие команды в этой игре
            JoinRequest::where('player_id', $playerId)
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->where('id', '!=', $requestId)
                ->update(['status' => 'rejected']);

            DB::commit();

            // Очищаем кэш для капитана и нового игрока
            Cache::forget("user_teams_{$captainId}_{$team->game_id}");
            Cache::forget("user_teams_{$playerId}_{$team->game_id}");
            Cache::forget("user_teams_{$captainId}_all_games");
            Cache::forget("user_teams_{$playerId}_all_games");

            // Уведомляем игрока о принятии заявки
            event(new \App\Events\JoinRequestAccepted($teamId, $playerId));
            // Уведомляем о новом участнике команды
            event(new \App\Events\TeamMemberJoined($teamId));

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при принятии заявки: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Отклоняет заявку на вступление в команду.
     *
     * @param int $requestId ID заявки
     * @param int $captainId ID капитана, который отклоняет заявку
     * @return bool
     */
    public function rejectRequest(int $requestId, int $captainId): bool
    {
        $request = JoinRequest::find($requestId);

        if (!$request) {
            throw new \Exception('Заявка не найдена.');
        }

        if (!$this->teamService->isUserCaptainOfTeam($captainId, $request->team_id)) {
            throw new \Exception('Только капитан может отклонять заявки.');
        }

        $playerId = $request->player_id;
        $teamId = $request->team_id;
        
        $request->status = 'rejected';
        $result = $request->save();
        
        // Отправляем Pusher событие игроку об отклонении заявки
        if ($result) {
            event(new \App\Events\JoinRequestRejected($teamId, $playerId));
        }
        
        return $result;
    }

    /**
     * Получает список ожидающих заявок на вступление для команды.
     *
     * @param int $teamId ID команды
     * @return \Illuminate\Support\Collection
     */
    public function getPendingRequestsForTeam(int $teamId): Collection
    {
        return JoinRequest::where('team_id', $teamId)
            ->where('status', 'pending')
            ->with('user:id,client_nick,avatar')
            ->get();
    }
}












