<?php

namespace App\Livewire;

use Livewire\Component;

class AlpineSelect extends Component
{
    public $options = [];
    public $selected = null;
    public $name = '';
    public $placeholder = 'Выберите...';
    public $label = '';
    public $required = false;
    public $itemValue = 'id';
    public $itemText = 'name';
    public $selectedText = ''; // Добавлено свойство для отображения выбранного значения
    public $search = ''; // Добавлено свойство для поиска

    public function mount($options = [], $selected = null, $name = '', $placeholder = '', $label = '', 
                         $required = false, $itemValue = 'id', $itemText = 'name')
    {
        $this->options = $options;
        $this->selected = $selected;
        $this->name = $name;
        $this->placeholder = $placeholder;
        $this->label = $label;
        $this->required = $required;
        $this->itemValue = $itemValue;
        $this->itemText = $itemText;
        
        // Отправляем событие о том, что компонент смонтирован, для синхронизации поиска
        $this->dispatch('search-updated', $this->search);
    }
    
    public function updatedSearch($value)
    {
        $this->dispatch('search-updated', $value);
    }
    
    public function updatedSelected($value)
    {
        if ($value) {
            $user = \App\Models\User::find($value);
            if ($user) {
                $this->selectedText = $user->client_nick;
            }
        }
    }

    public function updSelected($value)
    {
        $this->selected = $value;
        
        // Отправляем событие с выбранным значением
        $this->dispatch('select-changed', [
            'name' => $this->name,
            'value' => $value
        ]);
        
        // Обновляем текст выбранного элемента
        $this->updatedSelected($value);
    }

    public function render()
    {
        return view('livewire.alpine-select');
    }
}