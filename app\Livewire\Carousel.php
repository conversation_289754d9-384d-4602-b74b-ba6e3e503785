<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Game;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Cache;

class Carousel extends Component
{
    public $selectedGameId = null;
    public $games = []; 

    public function mount($gameId = null)  
    {
        // Приоритет: 1) переданный gameId, 2) значение из сессии, 3) первая игра
        if ($gameId) {
            $this->selectedGameId = $gameId;
        } 
        elseif (session()->has('selectedGameId')) {
            $this->selectedGameId = session('selectedGameId');
        }
        else {
            $this->selectedGameId = Game::first()->id;
        }
        
        // Обновляем сессию с текущим значением
        session(['selectedGameId' => $this->selectedGameId]);
        
        // Загружаем игры сразу при монтировании
        $this->loadGames();
    }
    
    protected function loadGames()
    {
        // Кэшируем список игр на 1 час, так как они редко меняются
        $this->games = Cache::remember('games_list', now()->addHour(), function() {
            return Game::select('id', 'name', 'team_size', 'image')
                ->get()
                ->map(function($game) {
                    return [
                        'id' => $game->id,
                        'title' => $game->name,
                        'description' => 'Team size: ' . $game->team_size,
                        'image' => asset('images/' . $game->image),
                    ];
                })
                ->values()
                ->toArray();
        });
    }

    #[On('gameSelected')]
    public function selectGame($gameId)
    {
        if ($this->selectedGameId == $gameId) {
            return;
        }
        
        $this->selectedGameId = $gameId;
        
        // Сохраняем выбранную игру в сессии
        session(['selectedGameId' => $gameId]);
        
        // Отправляем событие
        $this->dispatch('gameChanged', gameId: $gameId);
    }

    public function render()
    {
        return view('livewire.carousel');
    }
}










