<?php

namespace App\Livewire;

use App\Models\Game;
use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

// Импортируем сервис
use App\Services\Interfaces\InvitationManagerInterface;

class ReceivedInvitationsList extends Component
{
    public $userId; // ID пользователя, для которого загружаем приглашения
    public array $receivedInvitations = [];
    public array $invitations = []; // Переименовано для ясности

    protected InvitationManagerInterface $invitationService;

    protected $listeners = [
        'loadReceivedInvitations' => 'loadReceivedInvitations',
        'invitation-cancelled' => 'loadReceivedInvitations', // Слушатель для отмены приглашения
        'invitation-received' => 'loadReceivedInvitations', // Слушатель для получения нового приглашения
    ];

    public function boot(InvitationManagerInterface $invitationService)
    {
        $this->invitationService = $invitationService;
    }

    /**
     * Mount lifecycle hook. Загружаем полученные приглашения.
     */    
    public function mount()
    {
        $this->loadReceivedInvitations();
        $this->userId = Auth::id(); // Сохраняем ID пользователя для использования в слушателях
    }

    /**
     * Слушатель события для обновления списка полученных приглашений.
     */
    #[On('accepted_invitation')] // Когда приглашение принято
    #[On('declined_invitation')] // Когда приглашение отклонено
    public function loadReceivedInvitations($data = null)
    {
        Log::info('ReceivedInvitationsList: loadReceivedInvitations вызван', ['data_param' => $data]);
        Log::info('ReceivedInvitationsList: Auth::id()', ['id' => Auth::id()]);
        Log::info('ReceivedInvitationsList: Обновление списка приглашений');
        if (Auth::check()) {
            $rawInvitations = $this->invitationService->getReceivedInvitationsForPlayer(Auth::id());
            $this->receivedInvitations = array_values($rawInvitations->toArray());

            // Добавляем статус команды к каждому приглашению
            $this->invitations = $rawInvitations->map(function ($invitation) {
                $invitation->load('team.members', 'team.game', 'team.captain');
                $team = $invitation->team;
                $isTeamFull = false;

                if ($team) {
                    $currentMembersCount = $team->members->count();
                    // Если капитан не является членом команды, добавляем его к счетчику
                    if (!$team->members->contains('player_id', $team->captain_id)) {
                        $currentMembersCount++;
                    }

                    $requiredTeamSize = 0;
                    if ($team->game && $team->game->team_size) {
                        $requiredTeamSize = $team->game->team_size;
                    } else {
                        // Fallback для игр, у которых нет явного размера команды
                        $fallbackSize = match ((int)$team->game_id) {
                            3 => 4, // PUBG
                            default => 5, // CS2 и Dota2
                        };
                        $requiredTeamSize = $fallbackSize;
                    }
                    $isTeamFull = $currentMembersCount >= $requiredTeamSize;
                }
                $invitation->is_team_full = $isTeamFull;
                return $invitation;
            })->toArray();
        } else {
            $this->receivedInvitations = [];
            $this->invitations = [];
        }
        Log::info('ReceivedInvitationsList: receivedInvitations после загрузки', [
            'count' => count($this->receivedInvitations),
            'data' => $this->receivedInvitations,
            'invitations_with_team_status' => $this->invitations
        ]);
    }


    /**
     * Принимает приглашение в команду.
     */
    public function acceptInvitation(int $invitationId)
    {
        Log::info('ReceivedInvitationsList: acceptInvitation вызван', ['invitation_id' => $invitationId, 'user_id' => Auth::id()]);
        try {
            $this->invitationService->acceptInvitation($invitationId, Auth::id());

            // Обновляем список приглашений
            $this->loadReceivedInvitations();

            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение принято. Вы вступили в команду!'
            ]);
            Log::info('ReceivedInvitationsList: Диспатчим accepted_invitation и refreshTeamList');
            $this->dispatch('accepted_invitation'); // Оповещаем другие компоненты об изменении
            $this->dispatch('refreshTeamList'); // Оповещаем TeamList, чтобы обновить данные команды
            $this->dispatch('$refresh'); // Принудительно обновляем компонент
        } catch (\Exception $e) {
            Log::error('Ошибка при принятии приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Отклоняет приглашение в команду.
     */
    public function declineInvitation(int $invitationId)
    {
        try {
            $this->invitationService->declineInvitation($invitationId, Auth::id());

            // Обновляем список приглашений
            $this->loadReceivedInvitations();

            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение отклонено.'
            ]);
            $this->dispatch('declined_invitation'); // Оповещаем другие компоненты об изменении
            $this->dispatch('$refresh'); // Принудительно обновляем компонент
        } catch (\Exception $e) {
            Log::error('Ошибка при отклонении приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.received-invitations-list', [
            'invitations' => $this->invitations,
        ]);
    }
}
