<?php

namespace App\Livewire;

use Livewire\Component;

class PlayerSelect extends Component
{
    public $options = [];
    public $selected = null;
    public $name = '';
    public $placeholder = 'Выберите игрока...';
    public $label = 'Имя игрока';
    public $required = true;
    public $itemValue = 'id';
    public $itemText = 'name';

    public function mount($options = [], $selected = null, $name = 'playerName')
    {
        $this->options = $options;
        $this->selected = $selected;
        $this->name = $name;
    }

    public function updatedSelected($value)
    {
        $this->dispatch('player-selected', [
            'value' => $value
        ]);
    }

    public function render()
    {
        return view('livewire.player-select');
    }
}