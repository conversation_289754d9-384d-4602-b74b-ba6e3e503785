# WebRGTournament - Платформа для организации турниров

## О проекте

WebRGTournament - это веб-приложение для организации и управления турнирами по различным играм. Платформа позволяет создавать команды, приглашать игроков, регистрироваться на турниры и отслеживать результаты соревнований.

## Основные возможности

- Регистрация и авторизация пользователей
- Создание и управление командами
- Система приглашений игроков в команды
- Организация турниров с различными форматами
- Отслеживание статистики игроков и команд
- Административная панель для управления турнирами
- Система матчмейкинга для поиска соперников
- Рейтинговая система с сезонными сбросами
- Трансляция событий матча в реальном времени
- Интеграция с внешними API игровых серверов
- Голосование за карты перед матчами
- Система проверки готовности игроков (ready-check)
- Система уведомлений

### Планируемые возможности

- Голосование за карты перед матчами
- Проверка готовности игроков (ready-check)

## Технический стек

- Laravel 12.x
- PHP 8.2+
- MySQL/MariaDB
- Livewire 3.x
- Boostrap 5.3

## Установка и настройка

### Требования

- PHP 8.2 или выше
- Composer
- Node.js и npm
- MySQL или MariaDB

## Тестирование

Тестирование в этом проекте отличается от стандартного подхода Laravel следующими особенностями:

- **Использование SQLite в памяти** вместо стандартной БД
- **Ручное создание таблиц через SQL** вместо миграций
- **Фейковые процессы** для имитации внешних API
- **Самодостаточные тесты**, создающие все необходимые данные
- **Вспомогательные методы** для создания тестовых данных
- **Упрощенные тесты** с базовыми утверждениями
- **Тестирование Livewire компонентов**

Этот подход делает тесты более изолированными и независимыми, но требует больше кода для настройки тестового окружения в каждом тесте.