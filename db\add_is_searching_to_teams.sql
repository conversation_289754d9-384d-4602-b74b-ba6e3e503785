DELIMITER $$

DROP PROCEDURE IF EXISTS `add_is_searching_to_teams`$$

CREATE PROCEDURE `add_is_searching_to_teams`()
BEGIN
    -- Добавление поля is_searching, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = 'teams'
          AND COLUMN_NAME = 'is_searching'
    ) THEN
        ALTER TABLE teams
        ADD COLUMN is_searching BOOLEAN DEFAULT FALSE AFTER rating;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL add_is_searching_to_teams();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `add_is_searching_to_teams`;