<div class="container py-4">
    <div class="row">
        <!-- Основная информация о клубе -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title">{{ $club->name }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Игроки клуба -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>Игроки клуба</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Игрок</th>
                                    <th>Игра</th>
                                    <th>Рейтинг</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($players as $playerRatings)
                                    @php
                                        $player = $playerRatings->first()?->player;
                                    @endphp
                                    @if($player)
                                        <tr>
                                            <td>
                                                <a href="{{ route('player.show', ['player' => $player->id]) }}" class="text-decoration-none">
                                                    {{ $player->name }}
                                                </a>
                                            </td>
                                            <td>
                                                @foreach($playerRatings as $rating)
                                                    <div>{{ $rating->game?->name ?? 'N/A' }}: {{ $rating->game_rating }}</div>
                                                @endforeach
                                            </td>
                                            <td>
                                                @foreach($playerRatings as $rating)
                                                    <div>{{ $rating->game_rating }}</div>
                                                @endforeach
                                            </td>
                                        </tr>
                                    @endif
                                @empty
                                    <tr>
                                        <td colspan="3" class="text-center">Нет данных об игроках</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Компонент рейтинга клуба -->
    @livewire('club.rating', ['clubId' => $club->club_id, 'apiKey' => 'YOUR_API_KEY_HERE'])
</div>