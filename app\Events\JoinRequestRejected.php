<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Team;

class JoinRequestRejected implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $teamId;
    public $playerId;
    public $team;

    public function __construct($teamId, $playerId)
    {
        $this->teamId = $teamId;
        $this->playerId = $playerId;
        $this->team = Team::find($teamId);
    }

    public function broadcastOn()
    {
        return new PrivateChannel('player.' . $this->playerId);
    }

    public function broadcastAs()
    {
        return 'join.request.rejected';
    }
}