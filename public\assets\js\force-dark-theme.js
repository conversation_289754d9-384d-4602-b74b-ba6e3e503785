// Принудительное применение темной темы
(function() {
    // Функция для применения темной темы
    function applyDarkTheme() {
        // Установка темной темы на HTML элемент
        document.documentElement.classList.add('dark-style');
        document.documentElement.classList.remove('light-style');
        document.documentElement.setAttribute('data-style', 'dark');
        document.documentElement.setAttribute('data-theme', 'theme-default');
        
        // Удаление любых классов светлой темы
        const elementsWithLightClass = document.querySelectorAll('.light-style');
        elementsWithLightClass.forEach(el => {
            el.classList.remove('light-style');
            el.classList.add('dark-style');
        });
        
        // Установка темных стилей для body
        document.body.style.backgroundColor = '#232333';
        document.body.style.color = '#a3a4cc';
        
        // Обновление data-bs-theme для Bootstrap 5.3+ (если используется)
        document.documentElement.setAttribute('data-bs-theme', 'dark');
        
        // Обновление ссылок на CSS файлы темы, если они есть
        const themeStylesheet = document.querySelector('.template-customizer-theme-css');
        if (themeStylesheet && !themeStylesheet.href.includes('theme-dark')) {
            themeStylesheet.href = themeStylesheet.href.replace('theme-default', 'theme-dark-default');
        }
    }
    
    // Применяем тему сразу
    applyDarkTheme();
    
    // И также после загрузки DOM
    document.addEventListener('DOMContentLoaded', applyDarkTheme);
    
    // И после полной загрузки страницы
    window.addEventListener('load', applyDarkTheme);
})();
