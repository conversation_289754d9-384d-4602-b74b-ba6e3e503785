<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Team;
use App\Models\LiveMatch;
use App\Models\TeamMember;
use App\Events\MapBanned;
use App\Services\MapBanService;
use App\Exceptions\MapBanException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Livewire\Attributes\On;

class MapBan extends Component
{
    public LiveMatch $match;
    public Team $team;
    public $teamId;
    public $opponents;
    public $bannedMaps = [];
    public $availableMaps = [];
    public $currentVotingTeam = null;
    public $maxBans = 6;
    public ?string $serverConnectString = null;

    protected MapBanService $mapBanService;

    public function boot(MapBanService $mapBanService)
    {
        $this->mapBanService = $mapBanService;
    }

    public function mount(LiveMatch $match)
    {
        $this->match = $match->load(['team1', 'team2']);
        $this->authorize('viewBans', $this->match);

        $teamMember = TeamMember::where('player_id', Auth::id())->first();
        if (!$teamMember) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Вы не состоите ни в одной команде']);
            return;
        }

        $this->team = $teamMember->team;
        $this->teamId = $this->team->id;
        $this->opponents = $this->mapBanService->getOpponentTeam($this->match, $this->teamId);

        $this->loadMatchState();
    }

    protected function loadMatchState()
    {
        $state = $this->mapBanService->getMatchBanState($this->match);
        $this->bannedMaps = $state['banned_maps'];
        $this->availableMaps = $state['available_maps'];
        $this->currentVotingTeam = $state['current_voting_team'];
        $this->checkIfBanningComplete();
    }

    #[On('map.banned')]
    public function handleMapBanned(int $matchId)
    {
        if ($this->match->id !== $matchId) {
            return;
        }

        $this->loadMatchState();

        if ($this->currentVotingTeam && $this->currentVotingTeam->id === $this->teamId) {
            $this->dispatch('showNotification', ['type' => 'info', 'message' => 'Оппонент забанил карту. Теперь ваша очередь!']);
        } else {
            $this->dispatch('showNotification', ['type' => 'info', 'message' => 'Оппонент забанил карту. Ожидайте своей очереди.']);
        }
    }

    public function banMap($mapId)
    {
        $this->authorize('banMap', $this->match);

        try {
            $result = $this->mapBanService->banMap($this->match, $mapId, $this->teamId);

            if ($result['success']) {
                event(new MapBanned(
                    matchId: $this->match->id,
                    mapId: $mapId,
                    fromCaptainId: Auth::id(),
                    toCaptainId: $result['opponent_captain_id'],
                    nextVotingTeamId: $result['opponent_team_id']
                ));
                $this->loadMatchState();
                $this->checkIfBanningComplete();
            }
        } catch (MapBanException $e) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => $e->getMessage()]);
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Только капитан может банить карты']);
        }
    }

    public function unbanMap($mapId)
    {
        $this->authorize('unbanMap', $this->match);

        try {
            $success = $this->mapBanService->unbanMap($this->match, $mapId);
            if ($success) {
                $this->loadMatchState();
                $this->dispatch('showNotification', ['type' => 'success', 'message' => 'Карта успешно разбанена']);
            }
        } catch (MapBanException $e) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => $e->getMessage()]);
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Только капитан может разбанивать карты']);
        }
    }

    protected function checkIfBanningComplete()
    {
        try {
            $result = $this->mapBanService->checkBanningComplete($this->match, $this->maxBans);

            if ($result && $result['success']) {
                $this->serverConnectString = $result['server_addr'];
                $this->dispatch('showNotification', [
                    'type' => 'success',
                    'message' => "Финальная карта: {$result['final_map']->name}. Сервер поднят: {$result['server_addr']}"
                ]);
                $this->dispatch('mapVotingCompleted', [
                    'matchId' => $this->match->id,
                    'selectedMapId' => $result['final_map']->id,
                    'serverAddr' => $result['server_addr'],
                ]);
            }
        } catch (MapBanException $e) {
            Log::error('MapBan: Ошибка при завершении банов', [
                'match_id' => $this->match->id,
                'error' => $e->getMessage()
            ]);
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Не удалось запустить сервер. Возможно, все серверы заняты или произошла другая ошибка. Обратитесь к администратору.'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.map-ban');
    }
}
