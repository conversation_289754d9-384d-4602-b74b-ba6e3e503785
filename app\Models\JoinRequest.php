<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $player_id
 * @property int $team_id
 * @property string|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Team $team
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|JoinRequest whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class JoinRequest extends Model
{
    protected $table = 'join_requests';

    protected $fillable = [
        'player_id',
        'team_id',
        'status',
    ];

     /**
     * Get the team that owns the invitation.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }
    
    /**
     * Get the user that received the invitation.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}
