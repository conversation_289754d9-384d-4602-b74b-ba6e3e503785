<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Process;
use Tests\TestCase;

class ProcessFakeTest extends TestCase
{
    /**
     * Тест для проверки работы Process::fake()
     */
    public function test_process_fake_works(): void
    {
        // Фейковые процессы
        Process::fake();
        
        // Запускаем процесс
        $result = Process::run('ls -la');
        
        // Проверяем, что процесс был запущен
        Process::assertRan('ls -la');
        
        // Проверяем, что результат успешный
        $this->assertEquals(0, $result->exitCode());
    }
    
    /**
     * Тест для проверки работы Process::fake() с разными результатами
     */
    public function test_process_fake_with_custom_results(): void
    {
        // Фейковые процессы с разными результатами
        Process::fake([
            'ls -la' => Process::result(
                output: 'Fake directory listing',
                errorOutput: '',
                exitCode: 0
            ),
            'invalid-command' => Process::result(
                output: '',
                errorOutput: 'Command not found',
                exitCode: 127
            )
        ]);
        
        // Запускаем процессы
        $successResult = Process::run('ls -la');
        $failResult = Process::run('invalid-command');
        
        // Проверяем, что процессы были запущены
        Process::assertRan('ls -la');
        Process::assertRan('invalid-command');
        
        // Проверяем результаты
        $this->assertEquals(0, $successResult->exitCode());
        $this->assertEquals('Fake directory listing', trim($successResult->output()));
        
        $this->assertEquals(127, $failResult->exitCode());
        $this->assertEquals('Command not found', trim($failResult->errorOutput()));
    }
    
    /**
     * Тест для проверки работы Process::fake() с последовательностью результатов
     */
    public function test_process_fake_with_sequence(): void
    {
        // Фейковые процессы с последовательностью результатов
        Process::fake([
            'ls -la' => Process::sequence()
                ->push(Process::result('First result'))
                ->push(Process::result('Second result'))
        ]);
        
        // Запускаем процессы
        $firstResult = Process::run('ls -la');
        $secondResult = Process::run('ls -la');
        
        // Проверяем, что процессы были запущены
        Process::assertRanTimes('ls -la', 2);
        
        // Проверяем результаты
        $this->assertEquals('First result', trim($firstResult->output()));
        $this->assertEquals('Second result', trim($secondResult->output()));
    }
    
    /**
     * Тест для проверки работы Process::fake() с описанием асинхронного процесса
     */
    public function test_process_fake_with_description(): void
    {
        // Фейковые процессы с описанием асинхронного процесса
        Process::fake([
            'long-running-command' => Process::describe()
                ->output('First line of output')
                ->errorOutput('First line of error')
                ->output('Second line of output')
                ->exitCode(0)
                ->iterations(3)
        ]);
        
        // Запускаем асинхронный процесс
        $process = Process::start('long-running-command');
        
        // Проверяем, что процесс запущен
        $this->assertTrue($process->running());
        
        // Проверяем вывод
        $this->assertEquals('First line of output', trim($process->latestOutput()));
        $this->assertEquals('First line of error', trim($process->latestErrorOutput()));
        
        // Ждем завершения процесса
        $result = $process->wait();
        
        // Проверяем результат
        $this->assertEquals(0, $result->exitCode());
        $this->assertStringContainsString('Second line of output', $result->output());
    }
}
