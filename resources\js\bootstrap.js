import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

// Проверяем, нужно ли инициализировать Echo
const shouldInitializeEcho = () => {
    // Не инициализируем на страницах авторизации
    if (window.location.pathname.includes('auth-token')) {
        return false;
    }
    
    // Проверяем наличие необходимых переменных окружения
    if (!import.meta.env.VITE_REVERB_APP_KEY || !import.meta.env.VITE_REVERB_HOST || !import.meta.env.VITE_REVERB_PORT) {
        console.warn('Reverb environment variables not set');
        return false;
    }
    
    return true;
};

if (shouldInitializeEcho()) {
    window.Pusher = Pusher;
    Pusher.logToConsole = true; // Включаем логирование Pusher в консоль

    try {
        window.Echo = new Echo({
            broadcaster: 'reverb',
            key: import.meta.env.VITE_REVERB_APP_KEY,
            wsHost: import.meta.env.VITE_REVERB_HOST,
            wsPort: import.meta.env.VITE_REVERB_PORT ?? 8080,
            wssPort: import.meta.env.VITE_REVERB_PORT ?? 8080,
            forceTLS: false,
            enabledTransports: ['ws', 'wss'],
            auth: {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            },
        });
        
        console.log('Echo initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Echo:', error);
    }
} else {
    console.log('Echo initialization skipped');
}


if (window.Echo) {
    window.Echo.connector.pusher.connection.bind('connected', () => {
        console.log('Echo: Connected to Reverb!');
    });

    window.Echo.connector.pusher.connection.bind('disconnected', () => {
        console.warn('Echo: Disconnected from Reverb!');
    });

    window.Echo.connector.pusher.connection.bind('error', (err) => {
        console.error('Echo: Reverb connection error:', err);
    });

    window.Echo.connector.pusher.connection.bind('state_change', (states) => {
        console.log('Echo: Reverb state change:', states.current);
    });
}

// Твои существующие listen-еры остаются без изменений!
// Например:
// window.Echo.private(`player.${userId}`)
//     .listen('InvitationSent', (e) => {
//         console.log(e.invitation);
//     });



/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allow your team to quickly build robust real-time web applications.
 */

import './echo';
