<div>
    <div x-data="{ 
            open: false,
            init() {
                window.addEventListener('open-create-team-modal', (event) => {
                    if (event.detail && event.detail.gameId) {
                        @this.set('selectedGameId', event.detail.gameId);
                    }
                    this.open = true;
                    document.body.classList.add('modal-open');
                });
                window.addEventListener('closeCreateTeamModal', () => {
                    this.open = false;
                    document.body.classList.remove('modal-open');
                });
                this.$watch('open', value => {
                    if (!value) {
                        document.body.classList.remove('modal-open');
                    }
                });
            }
        }"
        @keydown.escape.window="open = false"
        class="modal fade"
        :class="{ 'show': open }"
        style="display: none;"
        :style="open ? 'display: block;' : 'display: none;'"
        tabindex="-1"
        aria-labelledby="createTeamModalLabel"
        aria-hidden="true"
        wire:ignore.self>
        
        <div class="modal-backdrop fade" 
             :class="{ 'show': open }"
             x-show="open" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="open = false"></div>
             
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="createTeamModalLabel">Создание новой команды</h1>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="saveTeam">
                        <div class="mb-3">
                            <label for="team-name" class="col-form-label">Название команды:</label>
                            <input type="text" class="form-control" id="team-name" wire:model="teamName">
                            @error('teamName') <div class="text-danger">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <label for="team-description" class="col-form-label">Описание:</label>
                            <textarea class="form-control" id="team-description" wire:model="teamDescription"></textarea>
                            @error('teamDescription') <div class="text-danger">{{ $message }}</div> @enderror
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                            <button type="submit" class="btn btn-primary">Создать</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>