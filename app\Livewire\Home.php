<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Rating;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\WithPagination;

#[Title('Главная')]
class Home extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    public $selectedGameId = null;
    public $perPage = 20;

    public function mount()
    {
        // Если selectedGameId пустой, выбираем первую игру
        if (!$this->selectedGameId) {
            $firstGame = \App\Models\Game::first();
            if ($firstGame) {
                $this->selectedGameId = $firstGame->id;
                // Сохраняем в сессии
                session(['selectedGameId' => $this->selectedGameId]);
            }
        }
    }
    
    #[On('gameChanged')]
    public function updateSelectedGame($gameId)
    {
        $this->selectedGameId = $gameId;
        // Сбрасываем пагинацию при изменении фильтра
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }
    
    public function render()
    {
        $ratingsQuery = Rating::select(['id', 'game_id', 'game_rating', 'club_id', 'player_id'])
            ->with([
                'player', 
                'game', 
                'club'
            ])
            ->orderBy('game_rating', 'desc');

        if ($this->selectedGameId) {
            $ratingsQuery->where('game_id', $this->selectedGameId);
        }

        $ratings = $ratingsQuery->paginate($this->perPage);

        return view('livewire.home', compact('ratings'));
    }
}










