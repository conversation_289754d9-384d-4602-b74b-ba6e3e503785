<div>
    @if(count($invitations) > 0)
        <div class="card mb-4 mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Приглашения в команды</h6>
            </div>
            <ul class="list-group list-group-flush">
                @foreach($invitations as $invitation)
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                @if(isset($invitation['team']))
                                    <strong>{{ $invitation['team']['name'] ?? 'Неизвестная команда' }}</strong>
                                    <span class="text-muted ms-2">Капитан: {{ $invitation['team']['captain']['client_nick'] ?? 'Неизвестный капитан' }}</span>
                                @else
                                    <strong>Неизвестная команда</strong>
                                    <span class="text-muted ms-2">Капитан: Неизвестный капитан</span>
                                @endif
                                @if($invitation['is_team_full'])
                                    <span class="badge bg-warning ms-2">Команда полна</span>
                                @endif
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success me-2"
                                        wire:click="acceptInvitation({{ $invitation['id'] }})"
                                        @if($invitation['is_team_full']) disabled @endif>
                                    <i class="ri-check-line me-1"></i> Принять
                                </button>
                                <button class="btn btn-sm btn-danger" wire:click="declineInvitation({{ $invitation['id'] }})">
                                    <i class="ri-close-line me-1"></i> Отклонить
                                </button>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>

{{-- @script
<script>
    window.Echo.private(`player.` + $wire.userId)
    .listen('.team.invitation.sent', (event) => {
        console.log('Получено новое приглашение:', event);
        $wire.loadReceivedInvitations();
    })
    .listen('.team.invitation.cancelled', (event) => {
        console.log('Приглашение отменено:', event);
        $wire.loadReceivedInvitations();
    });
</script>
@endscript --}}
