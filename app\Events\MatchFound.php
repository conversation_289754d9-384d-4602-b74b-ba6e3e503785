<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

class MatchFound implements ShouldBroadcast
{
    use SerializesModels;

    public $message;

    public function __construct(string $message)
    {
        $this->message = $message;
    }

    public function broadcastOn()
    {
        return new Channel('match-channel'); // Используй тот же канал, что и в app.js
    }

    public function broadcastWith()
    {
        return [
            'message' => $this->message,
        ];
    }
}
