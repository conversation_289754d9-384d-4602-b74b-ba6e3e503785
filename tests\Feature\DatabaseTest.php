<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class DatabaseTest extends TestCase
{
    /**
     * Проверяет, что таблица players создается.
     */
    public function test_players_table_exists(): void
    {
        $this->assertTrue(Schema::hasTable('players'));
    }
    
    /**
     * Проверяет, что можем добавить запись в таблицу players.
     */
    public function test_can_insert_into_players_table(): void
    {
        \DB::table('players')->insert([
            'client_nick' => 'test_player',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        $this->assertDatabaseHas('players', [
            'client_nick' => 'test_player'
        ]);
    }
}

