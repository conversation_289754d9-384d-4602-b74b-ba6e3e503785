<div>
    <div class="container py-4">
        @include('livewire.notification')
        <h1>Матчи</h1>
        
        <div class="mb-3">
            <button class="btn btn-primary" wire:click="$set('showCreateForm', true)">Создать новый матч</button>
        </div>
        
        @if($showCreateForm)
            <div class="card mb-4">
                <div class="card-header">Создать новый матч</div>
                <div class="card-body">
                    <form wire:submit.prevent="createMatch">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="gameType" class="form-label">Тип игры</label>
                                <select id="gameType" class="form-select" wire:model="newMatch.game_type">
                                    <option value="cs2">CS2</option>
                                    <option value="dota2" disabled>Dota 2</option>
                                    <option value="pubg" disabled>PUBG</option>
                                </select>
                                @error('newMatch.game_type') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="sessionId" class="form-label">ID сессии</label>
                                <input type="text" id="sessionId" class="form-control" wire:model="newMatch.session_id">
                                @error('newMatch.session_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="matchId" class="form-label">ID матча</label>
                                <input type="text" id="matchId" class="form-control" wire:model="newMatch.match_id">
                                @error('newMatch.match_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        @if($newMatch['game_type'] === 'cs2')
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5>Бан карт</h5>
                                    <p class="text-muted">Выберите до {{ $maxBans }} карт для бана</p>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6>Доступные карты</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mt-4">
                                                @foreach($availableMaps as $map)
                                                    @if(!in_array($map['id'], $bannedMaps))
                                                        <div class="col-md-4 mb-3">
                                                            <div class="card h-100 position-relative">
                                                                @if($map['image_url'])
                                                                    <img src="{{ asset($map['image_url']) }}" class="card-img-top" alt="{{ $map['name'] }}">
                                                                @endif
                                                                <div class="card-body position-absolute bottom-0 start-0 w-100" style="background: rgba(0, 0, 0, 0.7);">
                                                                    <h6 class="card-title text-center text-white mb-2">{{ $map['name'] }}</h6>
                                                                    <button type="button" class="btn btn-danger btn-sm w-100" wire:click="banMap({{ $map['id'] }})">
                                                                        Забанить
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6>Забаненные карты</h6>
                                        </div>
                                        <div class="card-body mt-4">
                                            <div class="row">
                                                @foreach($availableMaps as $map)
                                                    @if(in_array($map['id'], $bannedMaps))
                                                        <div class="col-md-4 mb-3">
                                                            <div class="card h-100 position-relative">
                                                                @if($map['image_url'])
                                                                    <img src="{{ asset($map['image_url']) }}" class="card-img-top" alt="{{ $map['name'] }}">
                                                                @endif
                                                                <div class="card-body text-center position-absolute bottom-0 start-0 w-100" style="background: rgba(0, 0, 0, 0.7);">
                                                                    <h6 class="card-title text-white mb-2">{{ $map['name'] }}</h6>
                                                                    <button type="button" class="btn btn-success btn-sm w-100" wire:click="unbanMap({{ $map['id'] }})">
                                                                        Разбанить
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-secondary me-2" wire:click="hideCreateForm">Отмена</button>
                            <button type="submit" class="btn btn-primary">Создать</button>
                        </div>
                    </form>
                </div>
            </div>
        @endif
        
        <div class="card mb-4">
            <div class="card-header">Фильтры</div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="gameTypeFilter" class="form-label">Тип игры</label>
                        <select id="gameTypeFilter" class="form-select" wire:model.live="gameType">
                            <option value="all">Все игры</option>
                            <option value="cs2">CS2</option>
                            <option value="dota2">Dota 2</option>
                            <option value="pubg">PUBG</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="dateFrom" class="form-label">Дата от</label>
                        <input type="date" id="dateFrom" class="form-control" wire:model.live="dateFrom">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="dateTo" class="form-label">Дата до</label>
                        <input type="date" id="dateTo" class="form-control" wire:model.live="dateTo">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="resultStatus" class="form-label">Результат</label>
                        <select id="resultStatus" class="form-select" wire:model.live="resultStatus">
                            <option value="all">Все результаты</option>
                            <option value="victory">Победы</option>
                            <option value="defeat">Поражения</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">Список матчей</div>
            <div class="card-body mt-3">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Тип игры</th>
                                <th>Счет</th>
                                <th>Результат</th>
                                <th>Дата</th>
                                <th>Действия</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($matches as $match)
                                <tr>
                                    <td>{{ $match->id }}</td>
                                    <td>{{ $getGameName($match->game_type) }}</td>
                                    <td>{{ $match->match_score ?? 'Н/Д' }}</td>
                                    <td>
                                        @if($match->victory === 1)
                                            <span class="badge bg-success">Победа</span>
                                        @elseif($match->victory === 0)
                                            <span class="badge bg-danger">Поражение</span>
                                        @else
                                            <span class="badge bg-warning">Ожидает</span>
                                        @endif
                                    </td>
                                    <td>{{ $match->date?->format('d.m.Y H:i') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" wire:click="showDetails({{ $match->id }})">
                                            Детали
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center">Нет данных о матчах</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                    
                    <div class="d-flex justify-content-center mt-4">
                        {{ $matches->links() }}
                    </div>
                </div>
            </div>
        </div>
        
        @if($selectedMatch)
            <div class="modal fade show" style="display: block;" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Детали матча #{{ $selectedMatch->id }}</h5>
                            <button type="button" class="btn-close" wire:click="closeDetails"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Информация о матче</h6>
                                    <table class="table">
                                        <tr>
                                            <th>ID:</th>
                                            <td>{{ $selectedMatch->id }}</td>
                                        </tr>
                                        <tr>
                                            <th>Тип игры:</th>
                                            <td>{{ $selectedMatch->game_type }}</td>
                                        </tr>
                                        <tr>
                                            <th>ID сессии:</th>
                                            <td>{{ $selectedMatch->session_id }}</td>
                                        </tr>
                                        <tr>
                                            <th>ID матча:</th>
                                            <td>{{ $selectedMatch->match_id }}</td>
                                        </tr>
                                        <tr>
                                            <th>Счет:</th>
                                            <td>{{ $selectedMatch->match_score ?? 'Н/Д' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Дата создания:</th>
                                            <td>{{ $selectedMatch->created_at?->format('d.m.Y H:i:s') }}</td>
                                        </tr>
                                    </table>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Результат матча</h6>
                                    @if($selectedMatch->result)
                                        <table class="table">
                                            <tr>
                                                <th>Результат:</th>
                                                <td>
                                                    @if($selectedMatch->result->victory)
                                                        <span class="badge bg-success">Победа</span>
                                                    @else
                                                        <span class="badge bg-danger">Поражение</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Очки:</th>
                                                <td>{{ $selectedMatch->result->add_score }}</td>
                                            </tr>
                                            @if($selectedMatch->result->details)
                                                <tr>
                                                    <th>Детали:</th>
                                                    <td>
                                                        <ul class="list-unstyled">
                                                            @foreach($selectedMatch->result->details as $key => $value)
                                                                <li><strong>{{ $key }}:</strong> {{ $value }}</li>
                                                            @endforeach
                                                        </ul>
                                                    </td>
                                                </tr>
                                            @endif
                                        </table>
                                    @else
                                        <div class="alert alert-warning">
                                            Результат матча еще не обработан
                                        </div>
                                    @endif
                                </div>
                            </div>
                            
                            @if($selectedMatch->game_type === 'cs2')
                                <div class="mt-3">
                                    <a href="{{ route('match.ban-maps', ['match' => $selectedMatch->id]) }}" class="btn btn-primary">
                                        Бан карт
                                    </a>
                                </div>
                            @endif
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" wire:click="closeDetails">Закрыть</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
</div>