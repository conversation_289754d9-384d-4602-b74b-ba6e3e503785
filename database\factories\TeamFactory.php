<?php

namespace Database\Factories;

use App\Models\Game;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Team>
 */
class TeamFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company() . ' Team',
            'game_id' => Game::inRandomOrder()->first()->id ?? Game::factory()->create()->id,
            'captain_id' => User::inRandomOrder()->first()->id ?? User::factory()->create()->id,
            'rating' => fake()->numberBetween(1000, 3000),
        ];
    }
}