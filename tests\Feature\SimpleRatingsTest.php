<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SimpleRatingsTest extends TestCase
{
    /**
     * Простой тест для проверки рейтинговой системы.
     */
    public function test_simple_ratings(): void
    {
        // Создаем необходимые таблицы
        $this->createTestTables();
        
        // Создаем игру
        DB::table('games')->insert([
            'name' => 'CS2',
            'code' => 'cs2',
            'image' => 'games/cs2.jpg',
            'team_size' => 5,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Создаем пользователя
        $playerId = DB::table('players')->insertGetId([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Создаем рейтинг для пользователя
        DB::table('ratings')->insert([
            'user_id' => $playerId,
            'game_id' => 1,
            'game_rating' => 1000,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что рейтинг создан
        $this->assertDatabaseHas('ratings', [
            'user_id' => $playerId,
            'game_rating' => 1000
        ]);
    }
    
    /**
     * Создает необходимые таблицы для тестов.
     */
    protected function createTestTables()
    {
        // Создаем таблицу players, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_nick TEXT,
                email TEXT UNIQUE,
                password TEXT,
                remember_token TEXT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Создаем таблицу games, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT,
                image TEXT NOT NULL DEFAULT "",
                team_size INTEGER NOT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Создаем таблицу ratings, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                game_id INTEGER NOT NULL,
                club_id INTEGER NULL,
                season_id INTEGER NULL,
                game_rating INTEGER NOT NULL DEFAULT 1000,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
    }
}

