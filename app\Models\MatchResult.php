<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $match_id
 * @property int $player_id
 * @property bool $victory
 * @property int $round
 * @property int $start_math
 * @property int $add_score
 * @property array<array-key, mixed>|null $details
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\GameMatch|null $match
 * @property-read \App\Models\User|null $player
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereAddScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereMatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereRound($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereStartMath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MatchResult whereVictory($value)
 * @mixin \Eloquent
 */
class MatchResult extends Model
{
    use HasFactory;

    /**
     * Атрибуты, которые можно массово назначать.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'match_id',
        'player_id',
        'victory',
        'round',
        'add_score',
        'details',
    ];

    /**
     * Атрибуты, которые должны быть приведены к определенным типам.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'victory' => 'boolean',
        'round' => 'integer',
        'add_score' => 'integer',
        'details' => 'array',
    ];

    /**
     * Получить матч, связанный с результатом.
     */
    public function match(): BelongsTo
    {
        return $this->belongsTo(GameMatch::class, 'match_id');
    }

    /**
     * Получить игрока, связанного с результатом.
     */
    public function player(): BelongsTo
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}


