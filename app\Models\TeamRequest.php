<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property-read \App\Models\Team|null $team
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamRequest query()
 * @mixin \Eloquent
 */
class TeamRequest extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'team_id',
        'player_id',
        'status',
        'message'
    ];
    
    /**
     * Get the team that owns the request.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }
    
    /**
     * Get the user that sent the request.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}



