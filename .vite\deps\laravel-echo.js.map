{"version": 3, "sources": ["../../node_modules/laravel-echo/src/channel/channel.ts", "../../node_modules/laravel-echo/src/util/event-formatter.ts", "../../node_modules/laravel-echo/src/util/index.ts", "../../node_modules/laravel-echo/src/channel/pusher-channel.ts", "../../node_modules/laravel-echo/src/channel/pusher-private-channel.ts", "../../node_modules/laravel-echo/src/channel/pusher-encrypted-private-channel.ts", "../../node_modules/laravel-echo/src/channel/pusher-presence-channel.ts", "../../node_modules/laravel-echo/src/channel/socketio-channel.ts", "../../node_modules/laravel-echo/src/channel/socketio-private-channel.ts", "../../node_modules/laravel-echo/src/channel/socketio-presence-channel.ts", "../../node_modules/laravel-echo/src/channel/null-channel.ts", "../../node_modules/laravel-echo/src/channel/null-private-channel.ts", "../../node_modules/laravel-echo/src/channel/null-encrypted-private-channel.ts", "../../node_modules/laravel-echo/src/channel/null-presence-channel.ts", "../../node_modules/laravel-echo/src/connector/connector.ts", "../../node_modules/laravel-echo/src/connector/pusher-connector.ts", "../../node_modules/laravel-echo/src/connector/socketio-connector.ts", "../../node_modules/laravel-echo/src/connector/null-connector.ts", "../../node_modules/laravel-echo/src/echo.ts"], "sourcesContent": ["import type { EchoOptionsWithDefaults } from \"../connector\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a basic channel.\n */\nexport abstract class Channel {\n    /**\n     * The Echo options.\n     */\n    options: EchoOptionsWithDefaults<BroadcastDriver>;\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    abstract listen(event: string, callback: CallableFunction): this;\n\n    /**\n     * Listen for a whisper event on the channel instance.\n     */\n    listenForWhisper(event: string, callback: CallableFunction): this {\n        return this.listen(\".client-\" + event, callback);\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    notification(callback: CallableFunction): this {\n        return this.listen(\n            \".Illuminate\\\\Notifications\\\\Events\\\\BroadcastNotificationCreated\",\n            callback,\n        );\n    }\n\n    /**\n     * Stop listening to an event on the channel instance.\n     */\n    abstract stopListening(event: string, callback?: CallableFunction): this;\n\n    /**\n     * Stop listening for a whisper event on the channel instance.\n     */\n    stopListeningForWhisper(event: string, callback?: CallableFunction): this {\n        return this.stopListening(\".client-\" + event, callback);\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    abstract subscribed(callback: CallableFunction): this;\n\n    /**\n     * Register a callback to be called anytime an error occurs.\n     */\n    abstract error(callback: CallableFunction): this;\n}\n", "/**\n * Event name formatter\n */\nexport class EventFormatter {\n    /**\n     * Create a new class instance.\n     */\n    constructor(private namespace: string | boolean | undefined) {\n        //\n    }\n\n    /**\n     * Format the given event name.\n     */\n    format(event: string): string {\n        if ([\".\", \"\\\\\"].includes(event.charAt(0))) {\n            return event.substring(1);\n        } else if (this.namespace) {\n            event = this.namespace + \".\" + event;\n        }\n\n        return event.replace(/\\./g, \"\\\\\");\n    }\n\n    /**\n     * Set the event namespace.\n     */\n    setNamespace(value: string | boolean): void {\n        this.namespace = value;\n    }\n}\n", "function isConstructor(obj: unknown): obj is new (...args: any[]) => any {\n    try {\n        new (obj as new (...args: any[]) => any)();\n    } catch (err) {\n        if (\n            err instanceof Error &&\n            err.message.includes(\"is not a constructor\")\n        ) {\n            return false;\n        }\n    }\n\n    return true;\n}\n\nexport { isConstructor };\nexport * from \"./event-formatter\";\n", "import { EventFormatter } from \"../util\";\nimport { Channel } from \"./channel\";\nimport type Pusher from \"pusher-js\";\nimport type { Channel as BasePusherChannel } from \"pusher-js\";\nimport type { EchoOptionsWithDefaults } from \"../connector\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher channel.\n */\nexport class PusherChannel<\n    TBroadcastDriver extends BroadcastDriver,\n> extends Channel {\n    /**\n     * The Pusher client instance.\n     */\n    pusher: Pusher;\n\n    /**\n     * The name of the channel.\n     */\n    name: string;\n\n    /**\n     * The event formatter.\n     */\n    eventFormatter: EventFormatter;\n\n    /**\n     * The subscription of the channel.\n     */\n    subscription: BasePusherChannel;\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(\n        pusher: Pusher,\n        name: string,\n        options: EchoOptionsWithDefaults<TBroadcastDriver>,\n    ) {\n        super();\n\n        this.name = name;\n        this.pusher = pusher;\n        this.options = options;\n        this.eventFormatter = new EventFormatter(this.options.namespace);\n\n        this.subscribe();\n    }\n\n    /**\n     * Subscribe to a Pusher channel.\n     */\n    subscribe(): void {\n        this.subscription = this.pusher.subscribe(this.name);\n    }\n\n    /**\n     * Unsubscribe from a Pusher channel.\n     */\n    unsubscribe(): void {\n        this.pusher.unsubscribe(this.name);\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    listen(event: string, callback: CallableFunction): this {\n        this.on(this.eventFormatter.format(event), callback);\n\n        return this;\n    }\n\n    /**\n     * Listen for all events on the channel instance.\n     */\n    listenToAll(callback: CallableFunction): this {\n        this.subscription.bind_global((event: string, data: unknown) => {\n            if (event.startsWith(\"pusher:\")) {\n                return;\n            }\n\n            let namespace = String(this.options.namespace ?? \"\").replace(\n                /\\./g,\n                \"\\\\\",\n            );\n\n            let formattedEvent = event.startsWith(namespace)\n                ? event.substring(namespace.length + 1)\n                : \".\" + event;\n\n            callback(formattedEvent, data);\n        });\n\n        return this;\n    }\n\n    /**\n     * Stop listening for an event on the channel instance.\n     */\n    stopListening(event: string, callback?: CallableFunction): this {\n        if (callback) {\n            this.subscription.unbind(\n                this.eventFormatter.format(event),\n                callback,\n            );\n        } else {\n            this.subscription.unbind(this.eventFormatter.format(event));\n        }\n\n        return this;\n    }\n\n    /**\n     * Stop listening for all events on the channel instance.\n     */\n    stopListeningToAll(callback?: CallableFunction): this {\n        if (callback) {\n            this.subscription.unbind_global(callback);\n        } else {\n            this.subscription.unbind_global();\n        }\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    subscribed(callback: CallableFunction): this {\n        this.on(\"pusher:subscription_succeeded\", () => {\n            callback();\n        });\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription error occurs.\n     */\n    error(callback: CallableFunction): this {\n        this.on(\"pusher:subscription_error\", (status: Record<string, any>) => {\n            callback(status);\n        });\n\n        return this;\n    }\n\n    /**\n     * Bind a channel to an event.\n     */\n    on(event: string, callback: CallableFunction): this {\n        this.subscription.bind(event, callback);\n\n        return this;\n    }\n}\n", "import { PusherChannel } from \"./pusher-channel\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher private channel.\n */\nexport class PusherPrivateChannel<\n    TBroadcastDriver extends BroadcastDriver,\n> extends PusherChannel<TBroadcastDriver> {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: Record<any, any>): this {\n        this.pusher.channels.channels[this.name].trigger(\n            `client-${eventName}`,\n            data,\n        );\n\n        return this;\n    }\n}\n", "import { PusherChannel } from \"./pusher-channel\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher private channel.\n */\nexport class PusherEncryptedPrivateChannel<\n    TBroadcastDriver extends BroadcastDriver,\n> extends PusherChannel<TBroadcastDriver> {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: Record<any, any>): this {\n        this.pusher.channels.channels[this.name].trigger(\n            `client-${eventName}`,\n            data,\n        );\n\n        return this;\n    }\n}\n", "import type { PresenceChannel } from \"./presence-channel\";\nimport { PusherPrivateChannel } from \"./pusher-private-channel\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Pusher presence channel.\n */\nexport class PusherPresenceChannel<TBroadcastDriver extends BroadcastDriver>\n    extends PusherPrivateChannel<TBroadcastDriver>\n    implements PresenceChannel\n{\n    /**\n     * Register a callback to be called anytime the member list changes.\n     */\n    here(callback: CallableFunction): this {\n        this.on(\"pusher:subscription_succeeded\", (data: Record<any, any>) => {\n            callback(Object.keys(data.members).map((k) => data.members[k]));\n        });\n\n        return this;\n    }\n\n    /**\n     * Listen for someone joining the channel.\n     */\n    joining(callback: CallableFunction): this {\n        this.on(\"pusher:member_added\", (member: Record<any, any>) => {\n            callback(member.info);\n        });\n\n        return this;\n    }\n\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: Record<any, any>): this {\n        this.pusher.channels.channels[this.name].trigger(\n            `client-${eventName}`,\n            data,\n        );\n\n        return this;\n    }\n\n    /**\n     * Listen for someone leaving the channel.\n     */\n    leaving(callback: CallableFunction): this {\n        this.on(\"pusher:member_removed\", (member: Record<any, any>) => {\n            callback(member.info);\n        });\n\n        return this;\n    }\n}\n", "import { EventFormatter } from \"../util\";\nimport { Channel } from \"./channel\";\nimport type { Socket } from \"socket.io-client\";\nimport type { EchoOptionsWithDefaults } from \"../connector\";\nimport type { BroadcastDriver } from \"../echo\";\n\n/**\n * This class represents a Socket.io channel.\n */\nexport class SocketIoChannel extends Channel {\n    /**\n     * The Socket.io client instance.\n     */\n    socket: Socket;\n\n    /**\n     * The name of the channel.\n     */\n    name: string;\n\n    /**\n     * The event formatter.\n     */\n    eventFormatter: EventFormatter;\n\n    /**\n     * The event callbacks applied to the socket.\n     */\n    events: Record<string, any> = {};\n\n    /**\n     * User supplied callbacks for events on this channel.\n     */\n    private listeners: Record<string, CallableFunction[]> = {};\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(\n        socket: Socket,\n        name: string,\n        options: EchoOptionsWithDefaults<BroadcastDriver>,\n    ) {\n        super();\n\n        this.name = name;\n        this.socket = socket;\n        this.options = options;\n        this.eventFormatter = new EventFormatter(this.options.namespace);\n\n        this.subscribe();\n    }\n\n    /**\n     * Subscribe to a Socket.io channel.\n     */\n    subscribe(): void {\n        this.socket.emit(\"subscribe\", {\n            channel: this.name,\n            auth: this.options.auth || {},\n        });\n    }\n\n    /**\n     * Unsubscribe from channel and ubind event callbacks.\n     */\n    unsubscribe(): void {\n        this.unbind();\n\n        this.socket.emit(\"unsubscribe\", {\n            channel: this.name,\n            auth: this.options.auth || {},\n        });\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    listen(event: string, callback: CallableFunction): this {\n        this.on(this.eventFormatter.format(event), callback);\n\n        return this;\n    }\n\n    /**\n     * Stop listening for an event on the channel instance.\n     */\n    stopListening(event: string, callback?: CallableFunction): this {\n        this.unbindEvent(this.eventFormatter.format(event), callback);\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    subscribed(callback: CallableFunction): this {\n        this.on(\"connect\", (socket: Socket) => {\n            callback(socket);\n        });\n\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime an error occurs.\n     */\n    error(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Bind the channel's socket to an event and store the callback.\n     */\n    on(event: string, callback: CallableFunction): this {\n        this.listeners[event] = this.listeners[event] || [];\n\n        if (!this.events[event]) {\n            this.events[event] = (channel: string, data: unknown) => {\n                if (this.name === channel && this.listeners[event]) {\n                    this.listeners[event].forEach((cb) => cb(data));\n                }\n            };\n\n            this.socket.on(event, this.events[event]);\n        }\n\n        this.listeners[event].push(callback);\n\n        return this;\n    }\n\n    /**\n     * Unbind the channel's socket from all stored event callbacks.\n     */\n    unbind(): void {\n        Object.keys(this.events).forEach((event) => {\n            this.unbindEvent(event);\n        });\n    }\n\n    /**\n     * Unbind the listeners for the given event.\n     */\n    protected unbindEvent(event: string, callback?: CallableFunction): void {\n        this.listeners[event] = this.listeners[event] || [];\n\n        if (callback) {\n            this.listeners[event] = this.listeners[event].filter(\n                (cb) => cb !== callback,\n            );\n        }\n\n        if (!callback || this.listeners[event].length === 0) {\n            if (this.events[event]) {\n                this.socket.removeListener(event, this.events[event]);\n\n                delete this.events[event];\n            }\n\n            delete this.listeners[event];\n        }\n    }\n}\n", "import { SocketIoChannel } from \"./socketio-channel\";\n\n/**\n * This class represents a Socket.io private channel.\n */\nexport class SocketIoPrivateChannel extends SocketIoChannel {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: unknown): this {\n        this.socket.emit(\"client event\", {\n            channel: this.name,\n            event: `client-${eventName}`,\n            data: data,\n        });\n\n        return this;\n    }\n}\n", "import type { PresenceChannel } from \"./presence-channel\";\nimport { SocketIoPrivateChannel } from \"./socketio-private-channel\";\n\n/**\n * This class represents a Socket.io presence channel.\n */\nexport class SocketIoPresenceChannel\n    extends SocketIoPrivateChannel\n    implements PresenceChannel\n{\n    /**\n     * Register a callback to be called anytime the member list changes.\n     */\n    here(callback: CallableFunction): this {\n        this.on(\"presence:subscribed\", (members: Record<string, any>[]) => {\n            callback(members.map((m) => m.user_info));\n        });\n\n        return this;\n    }\n\n    /**\n     * Listen for someone joining the channel.\n     */\n    joining(callback: CallableFunction): this {\n        this.on(\"presence:joining\", (member: Record<string, any>) =>\n            callback(member.user_info),\n        );\n\n        return this;\n    }\n\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(eventName: string, data: unknown): this {\n        this.socket.emit(\"client event\", {\n            channel: this.name,\n            event: `client-${eventName}`,\n            data: data,\n        });\n\n        return this;\n    }\n\n    /**\n     * Listen for someone leaving the channel.\n     */\n    leaving(callback: CallableFunction): this {\n        this.on(\"presence:leaving\", (member: Record<string, any>) =>\n            callback(member.user_info),\n        );\n\n        return this;\n    }\n}\n", "import { Channel } from \"./channel\";\n\n/**\n * This class represents a null channel.\n */\nexport class NullChannel extends Channel {\n    /**\n     * Subscribe to a channel.\n     */\n    subscribe(): void {\n        //\n    }\n\n    /**\n     * Unsubscribe from a channel.\n     */\n    unsubscribe(): void {\n        //\n    }\n\n    /**\n     * Listen for an event on the channel instance.\n     */\n    listen(_event: string, _callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Listen for all events on the channel instance.\n     */\n    listenToAll(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Stop listening for an event on the channel instance.\n     */\n    stopListening(_event: string, _callback?: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime a subscription succeeds.\n     */\n    subscribed(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Register a callback to be called anytime an error occurs.\n     */\n    error(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Bind a channel to an event.\n     */\n    on(_event: string, _callback: CallableFunction): this {\n        return this;\n    }\n}\n", "import { NullChannel } from \"./null-channel\";\n\n/**\n * This class represents a null private channel.\n */\nexport class NullPrivateChannel extends NullChannel {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(_eventName: string, _data: Record<any, any>): this {\n        return this;\n    }\n}\n", "import { NullChannel } from \"./null-channel\";\n\n/**\n * This class represents a null private channel.\n */\nexport class NullEncryptedPrivateChannel extends NullChannel {\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(_eventName: string, _data: Record<any, any>): this {\n        return this;\n    }\n}\n", "import { NullPrivateChannel } from \"./null-private-channel\";\nimport type { PresenceChannel } from \"./presence-channel\";\n\n/**\n * This class represents a null presence channel.\n */\nexport class NullPresenceChannel\n    extends NullPrivateChannel\n    implements PresenceChannel\n{\n    /**\n     * Register a callback to be called anytime the member list changes.\n     */\n    here(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Listen for someone joining the channel.\n     */\n    joining(_callback: CallableFunction): this {\n        return this;\n    }\n\n    /**\n     * Send a whisper event to other clients in the channel.\n     */\n    whisper(_eventName: string, _data: Record<any, any>): this {\n        return this;\n    }\n\n    /**\n     * Listen for someone leaving the channel.\n     */\n    leaving(_callback: CallableFunction): this {\n        return this;\n    }\n}\n", "/// <reference types=\"window\" />\n\nimport type { Channel, PresenceChannel } from \"../channel\";\nimport type { BroadcastDriver, EchoOptions } from \"../echo\";\n\nexport type EchoOptionsWithDefaults<TBroadcaster extends BroadcastDriver> = {\n    broadcaster: TBroadcaster;\n    auth: {\n        headers: Record<string, string>;\n    };\n    authEndpoint: string;\n    userAuthentication: {\n        endpoint: string;\n        headers: Record<string, string>;\n    };\n    csrfToken: string | null;\n    bearerToken: string | null;\n    host: string | null;\n    key: string | null;\n    namespace: string | false;\n\n    [key: string]: any;\n};\n\nexport abstract class Connector<\n    TBroadcastDriver extends BroadcastDriver,\n    TPublic extends Channel,\n    TPrivate extends Channel,\n    TPresence extends PresenceChannel,\n> {\n    /**\n     * Default connector options.\n     */\n    public static readonly _defaultOptions = {\n        auth: {\n            headers: {},\n        },\n        authEndpoint: \"/broadcasting/auth\",\n        userAuthentication: {\n            endpoint: \"/broadcasting/user-auth\",\n            headers: {},\n        },\n        csrfToken: null,\n        bearerToken: null,\n        host: null,\n        key: null,\n        namespace: \"App.Events\",\n    } as const;\n\n    /**\n     * Connector options.\n     */\n    options: EchoOptionsWithDefaults<TBroadcastDriver>;\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(options: EchoOptions<TBroadcastDriver>) {\n        this.setOptions(options);\n        this.connect();\n    }\n\n    /**\n     * Merge the custom options with the defaults.\n     */\n    protected setOptions(options: EchoOptions<TBroadcastDriver>): void {\n        this.options = {\n            ...Connector._defaultOptions,\n            ...options,\n            broadcaster: options.broadcaster as TBroadcastDriver,\n        };\n\n        let token = this.csrfToken();\n\n        if (token) {\n            this.options.auth.headers[\"X-CSRF-TOKEN\"] = token;\n            this.options.userAuthentication.headers[\"X-CSRF-TOKEN\"] = token;\n        }\n\n        token = this.options.bearerToken;\n\n        if (token) {\n            this.options.auth.headers[\"Authorization\"] = \"Bearer \" + token;\n            this.options.userAuthentication.headers[\"Authorization\"] =\n                \"Bearer \" + token;\n        }\n    }\n\n    /**\n     * Extract the CSRF token from the page.\n     */\n    protected csrfToken(): null | string {\n        if (typeof window !== \"undefined\" && window.Laravel?.csrfToken) {\n            return window.Laravel.csrfToken;\n        }\n\n        if (this.options.csrfToken) {\n            return this.options.csrfToken;\n        }\n\n        if (\n            typeof document !== \"undefined\" &&\n            typeof document.querySelector === \"function\"\n        ) {\n            return (\n                document\n                    .querySelector('meta[name=\"csrf-token\"]')\n                    ?.getAttribute(\"content\") ?? null\n            );\n        }\n\n        return null;\n    }\n\n    /**\n     * Create a fresh connection.\n     */\n    abstract connect(): void;\n\n    /**\n     * Get a channel instance by name.\n     */\n    abstract channel(channel: string): TPublic;\n\n    /**\n     * Get a private channel instance by name.\n     */\n    abstract privateChannel(channel: string): TPrivate;\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    abstract presenceChannel(channel: string): TPresence;\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    abstract leave(channel: string): void;\n\n    /**\n     * Leave the given channel.\n     */\n    abstract leaveChannel(channel: string): void;\n\n    /**\n     * Get the socket_id of the connection.\n     */\n    abstract socketId(): string | undefined;\n\n    /**\n     * Disconnect from the Echo server.\n     */\n    abstract disconnect(): void;\n}\n", "import type Pusher from \"pusher-js\";\nimport type { Options as PusherJsOptions } from \"pusher-js\";\nimport {\n    PusherChannel,\n    PusherEncryptedPrivateChannel,\n    PusherPresenceChannel,\n    PusherPrivateChannel,\n} from \"../channel\";\nimport type { BroadcastDriver } from \"../echo\";\nimport { Connector, type EchoOptionsWithDefaults } from \"./connector\";\n\ntype AnyPusherChannel =\n    | PusherChannel<BroadcastDriver>\n    | PusherPrivateChannel<BroadcastDriver>\n    | PusherEncryptedPrivateChannel<BroadcastDriver>\n    | PusherPresenceChannel<BroadcastDriver>;\n\nexport type PusherOptions<TBroadcastDriver extends BroadcastDriver> =\n    EchoOptionsWithDefaults<TBroadcastDriver> & {\n        key: string;\n        Pusher?: typeof Pusher;\n    } & PusherJsOptions;\n\n/**\n * This class creates a connector to Pusher.\n */\nexport class PusherConnector<\n    TBroadcastDriver extends BroadcastDriver,\n> extends Connector<\n    TBroadcastDriver,\n    PusherChannel<TBroadcastDriver>,\n    PusherPrivateChannel<TBroadcastDriver>,\n    PusherPresenceChannel<TBroadcastDriver>\n> {\n    /**\n     * The Pusher instance.\n     */\n    pusher: Pusher;\n\n    /**\n     * All of the subscribed channel names.\n     */\n    channels: Record<string, AnyPusherChannel> = {};\n\n    declare options: PusherOptions<TBroadcastDriver>;\n\n    /**\n     * Create a fresh Pusher connection.\n     */\n    connect(): void {\n        if (typeof this.options.client !== \"undefined\") {\n            this.pusher = this.options.client as Pusher;\n        } else if (this.options.Pusher) {\n            this.pusher = new this.options.Pusher(\n                this.options.key,\n                this.options,\n            );\n        } else if (\n            typeof window !== \"undefined\" &&\n            typeof window.Pusher !== \"undefined\"\n        ) {\n            this.pusher = new window.Pusher(this.options.key, this.options);\n        } else {\n            throw new Error(\n                \"Pusher client not found. Should be globally available or passed via options.client\",\n            );\n        }\n    }\n\n    /**\n     * Sign in the user via Pusher user authentication (https://pusher.com/docs/channels/using_channels/user-authentication/).\n     */\n    signin(): void {\n        this.pusher.signin();\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        name: string,\n        event: string,\n        callback: CallableFunction,\n    ): AnyPusherChannel {\n        return this.channel(name).listen(event, callback);\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(name: string): AnyPusherChannel {\n        if (!this.channels[name]) {\n            this.channels[name] = new PusherChannel(\n                this.pusher,\n                name,\n                this.options,\n            );\n        }\n\n        return this.channels[name];\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    privateChannel(name: string): PusherPrivateChannel<TBroadcastDriver> {\n        if (!this.channels[\"private-\" + name]) {\n            this.channels[\"private-\" + name] = new PusherPrivateChannel(\n                this.pusher,\n                \"private-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\n            \"private-\" + name\n        ] as PusherPrivateChannel<TBroadcastDriver>;\n    }\n\n    /**\n     * Get a private encrypted channel instance by name.\n     */\n    encryptedPrivateChannel(\n        name: string,\n    ): PusherEncryptedPrivateChannel<TBroadcastDriver> {\n        if (!this.channels[\"private-encrypted-\" + name]) {\n            this.channels[\"private-encrypted-\" + name] =\n                new PusherEncryptedPrivateChannel(\n                    this.pusher,\n                    \"private-encrypted-\" + name,\n                    this.options,\n                );\n        }\n\n        return this.channels[\n            \"private-encrypted-\" + name\n        ] as PusherEncryptedPrivateChannel<TBroadcastDriver>;\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    presenceChannel(name: string): PusherPresenceChannel<TBroadcastDriver> {\n        if (!this.channels[\"presence-\" + name]) {\n            this.channels[\"presence-\" + name] = new PusherPresenceChannel(\n                this.pusher,\n                \"presence-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\n            \"presence-\" + name\n        ] as PusherPresenceChannel<TBroadcastDriver>;\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(name: string): void {\n        let channels = [\n            name,\n            \"private-\" + name,\n            \"private-encrypted-\" + name,\n            \"presence-\" + name,\n        ];\n\n        channels.forEach((name: string) => {\n            this.leaveChannel(name);\n        });\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(name: string): void {\n        if (this.channels[name]) {\n            this.channels[name].unsubscribe();\n\n            delete this.channels[name];\n        }\n    }\n\n    /**\n     * Get the socket ID for the connection.\n     */\n    socketId(): string {\n        return this.pusher.connection.socket_id;\n    }\n\n    /**\n     * Disconnect Pusher connection.\n     */\n    disconnect(): void {\n        this.pusher.disconnect();\n    }\n}\n", "import { Connector } from \"./connector\";\nimport {\n    SocketIoChannel,\n    SocketIoPrivateChannel,\n    SocketIoPresenceChannel,\n} from \"../channel\";\nimport type {\n    io,\n    ManagerOptions,\n    Socket,\n    SocketOptions,\n} from \"socket.io-client\";\n\ntype AnySocketIoChannel =\n    | SocketIoChannel\n    | SocketIoPrivateChannel\n    | SocketIoPresenceChannel;\n\n/**\n * This class creates a connector to a Socket.io server.\n */\nexport class SocketIoConnector extends Connector<\n    \"socket.io\",\n    SocketIoChannel,\n    SocketIoPrivateChannel,\n    SocketIoPresenceChannel\n> {\n    /**\n     * The Socket.io connection instance.\n     */\n    socket: Socket;\n\n    /**\n     * All of the subscribed channel names.\n     */\n    channels: { [name: string]: SocketIoChannel } = {};\n\n    /**\n     * Create a fresh Socket.io connection.\n     */\n    connect(): void {\n        let io = this.getSocketIO();\n\n        this.socket = io(\n            this.options.host ?? undefined,\n            this.options as Partial<ManagerOptions & SocketOptions>,\n        );\n\n        this.socket.io.on(\"reconnect\", () => {\n            Object.values(this.channels).forEach((channel) => {\n                channel.subscribe();\n            });\n        });\n    }\n\n    /**\n     * Get socket.io module from global scope or options.\n     */\n    getSocketIO(): typeof io {\n        if (typeof this.options.client !== \"undefined\") {\n            return this.options.client as typeof io;\n        }\n\n        if (typeof window !== \"undefined\" && typeof window.io !== \"undefined\") {\n            return window.io;\n        }\n\n        throw new Error(\n            \"Socket.io client not found. Should be globally available or passed via options.client\",\n        );\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        name: string,\n        event: string,\n        callback: CallableFunction,\n    ): AnySocketIoChannel {\n        return this.channel(name).listen(event, callback);\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(name: string): AnySocketIoChannel {\n        if (!this.channels[name]) {\n            this.channels[name] = new SocketIoChannel(\n                this.socket,\n                name,\n                this.options,\n            );\n        }\n\n        return this.channels[name];\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    privateChannel(name: string): SocketIoPrivateChannel {\n        if (!this.channels[\"private-\" + name]) {\n            this.channels[\"private-\" + name] = new SocketIoPrivateChannel(\n                this.socket,\n                \"private-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\"private-\" + name] as SocketIoPrivateChannel;\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    presenceChannel(name: string): SocketIoPresenceChannel {\n        if (!this.channels[\"presence-\" + name]) {\n            this.channels[\"presence-\" + name] = new SocketIoPresenceChannel(\n                this.socket,\n                \"presence-\" + name,\n                this.options,\n            );\n        }\n\n        return this.channels[\"presence-\" + name] as SocketIoPresenceChannel;\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(name: string): void {\n        let channels = [name, \"private-\" + name, \"presence-\" + name];\n\n        channels.forEach((name) => {\n            this.leaveChannel(name);\n        });\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(name: string): void {\n        if (this.channels[name]) {\n            this.channels[name].unsubscribe();\n\n            delete this.channels[name];\n        }\n    }\n\n    /**\n     * Get the socket ID for the connection.\n     */\n    socketId(): string | undefined {\n        return this.socket.id;\n    }\n\n    /**\n     * Disconnect Socketio connection.\n     */\n    disconnect(): void {\n        this.socket.disconnect();\n    }\n}\n", "import { Connector } from \"./connector\";\nimport {\n    NullChannel,\n    NullPrivateChannel,\n    NullPresenceChannel,\n    NullEncryptedPrivateChannel,\n} from \"../channel\";\n\n/**\n * This class creates a null connector.\n */\nexport class NullConnector extends Connector<\n    \"null\",\n    NullChannel,\n    NullPrivateChannel,\n    NullPresenceChannel\n> {\n    /**\n     * All of the subscribed channel names.\n     */\n    channels: any = {};\n\n    /**\n     * Create a fresh connection.\n     */\n    connect(): void {\n        //\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        _name: string,\n        _event: string,\n        _callback: CallableFunction,\n    ): NullChannel {\n        return new NullChannel();\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(_name: string): NullChannel {\n        return new NullChannel();\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    privateChannel(_name: string): NullPrivateChannel {\n        return new NullPrivateChannel();\n    }\n\n    /**\n     * Get a private encrypted channel instance by name.\n     */\n    encryptedPrivateChannel(_name: string): NullEncryptedPrivateChannel {\n        return new NullEncryptedPrivateChannel();\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    presenceChannel(_name: string): NullPresenceChannel {\n        return new NullPresenceChannel();\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(_name: string): void {\n        //\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(_name: string): void {\n        //\n    }\n\n    /**\n     * Get the socket ID for the connection.\n     */\n    socketId(): string {\n        return \"fake-socket-id\";\n    }\n\n    /**\n     * Disconnect the connection.\n     */\n    disconnect(): void {\n        //\n    }\n}\n", "import type { InternalAxiosRequestConfig } from \"axios\";\nimport {\n    Channel,\n    NullChannel,\n    NullEncryptedPrivateChannel,\n    NullPresenceChannel,\n    NullPrivateChannel,\n    PusherChannel,\n    PusherEncryptedPrivateChannel,\n    PusherPresenceChannel,\n    PusherPrivateChannel,\n    SocketIoChannel,\n    SocketIoPresenceChannel,\n    SocketIoPrivateChannel,\n    type PresenceChannel,\n} from \"./channel\";\nimport {\n    Connector,\n    NullConnector,\n    PusherConnector,\n    SocketIoConnector,\n    type PusherOptions,\n} from \"./connector\";\nimport { isConstructor } from \"./util\";\n\n/**\n * This class is the primary API for interacting with broadcasting.\n */\nexport default class Echo<T extends keyof Broadcaster> {\n    /**\n     * The broadcasting connector.\n     */\n    connector: Broadcaster[Exclude<T, \"function\">][\"connector\"];\n\n    /**\n     * The Echo options.\n     */\n    options: EchoOptions<T>;\n\n    /**\n     * Create a new class instance.\n     */\n    constructor(options: EchoOptions<T>) {\n        this.options = options;\n        this.connect();\n\n        if (!this.options.withoutInterceptors) {\n            this.registerInterceptors();\n        }\n    }\n\n    /**\n     * Get a channel instance by name.\n     */\n    channel(channel: string): Broadcaster[T][\"public\"] {\n        return this.connector.channel(channel);\n    }\n\n    /**\n     * Create a new connection.\n     */\n    connect(): void {\n        if (this.options.broadcaster === \"reverb\") {\n            this.connector = new PusherConnector<\"reverb\">({\n                ...this.options,\n                cluster: \"\",\n            });\n        } else if (this.options.broadcaster === \"pusher\") {\n            this.connector = new PusherConnector<\"pusher\">(this.options);\n        } else if (this.options.broadcaster === \"ably\") {\n            this.connector = new PusherConnector<\"pusher\">({\n                ...this.options,\n                cluster: \"\",\n                broadcaster: \"pusher\",\n            });\n        } else if (this.options.broadcaster === \"socket.io\") {\n            this.connector = new SocketIoConnector(this.options);\n        } else if (this.options.broadcaster === \"null\") {\n            this.connector = new NullConnector(this.options);\n        } else if (\n            typeof this.options.broadcaster === \"function\" &&\n            isConstructor(this.options.broadcaster)\n        ) {\n            this.connector = new this.options.broadcaster(this.options);\n        } else {\n            throw new Error(\n                `Broadcaster ${typeof this.options.broadcaster} ${String(this.options.broadcaster)} is not supported.`,\n            );\n        }\n    }\n\n    /**\n     * Disconnect from the Echo server.\n     */\n    disconnect(): void {\n        this.connector.disconnect();\n    }\n\n    /**\n     * Get a presence channel instance by name.\n     */\n    join(channel: string): Broadcaster[T][\"presence\"] {\n        return this.connector.presenceChannel(channel);\n    }\n\n    /**\n     * Leave the given channel, as well as its private and presence variants.\n     */\n    leave(channel: string): void {\n        this.connector.leave(channel);\n    }\n\n    /**\n     * Leave the given channel.\n     */\n    leaveChannel(channel: string): void {\n        this.connector.leaveChannel(channel);\n    }\n\n    /**\n     * Leave all channels.\n     */\n    leaveAllChannels(): void {\n        for (const channel in this.connector.channels) {\n            this.leaveChannel(channel);\n        }\n    }\n\n    /**\n     * Listen for an event on a channel instance.\n     */\n    listen(\n        channel: string,\n        event: string,\n        callback: CallableFunction,\n    ): Broadcaster[T][\"public\"] {\n        return this.connector.listen(channel, event, callback);\n    }\n\n    /**\n     * Get a private channel instance by name.\n     */\n    private(channel: string): Broadcaster[T][\"private\"] {\n        return this.connector.privateChannel(channel);\n    }\n\n    /**\n     * Get a private encrypted channel instance by name.\n     */\n    encryptedPrivate(channel: string): Broadcaster[T][\"encrypted\"] {\n        if (this.connectorSupportsEncryptedPrivateChannels(this.connector)) {\n            return this.connector.encryptedPrivateChannel(channel);\n        }\n\n        throw new Error(\n            `Broadcaster ${typeof this.options.broadcaster} ${String(\n                this.options.broadcaster,\n            )} does not support encrypted private channels.`,\n        );\n    }\n\n    private connectorSupportsEncryptedPrivateChannels(\n        connector: unknown,\n    ): connector is PusherConnector<any> | NullConnector {\n        return (\n            connector instanceof PusherConnector ||\n            connector instanceof NullConnector\n        );\n    }\n\n    /**\n     * Get the Socket ID for the connection.\n     */\n    socketId(): string | undefined {\n        return this.connector.socketId();\n    }\n\n    /**\n     * Register 3rd party request interceptiors. These are used to automatically\n     * send a connections socket id to a Laravel app with a X-Socket-Id header.\n     */\n    registerInterceptors(): void {\n        // TODO: This package is deprecated and we should remove it in a future version.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        if (typeof Vue !== \"undefined\" && Vue?.http) {\n            this.registerVueRequestInterceptor();\n        }\n\n        if (typeof axios === \"function\") {\n            this.registerAxiosRequestInterceptor();\n        }\n\n        if (typeof jQuery === \"function\") {\n            this.registerjQueryAjaxSetup();\n        }\n\n        if (typeof Turbo === \"object\") {\n            this.registerTurboRequestInterceptor();\n        }\n    }\n\n    /**\n     * Register a Vue HTTP interceptor to add the X-Socket-ID header.\n     */\n    registerVueRequestInterceptor(): void {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n        Vue.http.interceptors.push(\n            (request: Record<any, any>, next: CallableFunction) => {\n                if (this.socketId()) {\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n                    request.headers.set(\"X-Socket-ID\", this.socketId());\n                }\n\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n                next();\n            },\n        );\n    }\n\n    /**\n     * Register an Axios HTTP interceptor to add the X-Socket-ID header.\n     */\n    registerAxiosRequestInterceptor(): void {\n        axios!.interceptors.request.use(\n            (config: InternalAxiosRequestConfig<any>) => {\n                if (this.socketId()) {\n                    config.headers[\"X-Socket-Id\"] = this.socketId();\n                }\n\n                return config;\n            },\n        );\n    }\n\n    /**\n     * Register jQuery AjaxPrefilter to add the X-Socket-ID header.\n     */\n    registerjQueryAjaxSetup(): void {\n        if (typeof jQuery.ajax != \"undefined\") {\n            jQuery.ajaxPrefilter(\n                (\n                    _options: any,\n                    _originalOptions: any,\n                    xhr: Record<any, any>,\n                ) => {\n                    if (this.socketId()) {\n                        xhr.setRequestHeader(\"X-Socket-Id\", this.socketId());\n                    }\n                },\n            );\n        }\n    }\n\n    /**\n     * Register the Turbo Request interceptor to add the X-Socket-ID header.\n     */\n    registerTurboRequestInterceptor(): void {\n        document.addEventListener(\n            \"turbo:before-fetch-request\",\n            (event: Record<any, any>) => {\n                event.detail.fetchOptions.headers[\"X-Socket-Id\"] =\n                    this.socketId();\n            },\n        );\n    }\n}\n\n/**\n * Export channel classes for TypeScript.\n */\nexport { Channel, Connector, type PresenceChannel };\n\nexport { EventFormatter } from \"./util\";\n\ntype CustomOmit<T, K extends PropertyKey> = {\n    [P in keyof T as Exclude<P, K>]: T[P];\n};\n\n/**\n * Specifies the broadcaster\n */\nexport type Broadcaster = {\n    reverb: {\n        connector: PusherConnector<\"reverb\">;\n        public: PusherChannel<\"reverb\">;\n        private: PusherPrivateChannel<\"reverb\">;\n        encrypted: PusherEncryptedPrivateChannel<\"reverb\">;\n        presence: PusherPresenceChannel<\"reverb\">;\n        options: GenericOptions<\"reverb\"> &\n            Partial<CustomOmit<PusherOptions<\"reverb\">, \"cluster\">>;\n    };\n    pusher: {\n        connector: PusherConnector<\"pusher\">;\n        public: PusherChannel<\"pusher\">;\n        private: PusherPrivateChannel<\"pusher\">;\n        encrypted: PusherEncryptedPrivateChannel<\"pusher\">;\n        presence: PusherPresenceChannel<\"pusher\">;\n        options: GenericOptions<\"pusher\"> & Partial<PusherOptions<\"pusher\">>;\n    };\n    ably: {\n        connector: PusherConnector<\"pusher\">;\n        public: PusherChannel<\"pusher\">;\n        private: PusherPrivateChannel<\"pusher\">;\n        encrypted: PusherEncryptedPrivateChannel<\"pusher\">;\n        presence: PusherPresenceChannel<\"pusher\">;\n        options: GenericOptions<\"ably\"> & Partial<PusherOptions<\"ably\">>;\n    };\n    \"socket.io\": {\n        connector: SocketIoConnector;\n        public: SocketIoChannel;\n        private: SocketIoPrivateChannel;\n        encrypted: never;\n        presence: SocketIoPresenceChannel;\n        options: GenericOptions<\"socket.io\">;\n    };\n    null: {\n        connector: NullConnector;\n        public: NullChannel;\n        private: NullPrivateChannel;\n        encrypted: NullEncryptedPrivateChannel;\n        presence: NullPresenceChannel;\n        options: GenericOptions<\"null\">;\n    };\n    function: {\n        connector: any;\n        public: any;\n        private: any;\n        encrypted: any;\n        presence: any;\n        options: GenericOptions<\"function\">;\n    };\n};\n\ntype Constructor<T = {}> = new (...args: any[]) => T;\n\nexport type BroadcastDriver = Exclude<keyof Broadcaster, \"function\">;\n\ntype GenericOptions<TBroadcaster extends keyof Broadcaster> = {\n    /**\n     * The broadcast connector.\n     */\n    broadcaster: TBroadcaster extends \"function\"\n        ? Constructor<InstanceType<Broadcaster[TBroadcaster][\"connector\"]>>\n        : TBroadcaster;\n\n    auth?: {\n        headers: Record<string, string>;\n    };\n    authEndpoint?: string;\n    userAuthentication?: {\n        endpoint: string;\n        headers: Record<string, string>;\n    };\n    csrfToken?: string | null;\n    bearerToken?: string | null;\n    host?: string | null;\n    key?: string | null;\n    namespace?: string | false;\n    withoutInterceptors?: boolean;\n\n    [key: string]: any;\n};\n\nexport type EchoOptions<TBroadcaster extends keyof Broadcaster> =\n    Broadcaster[TBroadcaster][\"options\"];\n"], "mappings": ";;;AAMO,IAAeA,IAAf,MAAuB;;;;EAc1B,iBAAiBC,GAAeC,GAAkC;AAC9D,WAAO,KAAK,OAAO,aAAaD,GAAOC,CAAQ;EAAA;;;;EAMnD,aAAaA,GAAkC;AAC3C,WAAO,KAAK;MACR;MACAA;IACJ;EAAA;;;;EAWJ,wBAAwBD,GAAeC,GAAmC;AACtE,WAAO,KAAK,cAAc,aAAaD,GAAOC,CAAQ;EAAA;AAY9D;ACpDO,IAAMC,IAAN,MAAqB;;;;EAIxB,YAAoBC,GAAyC;AAAzC,SAAA,YAAAA;EAAA;;;;EAOpB,OAAOH,GAAuB;AACtB,WAAA,CAAC,KAAK,IAAI,EAAE,SAASA,EAAM,OAAO,CAAC,CAAC,IAC7BA,EAAM,UAAU,CAAC,KACjB,KAAK,cACJA,IAAA,KAAK,YAAY,MAAMA,IAG5BA,EAAM,QAAQ,OAAO,IAAI;EAAA;;;;EAMpC,aAAaI,GAA+B;AACxC,SAAK,YAAYA;EAAA;AAEzB;AC9BA,SAASC,EAAcC,GAAkD;AACjE,MAAA;AACA,QAAKA,EAAoC;EAAA,SACpCC,GAAK;AACV,QACIA,aAAe,SACfA,EAAI,QAAQ,SAAS,sBAAsB;AAEpC,aAAA;EACX;AAGG,SAAA;AACX;ACHO,IAAMC,IAAN,cAEGT,EAAQ;;;;EAwBd,YACIU,GACAC,GACAC,GACF;AACQ,UAAA,GAEN,KAAK,OAAOD,GACZ,KAAK,SAASD,GACd,KAAK,UAAUE,GACf,KAAK,iBAAiB,IAAIT,EAAe,KAAK,QAAQ,SAAS,GAE/D,KAAK,UAAU;EAAA;;;;EAMnB,YAAkB;AACd,SAAK,eAAe,KAAK,OAAO,UAAU,KAAK,IAAI;EAAA;;;;EAMvD,cAAoB;AACX,SAAA,OAAO,YAAY,KAAK,IAAI;EAAA;;;;EAMrC,OAAOF,GAAeC,GAAkC;AACpD,WAAA,KAAK,GAAG,KAAK,eAAe,OAAOD,CAAK,GAAGC,CAAQ,GAE5C;EAAA;;;;EAMX,YAAYA,GAAkC;AAC1C,WAAA,KAAK,aAAa,YAAY,CAACD,GAAeY,MAAkB;AACxD,UAAAZ,EAAM,WAAW,SAAS;AAC1B;AAGJ,UAAIG,IAAY,OAAO,KAAK,QAAQ,aAAa,EAAE,EAAE;QACjD;QACA;MACJ,GAEIU,IAAiBb,EAAM,WAAWG,CAAS,IACzCH,EAAM,UAAUG,EAAU,SAAS,CAAC,IACpC,MAAMH;AAEZC,QAASY,GAAgBD,CAAI;IAAA,CAChC,GAEM;EAAA;;;;EAMX,cAAcZ,GAAeC,GAAmC;AAC5D,WAAIA,IACA,KAAK,aAAa;MACd,KAAK,eAAe,OAAOD,CAAK;MAChCC;IACJ,IAEA,KAAK,aAAa,OAAO,KAAK,eAAe,OAAOD,CAAK,CAAC,GAGvD;EAAA;;;;EAMX,mBAAmBC,GAAmC;AAClD,WAAIA,IACK,KAAA,aAAa,cAAcA,CAAQ,IAExC,KAAK,aAAa,cAAc,GAG7B;EAAA;;;;EAMX,WAAWA,GAAkC;AACpC,WAAA,KAAA,GAAG,iCAAiC,MAAM;AAClCA,QAAA;IAAA,CACZ,GAEM;EAAA;;;;EAMX,MAAMA,GAAkC;AAC/B,WAAA,KAAA,GAAG,6BAA6B,CAACa,MAAgC;AAClEb,QAASa,CAAM;IAAA,CAClB,GAEM;EAAA;;;;EAMX,GAAGd,GAAeC,GAAkC;AAC3C,WAAA,KAAA,aAAa,KAAKD,GAAOC,CAAQ,GAE/B;EAAA;AAEf;ACvJO,IAAMc,IAAN,cAEGP,EAAgC;;;;EAItC,QAAQQ,GAAmBJ,GAA8B;AACrD,WAAA,KAAK,OAAO,SAAS,SAAS,KAAK,IAAI,EAAE;MACrC,UAAUI,CAAS;MACnBJ;IACJ,GAEO;EAAA;AAEf;ACdO,IAAMK,IAAN,cAEGT,EAAgC;;;;EAItC,QAAQQ,GAAmBJ,GAA8B;AACrD,WAAA,KAAK,OAAO,SAAS,SAAS,KAAK,IAAI,EAAE;MACrC,UAAUI,CAAS;MACnBJ;IACJ,GAEO;EAAA;AAEf;ACbO,IAAMM,IAAN,cACKH,EAEZ;;;;EAII,KAAKd,GAAkC;AAC9B,WAAA,KAAA,GAAG,iCAAiC,CAACW,MAA2B;AACjEX,QAAS,OAAO,KAAKW,EAAK,OAAO,EAAE,IAAI,CAACO,MAAMP,EAAK,QAAQO,CAAC,CAAC,CAAC;IAAA,CACjE,GAEM;EAAA;;;;EAMX,QAAQlB,GAAkC;AACjC,WAAA,KAAA,GAAG,uBAAuB,CAACmB,MAA6B;AACzDnB,QAASmB,EAAO,IAAI;IAAA,CACvB,GAEM;EAAA;;;;EAMX,QAAQJ,GAAmBJ,GAA8B;AACrD,WAAA,KAAK,OAAO,SAAS,SAAS,KAAK,IAAI,EAAE;MACrC,UAAUI,CAAS;MACnBJ;IACJ,GAEO;EAAA;;;;EAMX,QAAQX,GAAkC;AACjC,WAAA,KAAA,GAAG,yBAAyB,CAACmB,MAA6B;AAC3DnB,QAASmB,EAAO,IAAI;IAAA,CACvB,GAEM;EAAA;AAEf;AC9CO,IAAMC,IAAN,cAA8BtB,EAAQ;;;;EA6BzC,YACIuB,GACAZ,GACAC,GACF;AACQ,UAAA,GAfV,KAAA,SAA8B,CAAC,GAK/B,KAAQ,YAAgD,CAAC,GAYrD,KAAK,OAAOD,GACZ,KAAK,SAASY,GACd,KAAK,UAAUX,GACf,KAAK,iBAAiB,IAAIT,EAAe,KAAK,QAAQ,SAAS,GAE/D,KAAK,UAAU;EAAA;;;;EAMnB,YAAkB;AACT,SAAA,OAAO,KAAK,aAAa;MAC1B,SAAS,KAAK;MACd,MAAM,KAAK,QAAQ,QAAQ,CAAA;IAAC,CAC/B;EAAA;;;;EAML,cAAoB;AAChB,SAAK,OAAO,GAEP,KAAA,OAAO,KAAK,eAAe;MAC5B,SAAS,KAAK;MACd,MAAM,KAAK,QAAQ,QAAQ,CAAA;IAAC,CAC/B;EAAA;;;;EAML,OAAOF,GAAeC,GAAkC;AACpD,WAAA,KAAK,GAAG,KAAK,eAAe,OAAOD,CAAK,GAAGC,CAAQ,GAE5C;EAAA;;;;EAMX,cAAcD,GAAeC,GAAmC;AAC5D,WAAA,KAAK,YAAY,KAAK,eAAe,OAAOD,CAAK,GAAGC,CAAQ,GAErD;EAAA;;;;EAMX,WAAWA,GAAkC;AACpC,WAAA,KAAA,GAAG,WAAW,CAACqB,MAAmB;AACnCrB,QAASqB,CAAM;IAAA,CAClB,GAEM;EAAA;;;;EAMX,MAAMC,GAAmC;AAC9B,WAAA;EAAA;;;;EAMX,GAAGvB,GAAeC,GAAkC;AAChD,WAAA,KAAK,UAAUD,CAAK,IAAI,KAAK,UAAUA,CAAK,KAAK,CAAC,GAE7C,KAAK,OAAOA,CAAK,MAClB,KAAK,OAAOA,CAAK,IAAI,CAACwB,GAAiBZ,MAAkB;AACjD,WAAK,SAASY,KAAW,KAAK,UAAUxB,CAAK,KACxC,KAAA,UAAUA,CAAK,EAAE,QAAQ,CAACyB,MAAOA,EAAGb,CAAI,CAAC;IAEtD,GAEA,KAAK,OAAO,GAAGZ,GAAO,KAAK,OAAOA,CAAK,CAAC,IAG5C,KAAK,UAAUA,CAAK,EAAE,KAAKC,CAAQ,GAE5B;EAAA;;;;EAMX,SAAe;AACX,WAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,CAACD,MAAU;AACxC,WAAK,YAAYA,CAAK;IAAA,CACzB;EAAA;;;;EAMK,YAAYA,GAAeC,GAAmC;AACpE,SAAK,UAAUD,CAAK,IAAI,KAAK,UAAUA,CAAK,KAAK,CAAC,GAE9CC,MACA,KAAK,UAAUD,CAAK,IAAI,KAAK,UAAUA,CAAK,EAAE;MAC1C,CAACyB,MAAOA,MAAOxB;IACnB,KAGA,CAACA,KAAY,KAAK,UAAUD,CAAK,EAAE,WAAW,OAC1C,KAAK,OAAOA,CAAK,MACjB,KAAK,OAAO,eAAeA,GAAO,KAAK,OAAOA,CAAK,CAAC,GAE7C,OAAA,KAAK,OAAOA,CAAK,IAGrB,OAAA,KAAK,UAAUA,CAAK;EAC/B;AAER;AC9JO,IAAM0B,IAAN,cAAqCL,EAAgB;;;;EAIxD,QAAQL,GAAmBJ,GAAqB;AACvC,WAAA,KAAA,OAAO,KAAK,gBAAgB;MAC7B,SAAS,KAAK;MACd,OAAO,UAAUI,CAAS;MAC1B,MAAAJ;IAAA,CACH,GAEM;EAAA;AAEf;ACZO,IAAMe,IAAN,cACKD,EAEZ;;;;EAII,KAAKzB,GAAkC;AAC9B,WAAA,KAAA,GAAG,uBAAuB,CAAC2B,MAAmC;AAC/D3B,QAAS2B,EAAQ,IAAI,CAACC,MAAMA,EAAE,SAAS,CAAC;IAAA,CAC3C,GAEM;EAAA;;;;EAMX,QAAQ5B,GAAkC;AACjC,WAAA,KAAA;MAAG;MAAoB,CAACmB,MACzBnB,EAASmB,EAAO,SAAS;IAC7B,GAEO;EAAA;;;;EAMX,QAAQJ,GAAmBJ,GAAqB;AACvC,WAAA,KAAA,OAAO,KAAK,gBAAgB;MAC7B,SAAS,KAAK;MACd,OAAO,UAAUI,CAAS;MAC1B,MAAAJ;IAAA,CACH,GAEM;EAAA;;;;EAMX,QAAQX,GAAkC;AACjC,WAAA,KAAA;MAAG;MAAoB,CAACmB,MACzBnB,EAASmB,EAAO,SAAS;IAC7B,GAEO;EAAA;AAEf;AClDO,IAAMU,IAAN,cAA0B/B,EAAQ;;;;EAIrC,YAAkB;EAAA;;;;EAOlB,cAAoB;EAAA;;;;EAOpB,OAAOgC,GAAgBR,GAAmC;AAC/C,WAAA;EAAA;;;;EAMX,YAAYA,GAAmC;AACpC,WAAA;EAAA;;;;EAMX,cAAcQ,GAAgBR,GAAoC;AACvD,WAAA;EAAA;;;;EAMX,WAAWA,GAAmC;AACnC,WAAA;EAAA;;;;EAMX,MAAMA,GAAmC;AAC9B,WAAA;EAAA;;;;EAMX,GAAGQ,GAAgBR,GAAmC;AAC3C,WAAA;EAAA;AAEf;ACxDO,IAAMS,IAAN,cAAiCF,EAAY;;;;EAIhD,QAAQG,GAAoBC,GAA+B;AAChD,WAAA;EAAA;AAEf;ACPO,IAAMC,IAAN,cAA0CL,EAAY;;;;EAIzD,QAAQG,GAAoBC,GAA+B;AAChD,WAAA;EAAA;AAEf;ACNO,IAAME,IAAN,cACKJ,EAEZ;;;;EAII,KAAKT,GAAmC;AAC7B,WAAA;EAAA;;;;EAMX,QAAQA,GAAmC;AAChC,WAAA;EAAA;;;;EAMX,QAAQU,GAAoBC,GAA+B;AAChD,WAAA;EAAA;;;;EAMX,QAAQX,GAAmC;AAChC,WAAA;EAAA;AAEf;ACbO,IAAec,IAAf,MAAeA,GAKpB;;;;EA4BE,YAAY1B,GAAwC;AAChD,SAAK,WAAWA,CAAO,GACvB,KAAK,QAAQ;EAAA;;;;EAMP,WAAWA,GAA8C;AAC/D,SAAK,UAAU;MACX,GAAG0B,GAAU;MACb,GAAG1B;MACH,aAAaA,EAAQ;IACzB;AAEI,QAAA2B,IAAQ,KAAK,UAAU;AAEvBA,UACA,KAAK,QAAQ,KAAK,QAAQ,cAAc,IAAIA,GAC5C,KAAK,QAAQ,mBAAmB,QAAQ,cAAc,IAAIA,IAG9DA,IAAQ,KAAK,QAAQ,aAEjBA,MACA,KAAK,QAAQ,KAAK,QAAQ,gBAAmB,YAAYA,GACzD,KAAK,QAAQ,mBAAmB,QAAQ,gBACpC,YAAYA;EACpB;;;;EAMM,YAA2B;AdrFlC,QAAAC,GAAAC;AcsFC,WAAI,OAAO,SAAW,SAAeD,IAAA,OAAO,YAAP,QAAAA,EAAgB,aAC1C,OAAO,QAAQ,YAGtB,KAAK,QAAQ,YACN,KAAK,QAAQ,YAIpB,OAAO,WAAa,OACpB,OAAO,SAAS,iBAAkB,eAG9BC,IAAA,SACK,cAAc,yBAAyB,MAD5C,OAAA,SAAAA,EAEM,aAAa,SAAA,MAAc,OAIlC;EAAA;AA0Cf;AAxHIH,EAAuB,kBAAkB;EACrC,MAAM;IACF,SAAS,CAAA;EACb;EACA,cAAc;EACd,oBAAoB;IAChB,UAAU;IACV,SAAS,CAAA;EACb;EACA,WAAW;EACX,aAAa;EACb,MAAM;EACN,KAAK;EACL,WAAW;AACf;AAvBG,IAAeI,IAAfJ;ACEA,IAAMK,IAAN,cAEGD,EAKR;EAPK,cAAA;AAAA,UAAA,GAAA,SAAA,GAgBH,KAAA,WAA6C,CAAC;EAAA;;;;EAO9C,UAAgB;AACZ,QAAI,OAAO,KAAK,QAAQ,SAAW;AAC1B,WAAA,SAAS,KAAK,QAAQ;aACpB,KAAK,QAAQ;AACf,WAAA,SAAS,IAAI,KAAK,QAAQ;QAC3B,KAAK,QAAQ;QACb,KAAK;MACT;aAEA,OAAO,SAAW,OAClB,OAAO,OAAO,SAAW;AAEpB,WAAA,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,KAAK,KAAK,OAAO;;AAE9D,YAAM,IAAI;QACN;MACJ;EACJ;;;;EAMJ,SAAe;AACX,SAAK,OAAO,OAAO;EAAA;;;;EAMvB,OACI/B,GACAV,GACAC,GACgB;AAChB,WAAO,KAAK,QAAQS,CAAI,EAAE,OAAOV,GAAOC,CAAQ;EAAA;;;;EAMpD,QAAQS,GAAgC;AACpC,WAAK,KAAK,SAASA,CAAI,MACd,KAAA,SAASA,CAAI,IAAI,IAAIF;MACtB,KAAK;MACLE;MACA,KAAK;IACT,IAGG,KAAK,SAASA,CAAI;EAAA;;;;EAM7B,eAAeA,GAAsD;AACjE,WAAK,KAAK,SAAS,aAAaA,CAAI,MAChC,KAAK,SAAS,aAAaA,CAAI,IAAI,IAAIK;MACnC,KAAK;MACL,aAAaL;MACb,KAAK;IACT,IAGG,KAAK,SACR,aAAaA,CACjB;EAAA;;;;EAMJ,wBACIA,GAC+C;AAC/C,WAAK,KAAK,SAAS,uBAAuBA,CAAI,MAC1C,KAAK,SAAS,uBAAuBA,CAAI,IACrC,IAAIO;MACA,KAAK;MACL,uBAAuBP;MACvB,KAAK;IACT,IAGD,KAAK,SACR,uBAAuBA,CAC3B;EAAA;;;;EAMJ,gBAAgBA,GAAuD;AACnE,WAAK,KAAK,SAAS,cAAcA,CAAI,MACjC,KAAK,SAAS,cAAcA,CAAI,IAAI,IAAIQ;MACpC,KAAK;MACL,cAAcR;MACd,KAAK;IACT,IAGG,KAAK,SACR,cAAcA,CAClB;EAAA;;;;EAMJ,MAAMA,GAAoB;AACP;MACXA;MACA,aAAaA;MACb,uBAAuBA;MACvB,cAAcA;IAClB,EAES,QAAQ,CAACA,MAAiB;AAC/B,WAAK,aAAaA,CAAI;IAAA,CACzB;EAAA;;;;EAML,aAAaA,GAAoB;AACzB,SAAK,SAASA,CAAI,MACb,KAAA,SAASA,CAAI,EAAE,YAAY,GAEzB,OAAA,KAAK,SAASA,CAAI;EAC7B;;;;EAMJ,WAAmB;AACR,WAAA,KAAK,OAAO,WAAW;EAAA;;;;EAMlC,aAAmB;AACf,SAAK,OAAO,WAAW;EAAA;AAE/B;AC/KO,IAAMiC,IAAN,cAAgCF,EAKrC;EALK,cAAA;AAAA,UAAA,GAAA,SAAA,GAcH,KAAA,WAAgD,CAAC;EAAA;;;;EAKjD,UAAgB;AACR,QAAAG,IAAK,KAAK,YAAY;AAE1B,SAAK,SAASA;MACV,KAAK,QAAQ,QAAQ;MACrB,KAAK;IACT,GAEA,KAAK,OAAO,GAAG,GAAG,aAAa,MAAM;AACjC,aAAO,OAAO,KAAK,QAAQ,EAAE,QAAQ,CAACpB,MAAY;AAC9CA,UAAQ,UAAU;MAAA,CACrB;IAAA,CACJ;EAAA;;;;EAML,cAAyB;AACrB,QAAI,OAAO,KAAK,QAAQ,SAAW;AAC/B,aAAO,KAAK,QAAQ;AAGxB,QAAI,OAAO,SAAW,OAAe,OAAO,OAAO,KAAO;AACtD,aAAO,OAAO;AAGlB,UAAM,IAAI;MACN;IACJ;EAAA;;;;EAMJ,OACId,GACAV,GACAC,GACkB;AAClB,WAAO,KAAK,QAAQS,CAAI,EAAE,OAAOV,GAAOC,CAAQ;EAAA;;;;EAMpD,QAAQS,GAAkC;AACtC,WAAK,KAAK,SAASA,CAAI,MACd,KAAA,SAASA,CAAI,IAAI,IAAIW;MACtB,KAAK;MACLX;MACA,KAAK;IACT,IAGG,KAAK,SAASA,CAAI;EAAA;;;;EAM7B,eAAeA,GAAsC;AACjD,WAAK,KAAK,SAAS,aAAaA,CAAI,MAChC,KAAK,SAAS,aAAaA,CAAI,IAAI,IAAIgB;MACnC,KAAK;MACL,aAAahB;MACb,KAAK;IACT,IAGG,KAAK,SAAS,aAAaA,CAAI;EAAA;;;;EAM1C,gBAAgBA,GAAuC;AACnD,WAAK,KAAK,SAAS,cAAcA,CAAI,MACjC,KAAK,SAAS,cAAcA,CAAI,IAAI,IAAIiB;MACpC,KAAK;MACL,cAAcjB;MACd,KAAK;IACT,IAGG,KAAK,SAAS,cAAcA,CAAI;EAAA;;;;EAM3C,MAAMA,GAAoB;AACP,KAACA,GAAM,aAAaA,GAAM,cAAcA,CAAI,EAElD,QAAQ,CAACA,MAAS;AACvB,WAAK,aAAaA,CAAI;IAAA,CACzB;EAAA;;;;EAML,aAAaA,GAAoB;AACzB,SAAK,SAASA,CAAI,MACb,KAAA,SAASA,CAAI,EAAE,YAAY,GAEzB,OAAA,KAAK,SAASA,CAAI;EAC7B;;;;EAMJ,WAA+B;AAC3B,WAAO,KAAK,OAAO;EAAA;;;;EAMvB,aAAmB;AACf,SAAK,OAAO,WAAW;EAAA;AAE/B;ACxJO,IAAMmC,IAAN,cAA4BJ,EAKjC;EALK,cAAA;AAAA,UAAA,GAAA,SAAA,GASH,KAAA,WAAgB,CAAC;EAAA;;;;EAKjB,UAAgB;EAAA;;;;EAOhB,OACIK,GACAf,GACAR,GACW;AACX,WAAO,IAAIO,EAAY;EAAA;;;;EAM3B,QAAQgB,GAA4B;AAChC,WAAO,IAAIhB,EAAY;EAAA;;;;EAM3B,eAAegB,GAAmC;AAC9C,WAAO,IAAId,EAAmB;EAAA;;;;EAMlC,wBAAwBc,GAA4C;AAChE,WAAO,IAAIX,EAA4B;EAAA;;;;EAM3C,gBAAgBW,GAAoC;AAChD,WAAO,IAAIV,EAAoB;EAAA;;;;EAMnC,MAAMU,GAAqB;EAAA;;;;EAO3B,aAAaA,GAAqB;EAAA;;;;EAOlC,WAAmB;AACR,WAAA;EAAA;;;;EAMX,aAAmB;EAAA;AAGvB;ACnEA,IAAqBC,IAArB,MAAuD;;;;EAcnD,YAAYpC,GAAyB;AACjC,SAAK,UAAUA,GACf,KAAK,QAAQ,GAER,KAAK,QAAQ,uBACd,KAAK,qBAAqB;EAC9B;;;;EAMJ,QAAQa,GAA2C;AACxC,WAAA,KAAK,UAAU,QAAQA,CAAO;EAAA;;;;EAMzC,UAAgB;AACR,QAAA,KAAK,QAAQ,gBAAgB;AACxB,WAAA,YAAY,IAAIkB,EAA0B;QAC3C,GAAG,KAAK;QACR,SAAS;MAAA,CACZ;aACM,KAAK,QAAQ,gBAAgB;AACpC,WAAK,YAAY,IAAIA,EAA0B,KAAK,OAAO;aACpD,KAAK,QAAQ,gBAAgB;AAC/B,WAAA,YAAY,IAAIA,EAA0B;QAC3C,GAAG,KAAK;QACR,SAAS;QACT,aAAa;MAAA,CAChB;aACM,KAAK,QAAQ,gBAAgB;AACpC,WAAK,YAAY,IAAIC,EAAkB,KAAK,OAAO;aAC5C,KAAK,QAAQ,gBAAgB;AACpC,WAAK,YAAY,IAAIE,EAAc,KAAK,OAAO;aAE/C,OAAO,KAAK,QAAQ,eAAgB,cACpCxC,EAAc,KAAK,QAAQ,WAAW;AAEtC,WAAK,YAAY,IAAI,KAAK,QAAQ,YAAY,KAAK,OAAO;;AAE1D,YAAM,IAAI;QACN,eAAe,OAAO,KAAK,QAAQ,WAAW,IAAI,OAAO,KAAK,QAAQ,WAAW,CAAC;MACtF;EACJ;;;;EAMJ,aAAmB;AACf,SAAK,UAAU,WAAW;EAAA;;;;EAM9B,KAAKmB,GAA6C;AACvC,WAAA,KAAK,UAAU,gBAAgBA,CAAO;EAAA;;;;EAMjD,MAAMA,GAAuB;AACpB,SAAA,UAAU,MAAMA,CAAO;EAAA;;;;EAMhC,aAAaA,GAAuB;AAC3B,SAAA,UAAU,aAAaA,CAAO;EAAA;;;;EAMvC,mBAAyB;AACV,eAAAA,KAAW,KAAK,UAAU;AACjC,WAAK,aAAaA,CAAO;EAC7B;;;;EAMJ,OACIA,GACAxB,GACAC,GACwB;AACxB,WAAO,KAAK,UAAU,OAAOuB,GAASxB,GAAOC,CAAQ;EAAA;;;;EAMzD,QAAQuB,GAA4C;AACzC,WAAA,KAAK,UAAU,eAAeA,CAAO;EAAA;;;;EAMhD,iBAAiBA,GAA8C;AAC3D,QAAI,KAAK,0CAA0C,KAAK,SAAS;AACtD,aAAA,KAAK,UAAU,wBAAwBA,CAAO;AAGzD,UAAM,IAAI;MACN,eAAe,OAAO,KAAK,QAAQ,WAAW,IAAI;QAC9C,KAAK,QAAQ;MAAA,CAChB;IACL;EAAA;EAGI,0CACJwB,GACiD;AAE7C,WAAAA,aAAqBN,KACrBM,aAAqBH;EAAA;;;;EAO7B,WAA+B;AACpB,WAAA,KAAK,UAAU,SAAS;EAAA;;;;;EAOnC,uBAA6B;AAGrB,WAAO,MAAQ,QAAe,OAAA,QAAA,IAAK,SACnC,KAAK,8BAA8B,GAGnC,OAAO,SAAU,cACjB,KAAK,gCAAgC,GAGrC,OAAO,UAAW,cAClB,KAAK,wBAAwB,GAG7B,OAAO,SAAU,YACjB,KAAK,gCAAgC;EACzC;;;;EAMJ,gCAAsC;AAElC,QAAI,KAAK,aAAa;MAClB,CAACI,GAA2BC,MAA2B;AAC/C,aAAK,SAAA,KAELD,EAAQ,QAAQ,IAAI,eAAe,KAAK,SAAA,CAAU,GAIjDC,EAAA;MAAA;IAEb;EAAA;;;;EAMJ,kCAAwC;AACpC,UAAO,aAAa,QAAQ;MACxB,CAACC,OACO,KAAK,SAAA,MACLA,EAAO,QAAQ,aAAa,IAAI,KAAK,SAAS,IAG3CA;IAEf;EAAA;;;;EAMJ,0BAAgC;AACxB,WAAO,OAAO,OAAQ,OACf,OAAA;MACH,CACIC,GACAC,GACAC,MACC;AACG,aAAK,SAAA,KACLA,EAAI,iBAAiB,eAAe,KAAK,SAAA,CAAU;MACvD;IAER;EACJ;;;;EAMJ,kCAAwC;AAC3B,aAAA;MACL;MACA,CAACtD,MAA4B;AACzBA,UAAM,OAAO,aAAa,QAAQ,aAAa,IAC3C,KAAK,SAAS;MAAA;IAE1B;EAAA;AAER;", "names": ["Channel", "event", "callback", "EventFormatter", "namespace", "value", "isConstructor", "obj", "err", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pusher", "name", "options", "data", "formattedEvent", "status", "PusherPrivateChannel", "eventName", "PusherEncryptedPrivateChannel", "PusherPresenceChannel", "k", "member", "SocketIoChannel", "socket", "_callback", "channel", "cb", "SocketIoPrivateChannel", "SocketIoPresenceChannel", "members", "m", "NullChannel", "_event", "NullPrivateChannel", "_eventName", "_data", "NullEncryptedPrivateChannel", "NullPresenceChannel", "_Connector", "token", "_a", "_b", "Connector", "PusherConnector", "SocketIoConnector", "io", "NullConnector", "_name", "Echo", "connector", "request", "next", "config", "_options", "_originalOptions", "xhr"]}