<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\UsesSqliteTestDatabase;
use App\Models\LiveMatch;
use App\Models\Team;
use App\Models\User;
use App\Models\TeamMember;
use App\Models\Cs2Map;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MapBanBladeTest extends TestCase
{
    use UsesSqliteTestDatabase, WithFaker;

    protected $team1;
    protected $team2;
    protected $captain1;
    protected $captain2;
    protected $match;
    protected $maps;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpSqliteDatabase();
        $this->createTestData();
    }

    protected function tearDown(): void
    {
        $this->tearDownSqliteDatabase();
        parent::tearDown();
    }

    protected function createTestData()
    {
        // Создаем карты CS2
        $this->maps = [
            Cs2Map::create(['name' => 'de_dust2', 'image_url' => 'dust2.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_mirage', 'image_url' => 'mirage.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_inferno', 'image_url' => 'inferno.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_nuke', 'image_url' => 'nuke.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_overpass', 'image_url' => 'overpass.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_train', 'image_url' => 'train.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_ancient', 'image_url' => 'ancient.jpg', 'is_active' => true]),
        ];

        // Создаем пользователей-капитанов
        $this->captain1 = User::create([
            'client_nick' => 'Captain1',
            'email' => '<EMAIL>',
            'client_password' => bcrypt('password'),
            'club_id' => '584'
        ]);

        $this->captain2 = User::create([
            'client_nick' => 'Captain2',
            'email' => '<EMAIL>',
            'client_password' => bcrypt('password'),
            'club_id' => '584'
        ]);

        // Создаем команды
        $this->team1 = Team::create([
            'name' => 'Team Alpha',
            'game_id' => 1, // CS2
            'captain_id' => $this->captain1->id,
            'rating' => 1000
        ]);

        $this->team2 = Team::create([
            'name' => 'Team Beta',
            'game_id' => 1, // CS2
            'captain_id' => $this->captain2->id,
            'rating' => 1000
        ]);

        // Добавляем капитанов в команды
        TeamMember::create([
            'team_id' => $this->team1->id,
            'player_id' => $this->captain1->id,
            'role' => 'captain'
        ]);

        TeamMember::create([
            'team_id' => $this->team2->id,
            'player_id' => $this->captain2->id,
            'role' => 'captain'
        ]);

        // Создаем матч
        $this->match = LiveMatch::create([
            'team1_id' => $this->team1->id,
            'team2_id' => $this->team2->id,
            'status' => MatchStatus::MAP_VOTING->value,
            'current_voter' => CurrentVoter::TEAM1->value,
            'current_voting_team' => $this->team1->id
        ]);
    }

    /** @test */
    public function test_blade_template_exists()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $this->assertFileExists($templatePath);
    }

    /** @test */
    public function test_blade_template_contains_required_elements()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие основных элементов
        $this->assertStringContainsString('wire:click="banMap', $content);
        $this->assertStringContainsString('currentVotingTeam', $content);
        $this->assertStringContainsString('bannedMaps', $content);
        $this->assertStringContainsString('availableMaps', $content);
    }

    /** @test */
    public function test_blade_template_contains_map_banning_interface()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие интерфейса бана карт
        $this->assertStringContainsString('banMap', $content);
        $this->assertStringContainsString('unbanMap', $content);
    }

    /** @test */
    public function test_blade_template_contains_team_information()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие информации о командах
        $this->assertStringContainsString('team', $content);
        $this->assertStringContainsString('opponents', $content);
    }

    /** @test */
    public function test_blade_template_contains_voting_status()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие статуса голосования
        $this->assertStringContainsString('voting', $content);
        $this->assertStringContainsString('ход', $content);
    }

    /** @test */
    public function test_blade_template_contains_notifications()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие уведомлений
        $this->assertStringContainsString('alert', $content);
        $this->assertStringContainsString('info', $content);
    }

    /** @test */
    public function test_blade_template_contains_sound_notifications()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие звуковых уведомлений
        $this->assertStringContainsString('audio', $content);
        $this->assertStringContainsString('play', $content);
    }

    /** @test */
    public function test_blade_template_contains_map_display()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие отображения карт
        $this->assertStringContainsString('map', $content);
        $this->assertStringContainsString('image', $content);
    }

    /** @test */
    public function test_blade_template_contains_bootstrap_classes()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие Bootstrap классов
        $this->assertStringContainsString('btn', $content);
        $this->assertStringContainsString('card', $content);
        $this->assertStringContainsString('container', $content);
    }

    /** @test */
    public function test_blade_template_contains_livewire_attributes()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие Livewire атрибутов
        $this->assertStringContainsString('wire:', $content);
        $this->assertStringContainsString('livewire:', $content);
    }

    /** @test */
    public function test_blade_template_contains_conditional_rendering()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие условного рендеринга
        $this->assertStringContainsString('@if', $content);
        $this->assertStringContainsString('@foreach', $content);
    }

    /** @test */
    public function test_blade_template_contains_error_handling()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие обработки ошибок
        $this->assertStringContainsString('error', $content);
        $this->assertStringContainsString('success', $content);
    }

    /** @test */
    public function test_blade_template_contains_refresh_functionality()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие функциональности обновления
        $this->assertStringContainsString('refresh', $content);
        $this->assertStringContainsString('poll', $content);
    }

    /** @test */
    public function test_blade_template_contains_match_information()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие информации о матче
        $this->assertStringContainsString('match', $content);
        $this->assertStringContainsString('команда', $content);
    }

    /** @test */
    public function test_blade_template_contains_map_voting_interface()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие интерфейса голосования по картам
        $this->assertStringContainsString('voting', $content);
        $this->assertStringContainsString('ban', $content);
        $this->assertStringContainsString('unban', $content);
    }

    /** @test */
    public function test_blade_template_contains_team_switching_logic()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие логики переключения команд
        $this->assertStringContainsString('team', $content);
        $this->assertStringContainsString('current', $content);
        $this->assertStringContainsString('voting', $content);
    }

    /** @test */
    public function test_blade_template_contains_map_selection_logic()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие логики выбора карт
        $this->assertStringContainsString('selected', $content);
        $this->assertStringContainsString('карта', $content);
        $this->assertStringContainsString('availableMaps', $content);
    }

    /** @test */
    public function test_blade_template_contains_user_interface_elements()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие элементов пользовательского интерфейса
        $this->assertStringContainsString('button', $content);
        $this->assertStringContainsString('div', $content);
        $this->assertStringContainsString('span', $content);
    }

    /** @test */
    public function test_blade_template_contains_responsive_design()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие адаптивного дизайна
        $this->assertStringContainsString('col-', $content);
        $this->assertStringContainsString('row', $content);
        $this->assertStringContainsString('container', $content);
    }

    /** @test */
    public function test_blade_template_contains_accessibility_features()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие функций доступности
        $this->assertStringContainsString('alt', $content);
        $this->assertStringContainsString('disabled', $content);
        $this->assertStringContainsString('loading', $content);
    }

    /** @test */
    public function test_blade_template_contains_proper_html_structure()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем правильную структуру HTML
        $this->assertStringContainsString('<div', $content);
        $this->assertStringContainsString('</div>', $content);
        $this->assertStringContainsString('<button', $content);
        $this->assertStringContainsString('<span', $content);
    }

    /** @test */
    public function test_blade_template_contains_css_classes_for_styling()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие CSS классов для стилизации
        $this->assertStringContainsString('class', $content);
        $this->assertStringContainsString('btn-', $content);
        $this->assertStringContainsString('card-', $content);
    }

    /** @test */
    public function test_blade_template_contains_javascript_integration()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $content = file_get_contents($templatePath);
        
        // Проверяем наличие интеграции с JavaScript
        $this->assertStringContainsString('script', $content);
        $this->assertStringContainsString('addEventListener', $content);
        $this->assertStringContainsString('console.log', $content);
    }
} 