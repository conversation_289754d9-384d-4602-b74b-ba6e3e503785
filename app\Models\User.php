<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string|null $club_id
 * @property string|null $client_id
 * @property string|null $client_nick
 * @property string|null $client_hash
 * @property string|null $client_password
 * @property string|null $auth_token
 * @property string|null $auth_token_from_date
 * @property string|null $client_status
 * @property int|null $change_password
 * @property int|null $remember_token
 * @property string|null $email
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $avatar
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Team> $captainedTeams
 * @property-read int|null $captained_teams_count
 * @property string|null $password
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TeamInvitation> $invitations
 * @property-read int|null $invitations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MatchResult> $matchResults
 * @property-read int|null $match_results_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\GameMatch> $matches
 * @property-read int|null $matches_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Rating> $ratings
 * @property-read int|null $ratings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\JoinRequest> $requests
 * @property-read int|null $requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Team> $teams
 * @property-read int|null $teams_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAuthToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAuthTokenFromDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereChangePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereClientHash($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereClientNick($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereClientPassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereClientStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * Имя таблицы, связанной с моделью.
     *
     * @var string
     */
    protected $table = 'players';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_nick',
        'email',
        'password',
        'club_id',
        'client_id',
        'auth_token',
        'auth_token_from_date',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_seen_at' => 'datetime', // Добавляем это для автоматического приведения к Carbon
    ];

    /**
     * Переопределение получения атрибута password.
     *
     * @param string $key
     * @return mixed
     */
    public function getAttribute($key)
    {
        if ($key === 'password') {
            $key = 'client_password';
        }

        return parent::getAttribute($key);
    }

    /**
     * Переопределение установки атрибута password.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setAttribute($key, $value)
    {
        if ($key === 'password') {
            $key = 'client_password';
        }

        parent::setAttribute($key, $value);
    }

    /**
     * Аксессор для получения значения пароля.
     *
     * @return string|null
     */
    public function getPasswordAttribute()
    {
        return $this->attributes['client_password'];
    }

    /**
     * Мутатор для установки значения пароля.
     *
     * @param string $value
     * @return void
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['client_password'] = $value;
    }

    /**
     * Получить матчи пользователя.
     */
    public function matches(): HasMany
    {
        return $this->hasMany(GameMatch::class, 'player_id');
    }

    /**
     * Получить результаты матчей пользователя.
     */
    public function matchResults(): HasMany
    {
        return $this->hasMany(MatchResult::class, 'player_id');
    }

    /**
     * Проверяет, является ли пользователь капитаном указанной команды
     *
     * @param int $teamId ID команды
     * @return bool
     */
    public function isCaptainOf($teamId)
    {
        return $this->teams()
            ->where('team_members.player_id', $this->id)
            ->where('team_id', $teamId)
            ->wherePivot('role', 'captain')
            ->exists();
    }

    /**
     * Проверяет, есть ли у пользователя команда для указанной игры
     *
     * @param int $gameId ID игры
     * @return bool
     */
    public function hasTeamForGame($gameId)
    {
        return $this->teams()
            ->whereHas('game', function($query) use ($gameId) {
                $query->where('id', $gameId);
            })
            ->exists();
    }

    /**
     * Получает команду пользователя для указанной игры
     *
     * @param int $gameId ID игры
     * @return \App\Models\Team|null
     */
    public function getTeamForGame($gameId)
    {
        // Проверяем, является ли пользователь капитаном команды для этой игры
        $captainTeam = \App\Models\Team::where('game_id', $gameId)
            ->where('captain_id', $this->id)
            ->first();
        
        if ($captainTeam) {
            return $captainTeam;
        }
        
        // Проверяем, является ли пользователь участником команды для этой игры
        $teamMember = \App\Models\TeamMember::whereHas('team', function($query) use ($gameId) {
            $query->where('game_id', $gameId);
        })->where('player_id', $this->id)->first();
        
        return $teamMember ? $teamMember->team : null;
    }

    /**
     * Получает членство пользователя в команде для указанной игры
     *
     * @param int $gameId ID игры
     * @return \App\Models\TeamMember|null
     */
    public function getTeamMembershipForGame($gameId)
    {
        return \App\Models\TeamMember::whereHas('team', function($query) use ($gameId) {
            $query->where('game_id', $gameId);
        })->where('player_id', $this->id)->first();
    }

    /**
     * Получает роль пользователя в указанной команде
     *
     * @param int $teamId ID команды
     * @return string|null
     */
    public function getRoleInTeam($teamId)
    {
        if ($this->isCaptainOf($teamId)) {
            return 'captain';
        }
        
        $teamMember = \App\Models\TeamMember::where('team_id', $teamId)
            ->where('player_id', $this->id)
            ->first();
        
        return $teamMember ? $teamMember->role : null;
    }

    /**
     * Команды, где пользователь является капитаном
     */
    public function captainedTeams()
    {
        return $this->hasMany(\App\Models\Team::class, 'captain_id');
    }

    /**
     * Команды, в которых состоит пользователь
     */
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_members', 'player_id', 'team_id')
            ->withPivot('role')
            ->withTimestamps();
    }

    /**
     * Получить рейтинги пользователя.
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class, 'player_id');
    }

    /**
     * Получить рейтинг пользователя для конкретной игры.
     * 
     * @param int $gameId ID игры
     * @param int|null $clubId ID клуба (опционально)
     * @param int|null $seasonId ID сезона (опционально)
     * @return Rating|null
     */
    public function getRatingForGame($gameId, $clubId = null, $seasonId = null)
    {
        $query = $this->ratings()->where('game_id', $gameId);
        
        if ($clubId) {
            $query->where('club_id', $clubId);
        }
        
        if ($seasonId) {
            $query->where('season_id', $seasonId);
        }
        
        return $query->first();
    }

    /**
     * Получить или создать рейтинг пользователя для конкретной игры.
     * 
     * @param int $gameId ID игры
     * @param int $clubId ID клуба
     * @param int|null $seasonId ID сезона (опционально)
     * @param int $defaultRating Значение рейтинга по умолчанию
     * @return Rating
     */
    public function getOrCreateRatingForGame($gameId, $clubId, $seasonId = null, $defaultRating = 1000)
    {
        $rating = $this->getRatingForGame($gameId, $clubId, $seasonId);
        
        if (!$rating) {
            $rating = Rating::create([
                'player_id' => $this->id,
                'game_id' => $gameId,
                'club_id' => $clubId,
                'season_id' => $seasonId,
                'game_rating' => $defaultRating
            ]);
        }
        
        return $rating;
    }

    /**
     * Получить приглашения пользователя
     */
    public function invitations(): HasMany
    {
        return $this->hasMany(TeamInvitation::class, 'player_id');
    }

    /**
     * Получить заявки пользователя
     */
    public function requests(): HasMany
    {
        return $this->hasMany(JoinRequest::class, 'player_id');
    }

    /**
     * Получить активные приглашения пользователя
     */
    public function getPendingInvitations()
    {
        return $this->invitations()->where('status', 'pending')->with(['team', 'team.captain'])->get();
    }

    /**
     * Получить активные заявки пользователя
     */
    public function getPendingRequests()
    {
        return $this->requests()->where('status', 'pending')->with(['team', 'team.captain'])->get();
    }

    /**
     * Проверить, есть ли у пользователя активные приглашения
     */
    public function hasPendingInvitations(): bool
    {
        return $this->invitations()->where('status', 'pending')->exists();
    }

    /**
     * Проверить, есть ли у пользователя активные заявки
     */
    public function hasPendingRequests(): bool
    {
        return $this->requests()->where('status', 'pending')->exists();
    }

    /**
     * Проверить, есть ли у пользователя активные приглашения для конкретной игры
     */
    public function hasPendingInvitationsForGame(int $gameId): bool
    {
        return $this->invitations()
            ->where('status', 'pending')
            ->whereHas('team', function($query) use ($gameId) {
                $query->where('game_id', $gameId);
            })
            ->exists();
    }

    /**
     * Проверить, есть ли у пользователя активные заявки для конкретной игры
     */
    public function hasPendingRequestsForGame(int $gameId): bool
    {
        return $this->requests()
            ->where('status', 'pending')
            ->whereHas('team', function($query) use ($gameId) {
                $query->where('game_id', $gameId);
            })
            ->exists();
    }

    /**
     * Проверяет, находится ли пользователь онлайн (был активен в последние 5 минут).
     *
     * @return bool
     */
    public function isOnline(): bool
    {
        return $this->last_seen_at && $this->last_seen_at->diffInMinutes(now()) < 5;
    }
}


