<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
        api: __DIR__.'/../routes/api.php',  // Добавляем путь к API маршрутам
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\LastUserActivity::class,
        ]);
        
        // Добавьте эту строку, если её нет
        $middleware->alias([
            'auth.broadcast' => \Illuminate\Broadcasting\BroadcastManager::class,
            'auth.token' => \App\Http\Middleware\AuthenticateByToken::class,
            'last_user_activity' => \App\Http\Middleware\LastUserActivity::class,
        ]);
    })
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: [
            'auth_token',
            'club_id',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();



