<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $event_id
 * @property int|null $player_id
 * @property int|null $rating
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SeasonRating whereRating($value)
 * @mixin \Eloquent
 */
class SeasonRating extends Model
{
    protected $table = 'events_season_rating';

    protected $fillable = [
        'event_id',
        'player_id',
        'rating',
    ];
}
