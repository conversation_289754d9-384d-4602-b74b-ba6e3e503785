<div>
    <h1>Рейтинг клуба: {{ $clubName }}</h1>

    @if ($ratings->isEmpty())
        <p>Рейтинг для этого клуба не найден или API ключ недействителен.</p>
    @else
        <table class="table">
            <thead>
                <tr>
                    <th>Игрок</th>
                    <th>Игра</th>
                    <th>Рейтинг</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($ratings as $rating)
                    <tr>
                        <td>{{ $rating->player->client_nick ?? 'Неизвестный игрок' }} </td>
                        <td><span class="alert alert-secondary m-0 p-1" role="alert">{{ $rating->game->name ?? 'Неизвестная игра' }}</span></td>
                        <td><span class="alert alert-success m-0 p-1" role="alert">{{ $rating->game_rating }}</span></td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif
</div>
