<!DOCTYPE html>
<html>
<head>
    <title>Test Match Notifications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Match Notifications</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Team 1 - Find Match</h5>
                    </div>
                    <div class="card-body">
                        <iframe src="{{ route('find.match', 1) }}" width="100%" height="600" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Team 2 - Find Match</h5>
                    </div>
                    <div class="card-body">
                        <iframe src="{{ route('find.match', 2) }}" width="100%" height="600" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary" onclick="testTeamAccepted()">
                                Test Team Accepted
                            </button>
                            <button type="button" class="btn btn-danger" onclick="testMatchDeclined()">
                                Test Match Declined
                            </button>
                            <button type="button" class="btn btn-warning" onclick="testMapBanned()">
                                Test Map Banned
                            </button>
                            <button type="button" class="btn btn-info" onclick="testVoterSwitched()">
                                Test Voter Switched
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <label for="matchId" class="form-label">Match ID:</label>
                            <input type="number" class="form-control" id="matchId" value="1" style="width: 100px; display: inline-block;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('js/app.js') }}"></script>
    
    <script>
        function testTeamAccepted() {
            const matchId = document.getElementById('matchId').value;
            fetch('/test-event/team-accepted', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    match_id: matchId,
                    team_id: 2
                })
            }).then(response => response.json())
              .then(data => console.log('Team accepted event sent:', data));
        }
        
        function testMatchDeclined() {
            const matchId = document.getElementById('matchId').value;
            fetch('/test-event/match-declined', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    match_id: matchId,
                    team_id: 2
                })
            }).then(response => response.json())
              .then(data => console.log('Match declined event sent:', data));
        }
        
        function testMapBanned() {
            const matchId = document.getElementById('matchId').value;
            fetch('/test-event/map-banned', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    match_id: matchId,
                    map_id: 1,
                    team_id: 2,
                    banned_maps: [1]
                })
            }).then(response => response.json())
              .then(data => console.log('Map banned event sent:', data));
        }
        
        function testVoterSwitched() {
            const matchId = document.getElementById('matchId').value;
            fetch('/test-event/voter-switched', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    match_id: matchId,
                    current_voter: 'team1'
                })
            }).then(response => response.json())
              .then(data => console.log('Voter switched event sent:', data));
        }
    </script>
</body>
</html>
