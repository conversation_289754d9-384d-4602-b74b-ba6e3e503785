<div x-data="{
    open: false,
    search: @entangle('search').live.debounce.300ms,
    selected: @entangle('selected'),
    getSelectedText() {
        if (!this.selected) return '{{ $placeholder }}';
        
        // Здесь мы не ищем в options, так как их нет.
        // Вместо этого мы будем использовать `selectedText`
        // который будет синхронизироваться из Livewire
        return this.selectedText || '{{ $placeholder }}';
    }
}" class="relative" x-init="
    // console.log('Alpine init');
">
    @if($label)
        <label for="{{ $name }}" class="form-label">
            {{ $label }} @if($required)<span class="text-danger">*</span>@endif
        </label>
    @endif

    <div class="form-control @error($name) is-invalid @enderror cursor-pointer"
         @click="open = !open"
         :class="{'border-primary': open}"
         wire:ignore>
        <div class="d-flex justify-content-between align-items-center">
            <span x-text="getSelectedText()" :class="{'text-muted': !selected}"></span>
            <i class="ri-arrow-down-s-line" :class="{'rotate-180': open}"></i>
        </div>
    </div>

    <div x-show="open" 
         x-transition
         @click.away="open = false"
         class="position-absolute w-100 mt-1 bg-white border rounded shadow-sm z-index-dropdown" style="    width: 93% !important;">
        <div class="p-2">
            <input type="text" 
                   class="form-control form-control-sm" 
                   placeholder="Поиск..." 
                   x-model="search"
                   @click.stop>
        </div>
        
        {{-- Список игроков --}}
        <div class="dropdown-menu-items ps-3" style="max-height: 200px; overflow-y: auto;">
            <div wire:loading.delay.class="d-block" wire:loading.delay.class.remove="d-none" class="d-none text-center p-2">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Загрузка...</span>
                </div>
            </div>
            
            @if(strlen($search) > 1 && $options->count() > 0)
                @foreach($options as $option)
                    <div wire:click="updSelected({{ $option->id }})"
                         @click.stop="selected = {{ $option->id }}; open = false;"
                         class="dropdown-item cursor-pointer"
                         :class="{'active': selected == {{ $option->id }}}">
                         <div class="d-flex align-items-center">
                            <img src="{{ $option->avatar }}" alt="{{ $option->client_nick }}" class="h-8 w-8 rounded-full me-2">
                            <span>{{ $option->client_nick }}</span>
                         </div>
                    </div>
                @endforeach
            @elseif(strlen($search) > 1 && $options->count() === 0)
                <div class="dropdown-item text-muted">
                    Ничего не найдено
                </div>
            @endif
            
            {{-- Кнопка "Загрузить ещё" для пагинации --}}
            @if(strlen($search) > 1 && $options->hasMorePages())
                <div class="dropdown-item">
                    <button wire:click.prevent="$parent.nextPage" class="btn btn-link w-100 text-center">
                        Загрузить ещё
                    </button>
                </div>
            @endif
        </div>
    </div>
    
    @error($name) <div class="invalid-feedback d-block">{{ $message }}</div> @enderror

    <style>
        .z-index-dropdown {
            z-index: 1000;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .rotate-180 {
            transform: rotate(180deg);
        }
    </style>
</div>