<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class AuthenticateByTokenTest extends TestCase
{
    /**
     * Тест успешной аутентификации по токену.
     */
    public function test_can_authenticate_with_valid_token(): void
    {
        // Создаем пользователя с токеном
        $userId = DB::table('players')->insertGetId([
            'client_nick' => 'Token User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'club_id' => 123,
            'auth_token' => 'valid_token_123',
            'auth_token_from_date' => Carbon::now()->addDays(1), // Токен действителен 1 день
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Проверяем, что пользователь создан
        $this->assertDatabaseHas('players', [
            'client_nick' => 'Token User',
            'auth_token' => 'valid_token_123',
        ]);

        // Делаем запрос с токеном
        $response = $this->get('/index-wcf.php?f=statistic&club=123&auth_token=valid_token_123');
        
        // Проверяем, что пользователь авторизован и перенаправлен на dashboard
        $response->assertRedirect('/dashboard');
        $this->assertTrue(auth()->check());
        $this->assertEquals($userId, auth()->id());
    }

    /**
     * Тест неудачной аутентификации с истекшим токеном.
     */
    public function test_cannot_authenticate_with_expired_token(): void
    {
        // Создаем пользователя с истекшим токеном
        DB::table('players')->insert([
            'client_nick' => 'Expired Token User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'club_id' => 456,
            'auth_token' => 'expired_token_456',
            'auth_token_from_date' => Carbon::now()->subDays(1), // Токен истек вчера
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Проверяем, что пользователь создан
        $this->assertDatabaseHas('players', [
            'client_nick' => 'Expired Token User',
            'auth_token' => 'expired_token_456',
        ]);

        // Делаем запрос с истекшим токеном
        $response = $this->get('/index-wcf.php?f=statistic&club=456&auth_token=expired_token_456');
        
        // Проверяем, что пользователь не авторизован и перенаправлен на login
        $response->assertRedirect('/login');
        $this->assertFalse(auth()->check());
    }

    /**
     * Тест неудачной аутентификации с неверным club_id.
     */
    public function test_cannot_authenticate_with_wrong_club_id(): void
    {
        // Создаем пользователя с токеном
        DB::table('players')->insert([
            'client_nick' => 'Club Token User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'club_id' => 789,
            'auth_token' => 'club_token_789',
            'auth_token_from_date' => Carbon::now()->addDays(1), // Токен действителен 1 день
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Проверяем, что пользователь создан
        $this->assertDatabaseHas('players', [
            'client_nick' => 'Club Token User',
            'auth_token' => 'club_token_789',
        ]);

        // Делаем запрос с правильным токеном, но неверным club_id
        $response = $this->get('/index-wcf.php?f=statistic&club=999&auth_token=club_token_789');
        
        // Проверяем, что пользователь не авторизован и перенаправлен на login
        $response->assertRedirect('/login');
        $this->assertFalse(auth()->check());
    }

    /**
     * Тест неудачной аутентификации с неверным токеном.
     */
    public function test_cannot_authenticate_with_invalid_token(): void
    {
        // Создаем пользователя
        DB::table('players')->insert([
            'client_nick' => 'Invalid Token User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'club_id' => 321,
            'auth_token' => 'valid_token_321',
            'auth_token_from_date' => Carbon::now()->addDays(1),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Делаем запрос с неверным токеном
        $response = $this->get('/index-wcf.php?f=statistic&club=321&auth_token=wrong_token');
        
        // Проверяем, что пользователь не авторизован и перенаправлен на login
        $response->assertRedirect('/login');
        auth()->logout();
        $this->assertFalse(auth()->check());
    }

    /**
     * Настройка перед каждым тестом
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем необходимые таблицы для тестов
        $this->createTestTables();
    }

    /**
     * Создает необходимые таблицы для тестов
     */
    protected function createTestTables(): void
    {
        // Создаем таблицу players, если она не существует
        if (!Schema::hasTable('players')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS players (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_nick TEXT,
                    email TEXT UNIQUE,
                    password TEXT,
                    club_id INTEGER,
                    auth_token TEXT,
                    auth_token_from_date TIMESTAMP,
                    remember_token TEXT NULL,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
    }
}