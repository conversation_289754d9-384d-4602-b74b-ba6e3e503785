<?php

namespace Database\Seeders;

use App\Models\Club;
use App\Models\Game;
use App\Models\Rating;
use App\Models\User;
use Illuminate\Database\Seeder;

class RatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Получаем всех пользователей, игры и клубы
        $users = User::all();
        $games = Game::all();
        $clubs = Club::all();
        
        // Для каждого пользователя создаем несколько рейтингов
        $users->each(function ($user) use ($games, $clubs) {
            // Выбираем случайное количество игр для оценки (1-3)
            $gamesToRate = $games->random(rand(1, min(3, $games->count())));
            
            foreach ($gamesToRate as $game) {
                // Выбираем случайное количество клубов для оценки (1-3)
                $clubsToRate = $clubs->random(rand(1, min(3, $clubs->count())));
                
                foreach ($clubsToRate as $club) {
                    Rating::create([
                        'player_id' => $user->id, // Изменено с user_id на player_id
                        'game_id' => $game->id,
                        'club_id' => $club->id,
                        'game_rating' => rand(100, 9999),
                    ]);
                }
            }
        });
    }
}

