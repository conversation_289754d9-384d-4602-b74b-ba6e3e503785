<?php

namespace App\Services\Interfaces;

use App\Models\TeamInvitation;
use Illuminate\Support\Collection;

interface InvitationManagerInterface
{
    /**
     * Отправляет приглашение игроку в команду.
     */
    public function sendInvitation(int $teamId, int $playerId, int $inviterId): TeamInvitation;

    /**
     * Отменяет отправленное приглашение.
     */
    public function cancelInvitation(int $invitationId, int $captainId): bool;

    /**
     * Принимает приглашение в команду.
     */
    public function acceptInvitation(int $invitationId, int $playerId): bool;

    /**
     * Отклоняет приглашение в команду.
     */
    public function declineInvitation(int $invitationId, int $playerId): bool;

    /**
     * Получает список ожидающих приглашений, отправленных командой.
     */
    public function getPendingInvitationsForTeam(int $teamId): Collection;

    /**
     * Получает список приглашений, полученных игроком.
     */
    public function getReceivedInvitationsForPlayer(int $playerId): Collection;
}