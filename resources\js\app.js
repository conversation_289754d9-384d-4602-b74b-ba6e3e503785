import './bootstrap';


window.Echo.channel('test-channel')
    .listen('TestPusherEvent', (e) => {
        console.log('Received event (app.js):', e.message);
        Livewire.dispatch('add-message-from-pusher', { message: e.message });
        console.log('Dispatched Livewire event: add-message-from-pusher with message:', e.message);
    });

document.addEventListener('livewire:initialized', () => {
    // Убедитесь, что `userId` доступен в глобальном контексте
    // Например, можно передать его из Blade-шаблона как <script> window.userId = {{ auth()->id() }}; </script>

    // Проверяем что Echo инициализирован
    if (typeof window.Echo === 'undefined') {
        console.log('Echo not available');
        return;
    }

    // Проверяем авторизацию пользователя
    const userId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
    if (!userId) {
        console.log('User not authenticated');
        return;
    } else {
        console.log('Authenticated user ID:', userId);
    }

    console.log('Setting up Echo listeners for user:', userId);
    console.log('Current user ID for Echo subscription:', userId);
    
    const channel = window.Echo.private(`player.${userId}`);
    
    channel.subscribed(() => {
        console.log(`Successfully subscribed to private channel: player.${userId}`);
    }).error((error) => {
        console.error(`Error subscribing to private channel player.${userId}:`, error);
    });

    channel.listen('.team.invitation.sent', (event) => {
        console.log('Получено новое приглашение:', event);
        Livewire.dispatch('invitation-received', { type: 'sent', data: event });
    });

    channel.listen('.team.invitation.cancelled', (event) => {
        console.log('Приглашение отменено:', event);
        Livewire.dispatch('invitation-cancelled', { type: 'cancelled', data: event });
    });
    
    channel.listen('.invitation.declined', (event) => {
        console.log('Приглашение отклонено (полные данные):', event);
        Livewire.dispatch('invitation-declined');
    });

    channel.listen('team.member.kicked', (e) => {
        console.log('Team member kicked event received:', e);
        console.log('Event data:', JSON.stringify(e, null, 2));
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('showNotification', {
                type: 'error',
                message: `Вас исключили из команды "${e.teamName || 'Неизвестная команда'}"`
            });
            Livewire.dispatch('memberKicked');
        }
    });

    channel.listen('join.request.accepted', (e) => {
        console.log('Join request accepted:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('joinRequestAccepted');
            Livewire.dispatch('showNotification', {
                type: 'success',
                message: `Ваша заявка в команду "${e.team.name}" была принята!`
            });
        }
    });

    channel.listen('join.request.rejected', (e) => {
        console.log('Join request rejected:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('joinRequestRejected');
            Livewire.dispatch('showNotification', {
                type: 'warning',
                message: `Ваша заявка в команду "${e.team.name}" была отклонена`
            });
        }
    });

    channel.listen('join.request.sent', (e) => {
        console.log('Join request sent event received:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('refreshTeamList');
        }
    });
    channel.listen('.team.created', (e) => {
        console.log('Team created event received:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('teamCreated');
        }
    });

    channel.listen('.team.disbanded', (e) => {
        console.log('Team disbanded event received:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('teamDisbanded');
        }
    });

    channel.listen('.team.member.left', (e) => {
        console.log('Team member left event received:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('teamMemberLeft');
        }
    });

    channel.listen('.team.member.self.left', (e) => {
        console.log('Team member self left event received:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('teamMemberSelfLeft', { teamId: e.teamId, playerId: e.playerId });
        }
    });

    channel.listen('.invitation.accepted', (e) => {
        console.log('Invitation accepted event received (app.js):', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('invitation-accepted', { teamId: e.teamId, captainId: e.captainId }); // Передаем данные события
        }
    });

    channel.listen('.team.member.joined', (e) => {
        console.log('Team member joined event received:', e);
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('teamMemberJoined');
        }
    });
});




