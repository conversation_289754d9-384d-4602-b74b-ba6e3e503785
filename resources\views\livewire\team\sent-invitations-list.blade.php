<div>
    @if(count($sentInvitations) > 0)
        <div class="card mb-4 mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Отправленные приглашения</h6>
            </div>
            <ul class="list-group list-group-flush">
                @foreach($sentInvitations as $invitation)
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        {{-- <span>{{ $invitation['user']['id'] }}</span> --}}
                        @if (isset($invitation['user']['avatar']))
                            <img src="{{ asset($invitation['user']['avatar']) }}"
                                alt="Аватар" class="rounded-circle me-2"
                                style="width: 24px; height: 24px;">
                        @else
                            <svg class="rounded-circle me-2 bg-secondary"
                                style="width: 24px; height: 24px;" fill="currentColor"
                                viewBox="0 0 24 24">
                                <path
                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                            </svg>
                        @endif
                        {{ $invitation['user']['client_nick'] }}
                        <span class="badge bg-warning text-dark ms-2">Ожидает
                            ответа</span>
                    </div>
                    <button class="btn btn-sm btn-outline-danger"
                        wire:click="cancelInvitation({{ $invitation['id'] }})">
                        <i class="ri-close-line"></i> Отменить
                    </button>
                </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>
