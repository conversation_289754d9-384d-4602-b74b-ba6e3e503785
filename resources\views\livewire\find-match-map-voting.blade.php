<div>
    @if ($foundMatch)
        @if ($currentVotingTeam && (int)$currentVotingTeam->id === (int)$teamId)
            <p>Ваш ход! Выберите карту для бана:</p>
            <ul class="list-group">
                @foreach ($availableMaps as $map)
                    @if (!in_array($map['id'], $bannedMaps))
                        <li class="list-group-item">
                            <button wire:click="banMap({{ $map['id'] }})" class="btn btn-danger">{{ $map['name'] }}</button>
                        </li>
                    @endif
                @endforeach
            </ul>
        @else
            <p class="text-muted">Ожидание хода соперника...</p>
        @endif

        <p>Забаненные карты:</p>
        <ul class="list-group">
            @foreach ($bannedMaps as $mapId)
                <li class="list-group-item">{{ $availableMapsById[$mapId]['name'] }}</li>
            @endforeach
        </ul>
    @endif
</div>