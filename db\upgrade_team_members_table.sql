﻿DELIMITER $$

DROP PROCEDURE IF EXISTS `upgrade_team_members_table`$$

CREATE PROCEDURE `upgrade_team_members_table`()
BEGIN
    -- Удаляем внешние ключи
    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND CONSTRAINT_NAME = 'team_members_ibfk_1'
    ) THEN
        ALTER TABLE rgtournament.team_members
        DROP FOREIGN KEY team_members_ibfk_1;
    END IF;

    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND CONSTRAINT_NAME = 'team_members_ibfk_2'
    ) THEN
        ALTER TABLE rgtournament.team_members
        DROP FOREIGN KEY team_members_ibfk_2;
    END IF;

    -- Проверяем, существует ли столбец id
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND COLUMN_NAME = 'id'
    ) THEN
        -- Добавляем столбец id как INT UNSIGNED AUTO_INCREMENT и делаем его частью PRIMARY KEY
        ALTER TABLE rgtournament.team_members
        ADD COLUMN id INT UNSIGNED AUTO_INCREMENT FIRST,
        DROP PRIMARY KEY,
        ADD PRIMARY KEY (id, player_id, team_id);
    END IF;

    -- Проверяем, является ли id UNSIGNED
    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND COLUMN_NAME = 'id'
          AND DATA_TYPE = 'int'
          AND COLUMN_TYPE NOT LIKE '%unsigned%'
    ) THEN
        -- Изменяем id на UNSIGNED
        ALTER TABLE rgtournament.team_members
        MODIFY COLUMN id INT UNSIGNED AUTO_INCREMENT;
    END IF;

    -- Добавление поля created_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND COLUMN_NAME = 'created_at'
    ) THEN
        ALTER TABLE rgtournament.team_members
        ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
    END IF;

    -- Добавление поля updated_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND COLUMN_NAME = 'updated_at'
    ) THEN
        ALTER TABLE rgtournament.team_members
        ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
    END IF;

    -- Воссоздаем внешние ключи
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND CONSTRAINT_NAME = 'team_members_ibfk_1'
    ) THEN
        ALTER TABLE rgtournament.team_members
        ADD CONSTRAINT team_members_ibfk_1
        FOREIGN KEY (player_id) REFERENCES rgtournament.players (id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'team_members'
          AND CONSTRAINT_NAME = 'team_members_ibfk_2'
    ) THEN
        ALTER TABLE rgtournament.team_members
        ADD CONSTRAINT team_members_ibfk_2
        FOREIGN KEY (team_id) REFERENCES rgtournament.teams (id);
    END IF;

END$$

DELIMITER ;

-- Вызов процедуры
CALL upgrade_team_members_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `upgrade_team_members_table`;