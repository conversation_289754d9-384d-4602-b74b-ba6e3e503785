<?php

namespace App\Livewire;

use App\Models\Club;
use App\Services\RatingService;
use Livewire\Component;

class ClubRating extends Component
{
    public $clubId;
    public $clubName;
    public $ratings;
    public $apiKey;

    protected $queryString = ['apiKey'];

    public function mount($clubId)
    {
        $this->clubId = $clubId;
        $this->loadClubData();
        $this->loadRatings();
    }

    public function loadClubData()
    {
        $club = Club::where('club_id', $this->clubId)->first();
        if ($club) {
            $this->clubName = $club->club_name;
        } else {
            $this->clubName = 'Клуб не найден';
        }
    }

    public function loadRatings()
    {
        // Проверяем наличие API ключа
        if (!$this->apiKey || $this->apiKey !== config('app.api_key')) { // Предполагается, что API ключ хранится в config/app.php
            $this->ratings = collect(); // Пустая коллекция, если ключ неверный или отсутствует
            return;
        }

        $ratingService = new RatingService();
        $this->ratings = $ratingService->getClubRatings($this->clubId);
    }

    public function render()
    {
        return view('livewire.club-rating', [
            'clubName' => $this->clubName,
            'ratings' => $this->ratings,
        ]);
    }
}
