<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\TokenService;
use Illuminate\Support\Facades\Log;

class AuthenticateByToken
{
    protected TokenService $tokenService;
    
    public function __construct(TokenService $tokenService)
    {
        $this->tokenService = $tokenService;
    }
    
    public function handle(Request $request, Closure $next)
    {
        if ($request->has(['auth_token', 'club'])) {
            $guard = Auth::guard('token');
            
            if ($guard->check()) {
                Auth::login($guard->user());
            } else {
                // Получаем детальную причину из TokenService
                $token = $request->query('auth_token');
                $clubId = $request->query('club');
                
                $result = $this->tokenService->validateToken($token, (int)$clubId);
                
                // Перенаправляем на специальную страницу ошибок
                return redirect()->route('auth.token.error', [
                    'errorType' => $result,
                    'token' => $token,
                    'clubId' => $clubId
                ]);
            }
        }

        return $next($request);
    }
}







