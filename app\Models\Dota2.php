<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $club_id
 * @property int|null $player_id
 * @property string|null $date
 * @property string|null $log
 * @property string|null $log_gameover
 * @property int|null $victory
 * @property int|null $score
 * @property string|null $math_score
 * @property int|null $match_id
 * @property int|null $hero_id
 * @property string|null $date_scan
 * @property int|null $col_scan
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereColScan($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereDateScan($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereHeroId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereLog($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereLogGameover($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereMatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereMathScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dota2 whereVictory($value)
 * @mixin \Eloquent
 */
class Dota2 extends Model
{
    protected $table = 'matches_dota2';

    protected $fillable = [
        'club_id',
        'player_id',
        'date',
        'log',
        'log_gameover',
        'victory',
        'score',
        'math_score',
        'match_id',
        'hero_id',
        'date_scan',
        'col_scan',
    ];
}
