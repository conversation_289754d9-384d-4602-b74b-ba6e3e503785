<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $club_id
 * @property int|null $player_id
 * @property string|null $date
 * @property string|null $log
 * @property string|null $log_gameover
 * @property int|null $victory
 * @property int|null $score
 * @property string|null $math_score
 * @property string|null $date_scan
 * @property int|null $col_scan
 * @property string|null $gamemode
 * @property string|null $mapname
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereColScan($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereDateScan($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereGamemode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereLog($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereLogGameover($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereMapname($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereMathScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg wherePlayerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pubg whereVictory($value)
 * @mixin \Eloquent
 */
class Pubg extends Model
{
    protected $table = 'matches_pubg';

    protected $fillable = [
        'club_id',
        'player_id',
        'date',
        'log',
        'log_gameover',
        'victory',
        'score',
        'math_score',
        'date_scan',
        'col_scan',
        'gamemode',
        'mapname',
    ];
}
