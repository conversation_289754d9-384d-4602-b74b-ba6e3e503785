<?php

namespace App\Livewire\Modals;

use Livewire\Component;
use App\Models\GameMatch;
use App\Models\Cs2Map;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class CreateMatchModal extends Component
{
    public $open = false; // Состояние видимости модального окна
    public $selectedTeamId = null; // ID команды, для которой создается матч
    public $bannedMaps = [];
    public $maxBans = 6;
    public $availableMaps = [];

    public $newMatch = [
        'game_type' => 'cs2',
        'session_id' => '',
        'match_id' => ''
    ];

    /**
     * Слушатель события для открытия модального окна
     */
    #[On('open-create-match-modal')]
    public function openModal($teamId)
    {
        $this->selectedTeamId = $teamId;
        $this->resetNewMatch(); // Сбрасываем форму при каждом открытии
        $this->loadAvailableMaps(); // Загружаем карты
        $this->bannedMaps = Session::get('banned_maps', []); // Загружаем забаненные карты из сессии
        $this->open = true;
    }

    /**
     * Слушатель события для закрытия модального окна
     */
    #[On('close-create-match-modal')]
    public function closeModal()
    {
        $this->open = false;
        $this->reset(['newMatch', 'bannedMaps', 'selectedTeamId']); // Полностью сбрасываем состояние
        Session::forget('banned_maps'); // Очищаем сессию с банами
    }

    /**
     * Сбросить данные нового матча
     */
    public function resetNewMatch()
    {
        $this->newMatch = [
            'game_type' => 'cs2',
            'session_id' => '',
            'match_id' => ''
        ];
        $this->bannedMaps = [];
        Session::forget('banned_maps');
    }

    /**
     * Загрузить доступные карты для CS2
     */
    protected function loadAvailableMaps()
    {
        $maps = Cs2Map::where('is_active', true)->get();
        $this->availableMaps = $maps->map(function($map) {
            return [
                'id' => $map->id,
                'name' => $map->name,
                'image_url' => $map->image_url
            ];
        })->toArray();
    }

    /**
     * Забанить карту
     */
    public function banMap($mapId)
    {
        if (count($this->bannedMaps) >= $this->maxBans) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Достигнут лимит банов карт'
            ]);
            return;
        }

        if (!in_array($mapId, $this->bannedMaps)) {
            $this->bannedMaps[] = $mapId;
            Session::put('banned_maps', $this->bannedMaps);
        }
    }

    /**
     * Разбанить карту
     */
    public function unbanMap($mapId)
    {
        $this->bannedMaps = array_diff($this->bannedMaps, [$mapId]);
        Session::put('banned_maps', $this->bannedMaps);
    }

    /**
     * Создать новый матч
     */
    public function createMatch()
    {
        // Проверка на капитана должна быть в родительском компоненте или на уровне политики,
        // но для надежности можно продублировать здесь.
        // Или, что лучше, передать флаг, что пользователь - капитан.
        
        $this->validate([
            'newMatch.game_type' => 'required|in:cs2',
            'newMatch.session_id' => 'required|string',
            'newMatch.match_id' => 'required|string'
        ]);

        try {
            $match = GameMatch::create([
                'game_type' => $this->newMatch['game_type'],
                'session_id' => $this->newMatch['session_id'],
                'match_id' => $this->newMatch['match_id'],
                'user_id' => Auth::id(), // Пользователь, создавший матч
                'date_scan' => now(),
                'col_scan' => 0,
            ]);

            // Сохраняем информацию о забаненных картах в лог матча
            if (count($this->bannedMaps) > 0) {
                $mapDetails = Cs2Map::whereIn('id', $this->bannedMaps)->pluck('name')->toArray();
                $logData = [
                    'session_id' => $this->newMatch['session_id'],
                    'match_id' => $this->newMatch['match_id'],
                    'banned_maps' => $mapDetails,
                    'team_id' => $this->selectedTeamId
                ];
                $match->update(['log' => json_encode($logData)]);
            }

            $this->closeModal(); // Закрываем модальное окно
            $this->dispatch('matchCreated'); // Оповещаем родителя о создании матча
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Матч успешно создан'
            ]);
        } catch (\Exception $e) {
            \Log::error('Ошибка при создании матча: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Произошла ошибка при создании матча: ' . $e->getMessage()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.modals.create-match-modal', [
            'openModal' => $this->open,
            'bannedMaps' => $this->bannedMaps,
            'availableMaps' => $this->availableMaps,
            'maxBans' => $this->maxBans,
            'newMatch' => $this->newMatch
        ]);
    }
}