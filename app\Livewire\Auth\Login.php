<?php

namespace App\Livewire\Auth;

use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class Login extends Component
{
    public $login;
    public $password;
    public $remember = false;

    protected $rules = [
        'login' => 'required|string',
        'password' => 'required|string',
    ];

    public function userLogin() {
        $this->validate([
            'login' => 'required|string',
            'password' => 'required|string',
        ], [
            'login.required' => 'Пожалуйста, введите логин или email',
            'login.string' => 'Логин должен быть текстом',
            'password.required' => 'Пожалуйста, введите пароль',
            'password.string' => 'Пароль должен быть текстом',
        ]); 

        try {
            // Ищем пользователя по логину или email
            $user = User::where(function($query) {
                    $query->where('client_nick', trim($this->login))
                          ->orWhere('email', trim($this->login));
                })
                ->first();
            
            // Если пользователь не найден или пароль не совпадает
            if (!$user || !Hash::check($this->password, $user->password)) {
                $this->addError('login', 'Неверные учетные данные');
                return;
            }

            Auth::login($user, $this->remember);
            return redirect()->intended('/team');
        } catch (\Exception $e) {
            // Логируем ошибку
            \Log::error('Error in userLogin: ' . $e->getMessage());
            
            // В случае ошибки возвращаем страницу с ошибкой
            $this->addError('login', 'Произошла ошибка при входе');
            return;
        }
    }

    public function render()
    {
        return view('livewire.auth.login');
    }
}





