<?php

namespace Tests\Unit;

use Tests\TestCase;

class DatabaseConnectionTest extends TestCase
{
    /**
     * Проверяет, что подключение к базе данных работает.
     */
    public function test_database_connection_works()
    {
        // Проверяем, что используется правильное подключение
        $connection = config('database.default');
        $this->assertEquals('sqlite', $connection);

        // Получаем информацию о базе данных
        $database = config('database.connections.' . $connection . '.database');
        $this->assertEquals(':memory:', $database);
    }
}

