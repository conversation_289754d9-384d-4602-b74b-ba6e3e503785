<div>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <a href="{{ route('team') }}" class="btn btn-sm btn-outline-primary" wire:navigate="">
                <i class="ri-arrow-left-line me-1"></i> Вернуться
            </a>
            
            <h5 class="card-title mb-0">Рейтинг игроков</h5>
            <div class="d-flex gap-2">
                <div class="d-flex align-items-center gap-2">
                    <button wire:click="gotoMyPosition" class="btn btn-primary btn-sm" style="white-space: nowrap;">
                        Мое место @if($playerPosition)<span class="badge bg-label-dark ms-2">{{ $playerPosition }}</span>@endif
                    </button>
                    <select wire:model.live="ratingType" class="form-select form-select-sm">
                        <option value="club" @if($ratingType === 'club') selected @endif>Клубный рейтинг</option>
                        <option value="general" @if($ratingType === 'general') selected @endif>Общий рейтинг</option>
                        <option value="season" @if($ratingType === 'season') selected @endif>Сезонный рейтинг</option>
                    </select>                    
                    <select wire:model.live="selectedGameId" class="form-select form-select-sm">
                        <option value="">Все игры</option>
                        @foreach($games as $game)
                            <option value="{{ $game->id }}">{{ $game->name }}</option>
                        @endforeach
                    </select>
    
                    @if($ratingType === 'season')
                        <select wire:model.live="selectedSeasonId" class="form-select form-select-sm">
                            <option value="">Все сезоны</option>
                            @foreach($seasons as $season)
                                <option value="{{ $season->id }}">{{ $season->name }}</option>
                            @endforeach
                        </select>
                    @endif
    
                    <select wire:model.live="perPage" class="form-select form-select-sm">
                        <option value="10">10 записей</option>
                        <option value="25">25 записей</option>
                        <option value="50">50 записей</option>
                        <option value="100">100 записей</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Позиция</th>
                            <th>Игрок</th>
                            @if($ratingType === 'general')
                                <th>Клуб</th>
                                <th>Игра</th>
                            @else
                                <th>Сезон</th>
                            @endif
                            <th>Рейтинг</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($ratings as $rating)
                            <tr class="{{ $rating->player_id == $highlightPlayerId ? 'table-primary' : '' }}" 
                                id="{{ $rating->player_id == $highlightPlayerId ? 'highlighted-row' : '' }}">
                                <td>{{ $rating->position }}</td>
                                <td>
                                    <a href="{{ route('player.show', ['player' => $rating->player_id]) }}" class="text-decoration-none">
                                        {{ $rating->player?->client_nick }}
                                    </a>
                                </td>
                                @if($ratingType === 'general')
                                    <td>
                                        @if($rating->club)
                                            <a href="{{ route('club.show', ['club' => $rating->club_id]) }}" class="text-decoration-none">
                                                {{ $rating->club->name }}
                                            </a>
                                        @else
                                            <span class="text-muted">Без клуба</span>
                                        @endif
                                    </td>
                                    <td>{{ $rating->game->name }}</td>
                                    <td>{{ $rating->game_rating }}</td>
                                @else
                                    <td>event</td>
                                    <td>{{ $rating->game_rating }}</td>
                                @endif
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">Нет данных о рейтинге</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $ratings->links() }}
            </div>
        </div>
    </div>    

    @if($highlightPlayerId)
    <style>
        .highlighted-row {
            animation: highlight 2s ease-in-out;
            background-color: #e3f2fd !important;
        }
        @keyframes highlight {
            0% { background-color: #e3f2fd; }
            50% { background-color: #bbdefb; }
            100% { background-color: #e3f2fd; }
        }
    </style>
    <script>
        function scrollToHighlightedRow() {
            const highlightedRow = document.getElementById('highlighted-row');
            if (highlightedRow) {
                // Плавно прокручиваем к подсвеченной строке
                highlightedRow.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center'
                });
                
                // Добавляем временную анимацию подсветки
                highlightedRow.style.transition = 'background-color 0.5s';
                highlightedRow.style.backgroundColor = '#e3f2fd';
                
                // Возвращаем исходный цвет через 2 секунды
                setTimeout(() => {
                    highlightedRow.style.backgroundColor = '';
                }, 2000);
            }
        }

        // Функция для повторных попыток прокрутки
        function retryScrollToHighlightedRow(attempts = 0) {
            if (attempts >= 5) return; // Максимум 5 попыток
            
            const highlightedRow = document.getElementById('highlighted-row');
            if (!highlightedRow) {
                setTimeout(() => retryScrollToHighlightedRow(attempts + 1), 500);
                return;
            }
            
            scrollToHighlightedRow();
        }

        // Слушаем событие инициализации Livewire
        document.addEventListener('livewire:initialized', () => {
            retryScrollToHighlightedRow();
        });

        // Также слушаем событие навигации Livewire
        document.addEventListener('livewire:navigated', () => {
            retryScrollToHighlightedRow();
        });

        // Пробуем прокрутить сразу после загрузки страницы
        document.addEventListener('DOMContentLoaded', () => {
            retryScrollToHighlightedRow();
        });
    </script>
    @endif
</div>