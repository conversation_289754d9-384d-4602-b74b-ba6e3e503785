<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\On;
use App\Events\TestPusherEvent;

class PusherTest extends Component
{
    public $message = '';
    #[Modelable]
    public $receivedMessages = [];

    #[On('add-message-from-pusher')]
    public function addMessageFromPusher($message)
    {
        \Log::info('Received message from Pusher (Livewire): ' . $message);
        $this->addMessage($message);
    }


    public function sendMessage()
    {
        // TestPusherEvent::dispatch($this->message);
        \Log::info('Sending TestPusherEvent with message: ' . $this->message);
        event(new TestPusherEvent($this->message));
            
        $this->message = '';
    }

    public function addMessage($message)
    {
        $this->receivedMessages = array_merge($this->receivedMessages, [$message]);
        $this->dispatch('$refresh'); // Принудительно перерисовываем компонент
    }

    public function render()
    {
        return view('livewire.pusher-test');
    }
}