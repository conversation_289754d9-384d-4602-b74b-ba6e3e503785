<?php

namespace App\Livewire\Modals;

use Livewire\Component;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\LiveMatch;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CreateTestMatchModal extends Component
{
    public function createTestMatch($teamId)
    {
        try {
            DB::beginTransaction();
            
            // Проверяем, есть ли уже активный матч для этой команды
            $existingMatch = LiveMatch::where(function($query) use ($teamId) {
                $query->where('team1_id', $teamId)
                      ->orWhere('team2_id', $teamId);
            })->whereIn('status', [
                MatchStatus::READY_CHECK->value,
                MatchStatus::MAP_VOTING->value,
                MatchStatus::LIVE->value
            ])->first();
            
            if ($existingMatch) {
                throw new \Exception('У вашей команды уже есть активный матч');
            }
            
            // Получаем команду
            $team = Team::find($teamId);
            if (!$team) {
                throw new \Exception('Команда не найдена');
            }
            
            // Ищем или создаем команду-соперника
            $opponentTeam = Team::where('id', '!=', $teamId)
                ->where('game_id', $team->game_id)
                ->first();
                
            if (!$opponentTeam) {
                // Создаем тестовую команду
                $opponentTeam = new Team();
                $opponentTeam->name = 'Тестовая команда';
                $opponentTeam->description = 'Автоматически созданная команда для тестирования';
                $opponentTeam->game_id = $team->game_id;
                $opponentTeam->captain_id = Auth::id(); // Временно назначаем текущего пользователя капитаном
                $opponentTeam->rating = rand(1000, 2000);
                $opponentTeam->save();
                
                // Создаем капитана для тестовой команды
                $testUser = \App\Models\User::first(); // Берем первого пользователя
                if ($testUser) {
                    \App\Models\TeamMember::create([
                        'team_id' => $opponentTeam->id,
                        'player_id' => $testUser->id,
                        'role' => 'captain'
                    ]);
                }
            }
            
            // Создаем матч
            $match = new LiveMatch();
            $match->team1_id = $teamId;
            $match->team2_id = $opponentTeam->id;
            $match->status = MatchStatus::READY_CHECK->value;
            $match->current_voter = CurrentVoter::TEAM1->value;
            $match->save();
            
            // Получаем капитанов команд
            $team1Captain = \App\Models\TeamMember::where('team_id', $teamId)
                ->where('role', 'captain')
                ->first();
                
            $team2Captain = \App\Models\TeamMember::where('team_id', $opponentTeam->id)
                ->where('role', 'captain')
                ->first();
            
            // Создаем записи о готовности
            if ($team1Captain) {
                DB::table('live_match_ready')->insert([
                    'match_id' => $match->id,
                    'player_id' => $team1Captain->player_id,
                    'is_ready' => false
                ]);
            }
            
            if ($team2Captain) {
                DB::table('live_match_ready')->insert([
                    'match_id' => $match->id,
                    'player_id' => $team2Captain->player_id,
                    'is_ready' => false
                ]);
            }
            
            DB::commit();
            
            // Перенаправляем на страницу поиска матча
            return redirect()->route('find.match', ['teamId' => $teamId]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при создании тестового матча: ' . $e->getMessage()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.modals.create-test-match-modal');
    }
}