<!DOCTYPE html>
<html
  lang="{{ str_replace('_', '-', app()->getLocale()) }}"
  class="dark-style layout-menu-fixed layout-compact"
  dir="ltr"
  data-theme="theme-default"
  data-assets-path="{{ asset('assets/') }}"
  data-template="vertical-menu-template-free"
  data-style="dark">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        @auth
            <meta name="user-id" content="{{ Auth::id() }}">
        @endauth
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ $title ?? 'Page Title' }}</title>
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/favicon/favicon.ico') }}" />

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap" rel="stylesheet" preload as="style" onload="this.onload=null;this.rel='stylesheet'">
        <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"></noscript>

        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/remixicon/remixicon.css') }}" />

        <!-- Core CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/core.css') }}" class="template-customizer-core-css" />
        <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}" />
        <!-- Дополнительные переопределения для темной темы -->
        <link rel="stylesheet" href="{{ asset('assets/css/dark-override.css') }}" />
        @stack('styles')
        
        <!-- Vite Assets -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body>        
        <main class="container">

            <nav class="navbar navbar-expand-lg mb-1 bg-body-tertiary">
                <div class="container-fluid">
                  <a class="navbar-brand" href="{{ route('home') }}" wire:navigate>RGTournament</a>
                  <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                  </button>
                  <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                      <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('team') ? 'active' : '' }}" {{ request()->routeIs('team') ? 'aria-current="page"' : '' }} href="{{ route('team') }}" wire:navigate>Leadboard</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('matches') ? 'active' : '' }}" {{ request()->routeIs('matches') ? 'aria-current="page"' : '' }} href="{{ route('matches') }}" wire:navigate>Matches</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('game-matches') ? 'active' : '' }}" {{ request()->routeIs('game-matches') ? 'aria-current="page"' : '' }} href="{{ route('game-matches') }}" wire:navigate>Game Matches</a>
                      </li>
                    </ul>
                    <ul class="d-flex navbar-nav">
                        @auth
                          <livewire:user-menu /> 
                        @else
                            <li class="nav-item"><a class="nav-link" href="{{ route('login') }}" wire:navigate>Войти</a></li>
                            <li class="nav-item"><a class="nav-link" href="{{ route('register') }}" wire:navigate>Регистрация</a></li>
                        @endauth
                    </ul>
                  </div>
                </div>
              </nav>

        
              {{ $slot }}

        </main>

        {{-- <div x-data="{
          message: '',
          type: '',
          show: false,
          init() {
              Livewire.on('showNotification', ({ message, type }) => {
                  this.message = message;
                  this.type = type;
                  this.show = true;
                  setTimeout(() => this.show = false, 5000);
              });
          }
      }" x-show="show" class="alert fade" :class="{ 'alert-success show': type === 'success', 'alert-danger show': type === 'error' }" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
          <span x-text="message"></span>
      </div> --}}

    <!-- Core JS -->
    <script src="{{ asset('assets/vendor/js/bootstrap.js') }}"></script>
    <script src="{{ asset('assets/vendor/js/menu.js') }}"></script>

    <!-- Main JS -->
    <script src="{{ asset('assets/js/main.js') }}"></script>

    @stack('scripts')
    @stack('carousel-scripts')

    <!-- Принудительное применение темной темы -->
    <script src="{{ asset('assets/js/force-dark-theme.js') }}"></script>
    
    <!-- Carousel JS -->
    <script src="{{ asset('assets/js/carousel.js') }}"></script>

    
    </body>
</html>

