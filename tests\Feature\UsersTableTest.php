<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class UsersTableTest extends TestCase
{
    /**
     * Тест для проверки работы с таблицей players.
     */
    public function test_users_table_works(): void
    {
        // Создаем таблицу players, если она не существует
        DB::statement('
            CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_nick TEXT,
                email TEXT UNIQUE,
                password TEXT,
                remember_token TEXT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP
            )
        ');
        
        // Вставляем данные
        DB::table('players')->insert([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Проверяем, что данные вставлены
        $this->assertDatabaseHas('players', [
            'client_nick' => 'Test User'
        ]);
    }
}
