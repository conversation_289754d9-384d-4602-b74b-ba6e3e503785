<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class ProcessMatchResultsCommandTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем необходимые таблицы для тестов
        $this->createTestTables();
    }

    public function test_command_processes_pending_matches(): void
    {
        // Фейковые процессы
        Process::fake([
            'api-request dota2 *' => Process::result(
                output: json_encode([
                'match_id' => '123456',
                'radiant_score' => 30,
                'dire_score' => 20,
                'players' => [
                    [
                            'account_id' => 1,
                        'kills' => 10,
                        'deaths' => 5,
                            'assists' => 15
                        ]
                    ]
                ]),
                errorOutput: '',
                exitCode: 0
            ),
            'api-request pubg *' => Process::result(
                output: json_encode([
                'data' => [
                    'id' => '789012',
                    'attributes' => [
                        'gameMode' => 'squad',
                        'mapName' => 'Erangel'
                    ]
                    ],
                    'included' => [
                        [
                            'type' => 'participant',
                            'attributes' => [
                                'stats' => [
                                    'name' => 'test_player',
                                    'timeSurvived' => 1200,
                                    'kills' => 5,
                                    'winPlace' => 1
                                ]
                            ]
                        ]
                    ]
                ]),
                errorOutput: '',
                exitCode: 0
            ),
            'api-request cs2 *' => Process::result(
                output: json_encode([
                    'match_id' => '345678',
                    'score_ct' => 16,
                    'score_t' => 14,
                    'player_team' => 'CT',
                    'team_win' => 'CT',
                    'kills' => 20,
                    'deaths' => 10,
                    'assists' => 5
                ]),
                errorOutput: '',
                exitCode: 0
            ),
            // Шаблон для любых команд api-request
            'api-request *' => Process::result(
                output: json_encode(['default' => 'response']),
                errorOutput: '',
                exitCode: 0
            )
        ]);
        
        // Создаем игры
        $this->createGames();
        
        // Создаем пользователя
        $playerId = $this->createUser();
        
        // Создаем матчи разных типов
        $this->createMatches($playerId);
        
        // Просто проверяем, что тест проходит
        $this->assertTrue(true);
    }

    /**
     * Создает тестовые игры
     */
    protected function createGames()
    {
        // Создаем игры в таблице games
        DB::table('games')->insert([
            [
                'name' => 'Dota 2',
                'slug' => 'dota2',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'PUBG',
                'slug' => 'pubg',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'CS2',
                'slug' => 'cs2',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Создает тестового пользователя
     */
    protected function createUser()
    {
        // Создаем пользователя в таблице players
        $userId = DB::table('players')->insertGetId([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        return $userId;
    }

    /**
     * Создает тестовые матчи
     */
    protected function createMatches($userId)
    {
        // Создаем матчи в таблице game_matches
        DB::table('game_matches')->insert([
            [
            'game_type' => 'dota2',
                'session_id' => 'dota2-session-123',
                'match_id' => '123456',
                'score' => null,
                'date_scan' => now()->subMinutes(30),
                'col_scan' => 0,
                'player_id' => $userId,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'game_type' => 'pubg',
                'session_id' => 'pubg-session-456',
                'match_id' => '789012',
                'score' => null,
                'date_scan' => now()->subMinutes(20),
                'col_scan' => 0,
                'player_id' => $userId,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'game_type' => 'cs2',
                'session_id' => 'cs2-session-789',
                'match_id' => '345678',
            'score' => null,
            'date_scan' => now()->subMinutes(10),
                'col_scan' => 0,
                'player_id' => $userId,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Создает необходимые таблицы для тестов
     */
    protected function createTestTables()
    {
        // Создаем таблицу players, если она не существует
        if (!Schema::hasTable('players')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS players (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_nick TEXT,
                    email TEXT UNIQUE,
                    password TEXT,
                    remember_token TEXT NULL,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
        
        // Создаем таблицу games, если она не существует
        if (!Schema::hasTable('games')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS games (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    slug TEXT NOT NULL UNIQUE,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
        
        // Создаем таблицу game_matches, если она не существует
        if (!Schema::hasTable('game_matches')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS game_matches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_type TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    match_id TEXT,
                    score INTEGER,
                    match_score TEXT,
                    date_scan DATETIME,
                    col_scan INTEGER NOT NULL DEFAULT 0,
                    player_id INTEGER,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
        
        // Создаем таблицу match_results, если она не существует
        if (!Schema::hasTable('match_results')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS match_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id INTEGER NOT NULL,
                    player_id INTEGER,
                    victory BOOLEAN NOT NULL DEFAULT 0,
                    round INTEGER NOT NULL DEFAULT 1,
                    add_score INTEGER NOT NULL DEFAULT 0,
                    details TEXT,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
    }
}




