<?php

namespace Database\Seeders;

use App\Models\Game;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Создаем команды для каждой игры
        Game::all()->each(function ($game) {
            // Создаем 3-5 команд для каждой игры
            $teamsCount = rand(3, 5);
            
            for ($i = 0; $i < $teamsCount; $i++) {
                // Создаем капитана
                $captain = User::factory()->create();
                
                // Создаем команду
                $team = Team::factory()->create([
                    'name' => fake()->company() . ' ' . $game->name . ' Team',
                    'game_id' => $game->id,
                    'captain_id' => $captain->id,
                ]);
                
                // Добавляем капитана как члена команды
                $team->members()->create([
                    'user_id' => $captain->id,
                    'team_id' => $team->id,
                    'role' => 'captain',
                ]);
                
                // Добавляем остальных членов команды (до размера команды для данной игры)
                for ($j = 1; $j < $game->team_size; $j++) {
                    $member = User::factory()->create();
                    $team->members()->create([
                        'user_id' => $member->id,
                        'team_id' => $team->id,
                        'role' => 'member',
                    ]);
                }
            }
        });
    }
}