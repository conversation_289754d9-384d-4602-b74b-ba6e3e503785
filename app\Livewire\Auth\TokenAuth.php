<?php

namespace App\Livewire\Auth;

use App\Models\Game;
use Livewire\Component;
use Livewire\Attributes\Url;
use Livewire\Attributes\Title;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cookie;
use App\Services\TokenService;

#[Title('Авторизация по токену')]
class TokenAuth extends Component
{
    #[Url(as: 'auth_token', keep: true)]
    public string $token = '';

    #[Url(as: 'club', keep: true)]
    public int $clubId = 0;

    #[Url(keep: true)]
    public string $game = '';

    #[Url(keep: true)]
    public string $run = '';

    #[Url(keep: true)]
    public bool $active = false;

    public bool $isProcessing = false;
    public string $status = 'checking'; // checking, success, error

    protected TokenService $tokenService;

    public function boot(TokenService $tokenService)
    {
        $this->tokenService = $tokenService;
    }

    public function mount(): void
    {
        // Атрибут #[Url] автоматически заполняет свойства из строки запроса.

        // Всегда выходим из системы текущего пользователя при аутентификации по токену.
        if (Auth::check()) {
            Log::info('Выход текущего пользователя для аутентификации по токену.');
            Auth::logout();
            // Очищаем сессию, чтобы удалить все оставшиеся данные (например, гостевые).
            request()->session()->flush();
            // Пересоздаем CSRF-токен после очистки.
            request()->session()->regenerateToken();
        }

        Log::info('Компонент TokenAuth смонтирован', [
            'has_token' => !empty($this->token),
            'club_id' => $this->clubId,
            'token_preview' => $this->token ? substr($this->token, 0, 8) . '...' : null,
        ]);

        if (empty($this->token) || empty($this->clubId)) {
            $this->status = 'error';
            Log::warning('Токен или ID клуба отсутствуют в запросе.', [
                'token' => !empty($this->token),
                'clubId' => !empty($this->clubId),
            ]);
            return;
        }

        $this->authenticate();
    }

    public function authenticate(): ?RedirectResponse
    {
        $this->isProcessing = true;

        Log::info('Начало аутентификации по токену', [
            'token_preview' => substr($this->token, 0, 8) . '...',
            'club_id' => $this->clubId
        ]);

        try {
            $result = $this->tokenService->validateToken($this->token, $this->clubId);

            if ($result instanceof \App\Models\User) {
                $this->handleSuccessfulLogin($result);
                return null;
            }

            return $this->handleAuthenticationError($result);

        } catch (\Exception $e) {
            Log::error('Исключение при аутентификации по токену', [
                'error' => $e->getMessage(),
                'token_preview' => substr($this->token, 0, 8) . '...',
                'club_id' => $this->clubId,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->handleAuthenticationError('exception');
        } finally {
            $this->isProcessing = false;
        }
    }
    
    /**
     * Обрабатывает логику успешной аутентификации.
     */
    private function handleSuccessfulLogin(\App\Models\User $user): void
    {
        Auth::login($user);
        $this->updateSessionData();
        $this->status = 'success';

        Cookie::queue('auth_token', $this->token, 180); // 3 часа
        Cookie::queue('club_id', $this->clubId, 180);

        Log::info('Аутентификация по токену прошла успешно', [
            'user_id' => $user->id,
            'club_id' => $user->club_id
        ]);

        $this->dispatch('redirect-to-team');
    }

    /**
     * Обновляет данные сессии на основе параметров URL.
     */
    private function updateSessionData(): void
    {
        $game = Game::where('name', strtoupper($this->game))->first();
        $selectedGameId = $game ? $game->id : 1; // По умолчанию 1, если игра не найдена

        $sessionData = [
            'selectedGameId' => $selectedGameId,
            'activeInActiveGameId' => $selectedGameId,
            'activeGame' => $this->active,
        ];

        if (request()->has('active')) {
            $sessionData['show_active_match_view_until'] = 'show_active_match_view';
        }

        session($sessionData);
    }

    /**
     * Обрабатывает ошибки аутентификации.
     */
    private function handleAuthenticationError(string $errorType)
    {
        Log::warning('Ошибка аутентификации по токену', [
            'reason' => $errorType,
            'token_preview' => substr($this->token, 0, 8) . '...',
            'club_id' => $this->clubId
        ]);

        return $this->redirect(route('auth.token.error', [
            'errorType' => $errorType,
            'token' => $this->token,
            'clubId' => $this->clubId
        ]), navigate: true);
    }

    public function render()
    {
        return view('livewire.auth.token-auth');
    }

}
