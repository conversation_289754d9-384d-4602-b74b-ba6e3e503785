<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class LogoutHandler extends Component
{
    protected $listeners = ['logout' => 'logout'];

    public function logout()
    {
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();
        
        return redirect()->route('home');
    }

    public function render()
    {
        return <<<'HTML'
        <div></div>
        HTML;
    }
}