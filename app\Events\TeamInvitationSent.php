<?php

namespace App\Events;

use App\Models\TeamInvitation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel; // Добавлено
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TeamInvitationSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $invitation;

    /**
     * Create a new event instance.
     */
    public function __construct(TeamInvitation $invitation)
    {
        $this->invitation = $invitation;
        \Illuminate\Support\Facades\Log::info('TeamInvitationSent event created for player: ' . $invitation->player_id, [
            'invitation_id' => $invitation->id,
            'player_id' => $invitation->player_id,
            'team_id' => $invitation->team_id
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channelName = 'player.' . $this->invitation->player_id;
        \Illuminate\Support\Facades\Log::info('TeamInvitationSent broadcasting to channel: ' . $channelName, [
            'player_id' => $this->invitation->player_id,
            'invitation_id' => $this->invitation->id,
            'team_id' => $this->invitation->team_id
        ]);
        return [
            new PrivateChannel($channelName),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'team.invitation.sent';
    }
}