<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Team;

class TeamMemberKicked implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $teamId;
    public $memberId;
    public $team;

    public function __construct($teamId, $memberId)
    {
        $this->teamId = $teamId;
        $this->memberId = $memberId;
        $this->team = Team::find($teamId);
        
        \Illuminate\Support\Facades\Log::info('TeamMemberKicked event created', [
            'team_id' => $teamId,
            'member_id' => $memberId,
            'team_name' => $this->team ? $this->team->name : 'Unknown'
        ]);
    }

    public function broadcastOn()
    {
        \Illuminate\Support\Facades\Log::info('TeamMemberKicked broadcasting to channel: player.' . $this->memberId, [
            'member_id' => $this->memberId,
            'team_name' => $this->team ? $this->team->name : 'Unknown'
        ]);
        return new PrivateChannel('player.' . $this->memberId);
    }

    public function broadcastAs()
    {
        return 'team.member.kicked';
    }

    public function broadcastWith()
    {
        return [
            'teamId' => $this->teamId,
            'memberId' => $this->memberId,
            'teamName' => $this->team ? $this->team->name : 'Неизвестная команда',
            'gameId' => $this->team ? $this->team->game_id : null
        ];
    }
}







