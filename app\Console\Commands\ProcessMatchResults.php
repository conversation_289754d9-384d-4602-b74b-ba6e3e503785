<?php

namespace App\Console\Commands;

use App\Models\GameMatch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;

class ProcessMatchResults extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'matches:process {game_type?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending match results';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $gameType = $this->argument('game_type');
        
        if ($gameType) {
            $this->processGameType($gameType);
        } else {
            // Process matches for each game type
            $this->processGameType('dota2');
            $this->processGameType('pubg');
            $this->processGameType('cs2');
        }

        return Command::SUCCESS;
    }

    /**
     * Process matches for a specific game type
     *
     * @param string $gameType
     * @return void
     */
    protected function processGameType(string $gameType)
    {
        $this->info("Processing {$gameType} matches...");
        
        // Get pending matches for this game type
        $matches = GameMatch::where('game_type', $gameType)
            ->where('col_scan', 0)
            ->where('date_scan', '<', now())
            ->get();
            
        $this->info("Found {$matches->count()} pending {$gameType} matches.");
        
        foreach ($matches as $match) {
            $this->info("Processing match ID: {$match->match_id}");
            
            try {
                // Run API request process to get match data
                $result = Process::run("api-request {$gameType} {$match->match_id}");
                
                if ($result->successful()) {
                    $data = json_decode(trim($result->output()), true);
                    
                    if (!$data) {
                        $this->error("Invalid JSON response for match {$match->match_id}");
                        continue;
                    }
                    
                    // Process match data based on game type
                    switch ($gameType) {
                        case 'dota2':
                            $this->processDota2Match($match, $data);
                            break;
                        case 'pubg':
                            $this->processPubgMatch($match, $data);
                            break;
                        case 'cs2':
                            $this->processCs2Match($match, $data);
                            break;
                        default:
                            $this->error("Unknown game type: {$gameType}");
                    }
                    
                    // Mark match as processed
                    $match->col_scan = 1;
                    $match->save();
                    
                    $this->info("Successfully processed match {$match->match_id}");
                } else {
                    $this->error("Process failed for match {$match->match_id}: " . $result->errorOutput());
                }
            } catch (\Exception $e) {
                $this->error("Exception processing match {$match->match_id}: " . $e->getMessage());
                Log::error("Exception processing match {$match->match_id}: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Process Dota 2 match data
     *
     * @param GameMatch $match
     * @param array $data
     * @return void
     */
    protected function processDota2Match(GameMatch $match, array $data)
    {
        // Calculate score based on Dota 2 specific metrics
        $kills = $data['players'][0]['kills'] ?? 0;
        $deaths = $data['players'][0]['deaths'] ?? 0;
        $assists = $data['players'][0]['assists'] ?? 0;
        
        // Simple scoring formula for Dota 2
        $score = ($kills * 3) + ($assists * 1) - ($deaths * 1);
        $score = max(0, $score); // Ensure score is not negative
        
        // Determine if player's team won
        $radiantScore = $data['radiant_score'] ?? 0;
        $direScore = $data['dire_score'] ?? 0;
        $victory = $radiantScore > $direScore; // Simplified logic
        
        // Save match result
        $match->score = $score;
        $match->save();
        
        $match->matchResult()->create([
            'victory' => $victory,
            'add_score' => $score,
            'details' => json_encode([
                'kills' => $kills,
                'deaths' => $deaths,
                'assists' => $assists,
                'radiant_score' => $radiantScore,
                'dire_score' => $direScore
            ])
        ]);
    }
    
    /**
     * Process PUBG match data
     *
     * @param GameMatch $match
     * @param array $data
     * @return void
     */
    protected function processPubgMatch(GameMatch $match, array $data)
    {
        // Extract player data from PUBG response
        $playerData = null;
        if (isset($data['included']) && is_array($data['included'])) {
            foreach ($data['included'] as $included) {
                if ($included['type'] === 'participant' && isset($included['attributes']['stats'])) {
                    $playerData = $included['attributes']['stats'];
                    break;
                }
            }
        }
        
        if (!$playerData) {
            $this->error("No player data found in PUBG match response");
            return;
        }
        
        // Calculate score based on PUBG specific metrics
        $kills = $playerData['kills'] ?? 0;
        $timeSurvived = $playerData['timeSurvived'] ?? 0;
        $winPlace = $playerData['winPlace'] ?? 0;
        
        // Simple scoring formula for PUBG
        $score = ($kills * 5) + ($timeSurvived / 60) + (100 - $winPlace * 5);
        $score = max(0, (int)$score); // Ensure score is not negative and convert to integer
        
        // Determine if player won
        $victory = ($winPlace === 1);
        
        // Save match result
        $match->score = $score;
        $match->save();
        
        $match->matchResult()->create([
            'victory' => $victory,
            'add_score' => $score,
            'details' => json_encode([
                'kills' => $kills,
                'time_survived' => $timeSurvived,
                'win_place' => $winPlace,
                'game_mode' => $data['data']['attributes']['gameMode'] ?? 'unknown',
                'map_name' => $data['data']['attributes']['mapName'] ?? 'unknown'
            ])
        ]);
    }
    
    /**
     * Process CS2 match data
     *
     * @param GameMatch $match
     * @param array $data
     * @return void
     */
    protected function processCs2Match(GameMatch $match, array $data)
    {
        // Extract data from CS2 response
        $kills = $data['kills'] ?? 0;
        $deaths = $data['deaths'] ?? 0;
        $assists = $data['assists'] ?? 0;
        $playerTeam = $data['player_team'] ?? '';
        $teamWin = $data['team_win'] ?? '';
        
        // Calculate score based on CS2 specific metrics
        $score = ($kills * 2) + ($assists * 1) - ($deaths * 0.5);
        $score = max(0, (int)$score); // Ensure score is not negative and convert to integer
        
        // Determine if player's team won
        $victory = ($playerTeam === $teamWin);
        
        // Save match result
        $match->score = $score;
        $match->save();
        
        $match->matchResult()->create([
            'victory' => $victory,
            'add_score' => $score,
            'details' => json_encode([
                'kills' => $kills,
                'deaths' => $deaths,
                'assists' => $assists,
                'score_ct' => $data['score_ct'] ?? 0,
                'score_t' => $data['score_t'] ?? 0,
                'player_team' => $playerTeam,
                'team_win' => $teamWin
            ])
        ]);
    }
}



