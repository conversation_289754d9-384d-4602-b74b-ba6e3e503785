<?php

namespace App\Jobs;

use App\Models\Team;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ClearTeamCache implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userId;
    public $invitedUserId;
    public $teamId;
    public $gameId;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 30;

    /**
     * Create a new job instance.
     */
    public function __construct($userId, $invitedUserId, $teamId, $gameId)
    {
        $this->userId = $userId;
        $this->invitedUserId = $invitedUserId;
        $this->teamId = $teamId;
        $this->gameId = $gameId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Очищаем кэш команд для капитана
        Cache::forget("user_teams_{$this->userId}_{$this->gameId}");
        Cache::forget("user_teams_{$this->userId}_all_games");
        Cache::forget("user_teams_{$this->userId}");

        // Очищаем кэш для приглашенного игрока
        Cache::forget("user_teams_{$this->invitedUserId}_{$this->gameId}");
        Cache::forget("user_teams_{$this->invitedUserId}_all_games");
        Cache::forget("user_teams_{$this->invitedUserId}");

        // Очищаем кэш опций игроков
        Cache::forget('player_options_' . $this->userId);
        // Очищаем кэш полученных приглашений для приглашенного игрока
        Cache::forget("received_invitations_for_player_{$this->invitedUserId}");

        // Очищаем кэш полученных приглашений для приглашенного игрока
        Cache::forget("received_invitations_for_player_{$this->invitedUserId}");

        Log::info('ClearTeamCache: Очищен кэш', [
            'captain_id' => $this->userId,
            'invited_user_id' => $this->invitedUserId,
            'team_id' => $this->teamId,
            'game_id' => $this->gameId
        ]);
    }
}
