<?php

namespace App\Livewire;

use App\Models\Team;
use App\Models\LiveMatch;
use App\Models\LiveMatchReady;
use App\Models\Cs2Map;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use App\Services\MatchmakingService;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Events\MatchAccepted;
use Livewire\Attributes\On; 
use App\Events\MapBanned;

class MatchSearch extends Component
{
    /** @var int|null */
    public ?int $teamId = null;

    /** @var Team|null */
    public ?Team $team = null;

    /** @var string */
    public string $searchStatus = 'waiting'; // waiting, searching, waiting_for_retry, ready_check, map_voting, live

    public int $searchTime = 0;
    public $startTime = null;
    public $maxSearchTime = 60;
    public $foundMatch = null;
    public $eligibleOpponentCount = 0;
    public $opponents = [];
    public $bannedMaps = [];
    public $availableMaps = [];
    public $currentVotingTeam = null;
    public int $maxBans = 6;
    protected $listeners = ['echo:match.accepted,MatchAccepted' => 'handleMatchAccepted'];
    
    public bool $showAcceptModal = false;
    public $matchData = null;

    public $errorMessage = ''; // Новое свойство для сообщений об ошибках
    public $infoMessage = '';  // Новое свойство для информационных сообщений

    // Свойства для нового таймера
    public int $retryTimer = 0;
    public int $retryDelay = 5;

    public function mount($teamId, $searchStatus = 'waiting', $foundMatch = null)
    {
        $this->teamId = $teamId;
        $this->team = \App\Models\Team::find($teamId);
        $this->searchStatus = $searchStatus;
        $this->foundMatch = $foundMatch;
        $this->opponents = [];
        $this->bannedMaps = [];
        
        if ($this->team) {
            $this->loadAvailableMaps();
        }
    }

    public function loadComponent()
    {
        // Этот метод теперь пуст, так как основная логика в mount и других методах.
    }

    protected function loadAvailableMaps()
    {
        if (!$this->team || !$this->team->game_id) {
            $this->availableMaps = [];
            return;
        }

        switch ($this->team->game_id) {
            case 1: // CS2
                $maps = Cs2Map::where('is_active', true)->get();
                $this->availableMaps = $maps->map(function($map) {
                    return [
                        'id' => $map->id,
                        'name' => $map->name,
                        'imageUrl' => $map->image_url
                    ];
                })->toArray();
                break;
            case 2: // Dota 2
                $this->availableMaps = [
                    ['id' => 1, 'name' => 'Default Map', 'imageUrl' => null],
                ];
                break;
            case 3: // PUBG
                $this->availableMaps = [
                    ['id' => 1, 'name' => 'Erangel', 'imageUrl' => null],
                    ['id' => 2, 'name' => 'Miramar', 'imageUrl' => null],
                    ['id' => 3, 'name' => 'Sanhok', 'imageUrl' => null],
                    ['id' => 4, 'name' => 'Vikendi', 'imageUrl' => null],
                    ['id' => 5, 'name' => 'Karakin', 'imageUrl' => null],
                    ['id' => 6, 'name' => 'Paramo', 'imageUrl' => null],
                    ['id' => 7, 'name' => 'Haven', 'imageUrl' => null],
                ];
                break;
            default:
                $this->availableMaps = [];
        }
    }

    public function startSearch()
    {
        $teamMember = \App\Models\TeamMember::where('team_id', $this->teamId)
            ->where('player_id', Auth::id())
            ->where('role', 'captain')
            ->first();

        if (!$teamMember) {
            session()->flash('error', 'Только капитан команды может начать поиск матча.');
            return;
        }

        $this->searchStatus = 'searching';
        $this->searchTime = 0;
        $this->startTime = now();
        
        $this->team->is_searching = true;
        $this->team->save();

        $this->dispatch('startSearchTimer');
        session(['search_status' => 'searching']);
    }
    
    #[On('findMatchAsync')]
    public function findMatchAsync()
    {
        $this->findMatch(app(MatchmakingService::class));
    }
    
    public function updateSearchTime()
    {
        if ($this->searchStatus === 'searching') {
            $this->searchTime++;
            if ($this->searchTime % 5 === 0) {
                $this->findMatch(app(MatchmakingService::class));
            }
        } elseif ($this->searchStatus === 'waiting_for_retry') {
            $this->retryTimer++;
            if ($this->retryTimer >= $this->retryDelay) {
                $this->retryTimer = 0;
                $this->startSearch(); // Повторяем поиск автоматически
            }
        }
    }

    public function findMatch(MatchmakingService $matchmakingService)
    {
        try {
            Log::info('Начало поиска матча', ['team_id' => $this->teamId]);

            $this->eligibleOpponentCount = Team::where('game_id', $this->team->game_id)
                ->where('id', '!=', $this->team->id)
                ->where('is_searching', true)
                ->count();
            
            $match = $matchmakingService->findMatch($this->team);

            if ($match) {
                Log::info('Матч найден', ['match_id' => $match->id]);
                $this->foundMatch = $match;
                $this->searchStatus = 'ready_check';
                $this->retryTimer = 0;
                $this->errorMessage = '';
                $this->infoMessage = '';

                $opponentTeamId = ($match->team1_id == $this->teamId) ? $match->team2_id : $match->team1_id;
                $this->opponents = [
                    'team' => Team::find($opponentTeamId),
                ];
    
            } else {
                Log::info('Матч не найден, переключаемся на waiting_for_retry.');
                $this->searchStatus = 'waiting_for_retry';
                $this->dispatch('stopSearchTimer');
            }
        } catch (\Exception $e) {
            Log::error('Ошибка при поиске матча: ' . $e->getMessage(), ['exception' => $e, 'trace' => $e->getTraceAsString()]);
            $this->errorMessage = $e->getMessage();
            $this->searchStatus = 'error';
        }
    }

    public function cancelSearch()
    {
        $this->searchStatus = 'waiting';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];
        $this->retryTimer = 0;
        session()->forget('search_status');
        $this->dispatch('stopSearchTimer');

        if ($this->team) {
            $this->team->is_searching = false;
            $this->team->save();
        }
        $this->errorMessage = '';
        $this->infoMessage = '';
    }

    #[On('showMatchAcceptModal')]
    public function handleShowMatchAcceptModal($matchId)
    {
        Log::info('Received showMatchAcceptModal event in PHP', ['matchId' => $matchId]);

        if (!$matchId) {
            Log::error('matchId missing in showMatchAcceptModal event', ['matchId' => $matchId]);
            $this->errorMessage = 'Информация о матче не найдена.';
            return;
        }

        $this->foundMatch = LiveMatch::find($matchId);
        
        if (!$this->foundMatch) {
            Log::error('Match not found after receiving showMatchAcceptModal event', ['matchId' => $matchId]);
            $this->errorMessage = 'Информация о матче не найдена.';
            return;
        }

        $this->searchStatus = 'ready_check';
        
        $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId)
            ? $this->foundMatch->team2_id
            : $this->foundMatch->team1_id;
        $this->opponents = ['team' => Team::find($opponentTeamId)];
        
        $this->checkTeamsReadyStatus();
        
        if ($this->searchStatus === 'ready_check') {
            $this->showAcceptModal = true;
            $this->matchData = $matchId;
            $this->dispatch('stopSearchTimer'); // Останавливаем таймер при показе модального окна
        } else {
            $this->showAcceptModal = false;
        }
    }

    public function acceptMatch()
    {
        try {
            if (!$this->foundMatch && $this->matchData) {
                $this->foundMatch = LiveMatch::find($this->matchData);
                if (!$this->foundMatch) {
                    Log::error('Match not found using matchData');
                    $this->errorMessage = 'Матч не найден';
                    return;
                }
            } elseif (!$this->foundMatch) {
                Log::error('Match not found and matchData is missing');
                $this->errorMessage = 'Матч не найден';
                return;
            }
    
            $teamCaptain = \App\Models\TeamMember::where('team_id', $this->teamId)
                ->where('player_id', Auth::id())
                ->where('role', 'captain')
                ->first();
    
            $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId) 
                ? $this->foundMatch->team2_id 
                : $this->foundMatch->team1_id;
    
            $opponentCaptain = \App\Models\TeamMember::where('team_id', $opponentTeamId)
                ->where('role', 'captain')
                ->first();
    
            if (!$teamCaptain || !$opponentCaptain) {
                Log::error('Captains not found', ['team_id' => $this->teamId, 'opponent_team_id' => $opponentTeamId]);
                $this->errorMessage = 'Не найдены капитаны команд';
                return;
            }
    
            DB::table('live_match_ready')->updateOrInsert(
                [
                    'match_id' => $this->foundMatch->id,
                    'player_id' => $teamCaptain->player_id,
                ],
                [
                    'is_ready' => true,
                ]
            );
    
            event(new MatchAccepted(
                $this->foundMatch->id,
                $teamCaptain->player_id,
                $opponentCaptain->player_id
            ));
    
            $this->checkTeamsReadyStatus();
            
        } catch (\Exception $e) {
            Log::error('Error in acceptMatch: ' . $e->getMessage());
            $this->errorMessage = 'Ошибка при принятии матча';
        }
    }
    
    protected function checkTeamsReadyStatus()
    {
        $this->foundMatch->refresh();
                
        if ($this->foundMatch->areBothTeamsReady()) {
            if ($this->foundMatch->status !== MatchStatus::MAP_VOTING->value) {
                $this->foundMatch->status = MatchStatus::MAP_VOTING->value;
                $this->foundMatch->save();
            }
            
            $this->searchStatus = 'map_voting';
            $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
            $this->loadAvailableMaps();
            $this->showAcceptModal = false;
            $this->infoMessage = '';
            
        } else {
            $this->searchStatus = 'ready_check';
            $this->infoMessage = 'Ожидание готовности второй команды.';
        }
    }
    
    public function declineMatch(): void
    {
        if ($this->foundMatch) {
            $this->foundMatch->delete();
        }

        $this->searchStatus = 'waiting';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];
        $this->retryTimer = 0;
        session()->forget('search_status');
        $this->dispatch('stopSearchTimer');

        if ($this->team) {
            $this->team->is_searching = false;
            $this->team->save();
        }
        $this->errorMessage = '';
        $this->infoMessage = '';
    }

    public function findNewMatch()
    {
        $this->searchStatus = 'searching';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];

        $this->findMatch(app(MatchmakingService::class));
    }

    protected function refreshVotingTeam()
    {
        if ($this->foundMatch) {
            $this->foundMatch->refresh();
            $this->currentVotingTeam = Team::find($this->foundMatch->current_voting_team);
            
            Log::info('Обновлена голосующая команда', [
                'match_id' => $this->foundMatch->id,
                'current_voting_team' => $this->currentVotingTeam?->id,
                'team_name' => $this->currentVotingTeam?->name
            ]);
            
            $this->syncBannedMaps();
        }
    }

    public function syncBannedMaps()
    {
        if ($this->foundMatch) {
            $this->bannedMaps = $this->foundMatch->bannedMaps->pluck('cs2_map_id')->toArray();
        } else {
            $this->bannedMaps = [];
        }
    }

    public function render()
    {
        if (!is_array($this->availableMaps)) {
            $this->availableMaps = [];
        }
        
        if (!is_array($this->bannedMaps)) {
            $this->bannedMaps = [];
        }
        
        if (!is_array($this->opponents)) {
            $this->opponents = [];
        }
        
        return view('livewire.match-search');
    }    
}