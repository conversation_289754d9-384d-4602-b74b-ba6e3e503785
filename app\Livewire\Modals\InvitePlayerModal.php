<?php

namespace App\Livewire\Modals;

use Livewire\Component;
use App\Models\User;
use App\Models\TeamInvitation;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\Interfaces\InvitationManagerInterface;

class InvitePlayerModal extends Component
{
    use WithPagination;

    protected InvitationManagerInterface $invitationService;

    public function boot(InvitationManagerInterface $invitationService)
    {
        $this->invitationService = $invitationService;
    }

    public $teamIdForInvite = null;
    public $playerName = null; // ID выбранного игрока
    public $search = ''; // Поисковый запрос
    public $selectedPlayerName = 'Выберите игрока'; // Отображаемое имя выбранного игрока
    
    // Свойство для управления видимостью выпадающего списка
    public $showDropdown = false;
    
    protected $paginationTheme = 'bootstrap';

    #[On('open-invite-player-modal')]
    public function openModal($data)
    {
        $this->teamIdForInvite = $data['teamId'];
        $this->reset(['playerName', 'search', 'selectedPlayerName']);
        $this->resetPage();
    }
    
    // Этот метод будет вызван Livewire автоматически при изменении $search
    public function updatedSearch()
    {
        $this->resetPage();
        $this->showDropdown = true; // Открываем список при поиске
    }
    
    // Динамическое свойство для получения игроков
    public function getPlayerOptionsProperty()
    {
        if (strlen($this->search) < 2) {
            return collect([]);
        }

        return User::where('id', '!=', Auth::id())
            ->whereNotNull('client_nick')
            ->where('client_nick', '!=', '')
            ->where('client_nick', 'like', '%' . $this->search . '%')
            ->select('id', 'client_nick', 'avatar')
            ->orderBy('client_nick')
            ->paginate(15);
    }
    
    // Метод для выбора игрока
    public function selectPlayer($id)
    {
        $this->playerName = $id;
        $player = User::find($id);
        $this->selectedPlayerName = $player->client_nick;
        $this->search = '';
        $this->resetPage();
        $this->showDropdown = false; // Закрываем список после выбора
    }
    
    public function nextPage()
    {
        $this->gotoPage($this->page + 1);
    }
    
    public function sendInvitation()
    {
        // ... твой код без изменений
        Log::info('InvitePlayerModal: Начало отправки приглашения');
        $startTime = microtime(true);

        Log::info('InvitePlayerModal: Данные для отправки приглашения', [
            'player_name' => $this->playerName,
            'team_id' => $this->teamIdForInvite
        ]);

        $this->validate([
            'playerName' => 'required',
        ], [
            'playerName.required' => 'Выберите игрока'
        ]);
        $validationTime = microtime(true) - $startTime;
        Log::info('InvitePlayerModal: Время валидации: '.$validationTime.' сек.');
        $startTime = microtime(true);

        $user = Auth::user();

        $isCaptainTime = microtime(true) - $startTime;
        Log::info('InvitePlayerModal: Время проверки капитана: '.$isCaptainTime.' сек.');
        $startTime = microtime(true);

        if (!app(\App\Services\Interfaces\TeamManagerInterface::class)->isUserCaptainOfTeam($user->id, $this->teamIdForInvite)) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Только капитан может приглашать игроков']);
            return;
        }

        $invitedUser = User::find($this->playerName);

        if (!$invitedUser) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Пользователь не найден']);
            return;
        }

        $findUserTime = microtime(true) - $startTime;
        Log::info('InvitePlayerModal: Время поиска пользователя: '.$findUserTime.' сек.');
        $startTime = microtime(true);

        $existingInvitation = TeamInvitation::where('team_id', $this->teamIdForInvite)
            ->where('player_id', $invitedUser->id)
            ->where('status', 'pending')
            ->first();

        if ($existingInvitation) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Приглашение уже отправлено этому игроку']);
            return;
        }

        $checkInvitationTime = microtime(true) - $startTime;
        Log::info('InvitePlayerModal: Время проверки приглашения: '.$checkInvitationTime.' сек.');
        $startTime = microtime(true);

        try {
            DB::beginTransaction();

            $invitationCreateTime = microtime(true) - $startTime;
            Log::info('InvitePlayerModal: Время до начала создания приглашения: '.$invitationCreateTime.' сек.');
            $startTime = microtime(true);

            $createdInvitation = $this->invitationService->sendInvitation(
                $this->teamIdForInvite,
                $invitedUser->id,
                $user->id
            );

            $invitationCreatedTime = microtime(true) - $startTime;
            Log::info('InvitePlayerModal: Время создания приглашения: '.$invitationCreatedTime.' сек.');
            $startTime = microtime(true);

            $getInvitationTime = microtime(true) - $startTime;
            Log::info('InvitePlayerModal: Время получения приглашения: '.$getInvitationTime.' сек.');
            $startTime = microtime(true);

            DB::commit();

            $commitTime = microtime(true) - $startTime;
            Log::info('InvitePlayerModal: Время коммита транзакции: '.$commitTime.' сек.');
            $startTime = microtime(true);

            $team = \App\Models\Team::find($this->teamIdForInvite);
            if ($team) {
                dispatch(new \App\Jobs\ClearTeamCache(
                    $user->id,
                    $invitedUser->id,
                    $team->id,
                    $team->game_id
                ));

                Log::info('InvitePlayerModal: Очистка кэша запущена асинхронно', [
                    'captain_id' => $user->id,
                    'invited_user_id' => $invitedUser->id,
                    'team_id' => $team->id,
                    'game_id' => $team->game_id
                ]);
            }

            if (Auth::check() && $createdInvitation && $team) {
                Log::info('InvitePlayerModal: Отправляем Pusher событие', [
                    'invitation_id' => $createdInvitation->id,
                    'player_id' => $invitedUser->id,
                    'team_name' => $team->name
                ]);

                try {
                    event(new \App\Events\TeamInvitationSent($createdInvitation));
                    Log::info('InvitePlayerModal: Pusher событие отправлено успешно', [
                        'team_id' => $createdInvitation->team_id,
                        'player_id' => $invitedUser->id
                    ]);
                    Log::info('InvitePlayerModal: Отправлено событие TeamInvitationSent', [
                        'invitation_id' => $createdInvitation->id,
                        'team_id' => $createdInvitation->team_id,
                        'player_id' => $invitedUser->id,
                        'channel' => "player.{$invitedUser->id}"
                    ]);
                } catch (\Exception $e) {
                    Log::error('InvitePlayerModal: Ошибка отправки Pusher события: ' . $e->getMessage());
                }
            } else {
                Log::error('InvitePlayerModal: Не удалось отправить Pusher событие', [
                    'invitation_found' => $createdInvitation ? 'yes' : 'no',
                    'team_found' => $team ? 'yes' : 'no'
                ]);
            }

            $pusherEventTime = microtime(true) - $startTime;
            Log::info('InvitePlayerModal: Время отправки Pusher события: '.$pusherEventTime.' сек.');
            $startTime = microtime(true);

            $this->reset(['playerName', 'search', 'selectedPlayerName']);
            $this->dispatch('closeInviteModal');
            $this->dispatch('showNotification', ['type' => 'success', 'message' => 'Приглашение успешно отправлено']);

            if ($team) {
                Log::info('InvitePlayerModal: Отправляем локальные события обновления', [
                    'team_id' => $team->id,
                    'game_id' => $team->game_id
                ]);
                $this->dispatch('invitationSent', $team->game_id);
            }
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при создании приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', ['type' => 'error', 'message' => $e->getMessage()]);
        }

        $totalTime = microtime(true) - $startTime;
        Log::info('InvitePlayerModal: Общее время выполнения sendInvitation: '.$totalTime.' сек.');
    }
    
    public function render()
    {
        return view('livewire.modals.invite-player-modal');
    }
}