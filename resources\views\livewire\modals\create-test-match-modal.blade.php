<div x-data="{ 
        open: false,
        teamId: null,
        init() {
            window.addEventListener('open-create-test-match-modal', (e) => {
                this.teamId = e.detail.teamId;
                this.open = true;
                document.body.classList.add('modal-open');
            });
            
            this.$watch('open', value => {
                if (!value) {
                    document.body.classList.remove('modal-open');
                }
            });
        }
    }"
    @keydown.escape.window="open = false"
    class="modal fade"
    :class="{ 'show': open }"
    style="display: none;"
    :style="open ? 'display: block;' : 'display: none;'"
    tabindex="-1"
    aria-hidden="true"
    wire:ignore.self>
    
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Создать тестовый матч</h5>
                <button type="button" class="btn-close" @click="open = false"></button>
            </div>
            <div class="modal-body">
                <p>Вы собираетесь создать тестовый матч для отладки. Это действие создаст тестовую команду-соперника (если необходимо) и сразу перейдет к этапу принятия матча.</p>
                <p class="text-warning">Внимание: Это функция только для тестирования!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                <button type="button" class="btn btn-primary" @click="$wire.createTestMatch(teamId); open = false;">Создать тестовый матч</button>
            </div>
        </div>
    </div>
    
    <div class="modal-backdrop fade show" x-show="open"></div>
</div>