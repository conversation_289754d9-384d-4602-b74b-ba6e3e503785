﻿DELIMITER $$

DROP PROCEDURE IF EXISTS `upgrade_games_table`$$

CREATE PROCEDURE `upgrade_games_table`()
BEGIN
  -- Добавление поля image, если оно отсутствует
  IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'rgtournament' 
      AND TABLE_NAME = 'games' 
      AND COLUMN_NAME = 'image'
  ) THEN
    ALTER TABLE rgtournament.games 
    ADD COLUMN image VARCHAR(255) NOT NULL DEFAULT '' AFTER name;
  END IF;

  -- Изменение name на varchar(255), если его длина меньше
  IF EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'rgtournament' 
      AND TABLE_NAME = 'games' 
      AND COLUMN_NAME = 'name' 
      AND CHARACTER_MAXIMUM_LENGTH < 255
  ) THEN
    ALTER TABLE rgtournament.games 
    MODIFY COLUMN name VARCHAR(255) NOT NULL;
  END IF;

  -- Изменение team_size на UNSIGNED SMALLINT, если это необходимо
  IF EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'rgtournament' 
      AND TABLE_NAME = 'games' 
      AND COLUMN_NAME = 'team_size' 
      AND (DATA_TYPE != 'smallint' OR COLUMN_TYPE NOT LIKE '%unsigned%')
  ) THEN
    ALTER TABLE rgtournament.games 
    MODIFY COLUMN team_size SMALLINT UNSIGNED NOT NULL;
  END IF;

  -- Добавление created_at, если он отсутствует
  IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'rgtournament' 
      AND TABLE_NAME = 'games' 
      AND COLUMN_NAME = 'created_at'
  ) THEN
    ALTER TABLE rgtournament.games 
    ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
  END IF;

  -- Добавление updated_at, если он отсутствует
  IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'rgtournament' 
      AND TABLE_NAME = 'games' 
      AND COLUMN_NAME = 'updated_at'
  ) THEN
    ALTER TABLE rgtournament.games 
    ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
  END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL upgrade_games_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `upgrade_games_table`;
