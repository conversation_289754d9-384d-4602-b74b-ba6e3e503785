<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ConvertMysqlDumpToSqlite extends Command
{
    /**
     * Имя и сигнатура консольной команды.
     */
    protected $signature = 'db:convert-dump {--output=database/sqlite_schema.sql : Путь к выходному файлу}';

    /**
     * Описание консольной команды.
     */
    protected $description = 'Конвертирует MySQL дамп в SQLite-совместимый формат';

    /**
     * Выполнение консольной команды.
     */
    public function handle(): int
    {
        $inputFile = base_path('rgtournament_copy_dump.sql');
        $outputFile = base_path($this->option('output'));
        
        if (!File::exists($inputFile)) {
            $this->error("Файл дампа не найден: $inputFile");
            return 1;
        }
        
        $this->info("Начинаем конвертацию MySQL дампа в SQLite формат...");
        $this->info("Исходный файл: $inputFile");
        $this->info("Выходной файл: $outputFile");
        
        // Создаем директорию для выходного файла, если она не существует
        $outputDir = dirname($outputFile);
        if (!File::exists($outputDir)) {
            File::makeDirectory($outputDir, 0755, true);
        }
        
        // Чтение MySQL дампа
        $mysqlDump = File::get($inputFile);
        
        // Конвертация в SQLite формат
        $sqliteSchema = $this->convertToSqlite($mysqlDump);
        
        // Сохранение результата
        File::put($outputFile, $sqliteSchema);
        
        $this->info("Конвертация завершена успешно!");
        return 0;
    }
    
    /**
     * Конвертирует MySQL дамп в SQLite-совместимый формат.
     */
    protected function convertToSqlite(string $mysqlDump): string
    {
        $lines = explode("\n", $mysqlDump);
        $sqliteLines = [];
        $inCreateTable = false;
        $currentTable = '';
        $createTableStatement = '';
        
        // Добавляем начальные настройки для SQLite
        $sqliteLines[] = "PRAGMA foreign_keys = OFF;";
        $sqliteLines[] = "BEGIN TRANSACTION;";
        
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            
            // Пропускаем комментарии и MySQL-специфичные команды
            if (empty($trimmedLine) || 
                strpos($trimmedLine, '--') === 0 || 
                strpos($trimmedLine, '/*!') === 0 || 
                strpos($trimmedLine, 'SET ') === 0 || 
                strpos($trimmedLine, 'DROP DATABASE') === 0 || 
                strpos($trimmedLine, 'CREATE DATABASE') === 0 || 
                strpos($trimmedLine, 'USE ') === 0) {
                continue;
            }
            
            // Обработка CREATE TABLE
            if (strpos($trimmedLine, 'CREATE TABLE') === 0) {
                $inCreateTable = true;
                $createTableStatement = '';
                
                // Извлекаем имя таблицы
                preg_match('/CREATE TABLE\s+`?([a-zA-Z0-9_]+)`?/i', $trimmedLine, $matches);
                $currentTable = $matches[1] ?? '';
                
                // Начинаем новый CREATE TABLE
                $createTableStatement .= "CREATE TABLE IF NOT EXISTS $currentTable (\n";
                continue;
            }
            
            // Внутри CREATE TABLE
            if ($inCreateTable) {
                // Конец CREATE TABLE
                if (strpos($trimmedLine, ');') === 0 || strpos($trimmedLine, ')') === 0) {
                    $inCreateTable = false;
                    $createTableStatement .= ");";
                    $sqliteLines[] = $this->convertCreateTableToSqlite($createTableStatement);
                    continue;
                }
                
                // Пропускаем ENGINE, CHARACTER SET и другие MySQL-специфичные параметры
                if (strpos($trimmedLine, 'ENGINE =') === 0 || 
                    strpos($trimmedLine, 'AUTO_INCREMENT =') === 0 || 
                    strpos($trimmedLine, 'DEFAULT CHARSET =') === 0 || 
                    strpos($trimmedLine, 'COLLATE =') === 0 || 
                    strpos($trimmedLine, 'ROW_FORMAT =') === 0 ||
                    strpos($trimmedLine, 'CHARACTER SET') !== false ||
                    strpos($trimmedLine, 'COLLATE') !== false) {
                    continue;
                }
                
                // Добавляем строку в CREATE TABLE
                $createTableStatement .= $this->convertColumnDefinition($trimmedLine) . "\n";
                continue;
            }
            
            // Обработка ALTER TABLE для индексов и внешних ключей
            if (strpos($trimmedLine, 'ALTER TABLE') === 0) {
                if (strpos($trimmedLine, 'ADD INDEX') !== false || 
                    strpos($trimmedLine, 'ADD UNIQUE INDEX') !== false || 
                    strpos($trimmedLine, 'ADD PRIMARY KEY') !== false) {
                    $sqliteLines[] = $this->convertIndexToSqlite($trimmedLine);
                } else if (strpos($trimmedLine, 'ADD CONSTRAINT') !== false && 
                           strpos($trimmedLine, 'FOREIGN KEY') !== false) {
                    $sqliteLines[] = $this->convertForeignKeyToSqlite($trimmedLine);
                }
                continue;
            }
            
            // Обработка INSERT INTO
            if (strpos($trimmedLine, 'INSERT INTO') === 0) {
                // Преобразуем INSERT INTO для SQLite
                $sqliteLines[] = $this->convertInsertToSqlite($trimmedLine);
                continue;
            }
        }
        
        // Добавляем завершающие команды
        $sqliteLines[] = "COMMIT;";
        $sqliteLines[] = "PRAGMA foreign_keys = ON;";
        
        return implode("\n", $sqliteLines);
    }
    
    /**
     * Конвертирует определение столбца из MySQL в SQLite.
     */
    protected function convertColumnDefinition(string $columnDef): string
    {
        // Заменяем типы данных
        $columnDef = preg_replace('/int\(\d+\)(\s+unsigned)?/i', 'INTEGER', $columnDef);
        $columnDef = preg_replace('/varchar\(\d+\)/i', 'TEXT', $columnDef);
        $columnDef = preg_replace('/text/i', 'TEXT', $columnDef);
        $columnDef = preg_replace('/datetime/i', 'DATETIME', $columnDef);
        $columnDef = preg_replace('/timestamp/i', 'TIMESTAMP', $columnDef);
        $columnDef = preg_replace('/double/i', 'REAL', $columnDef);
        $columnDef = preg_replace('/float/i', 'REAL', $columnDef);
        $columnDef = preg_replace('/decimal\(\d+,\d+\)/i', 'REAL', $columnDef);
        $columnDef = preg_replace('/enum\([^)]+\)/i', 'TEXT', $columnDef);
        
        // Заменяем AUTO_INCREMENT на AUTOINCREMENT
        $columnDef = str_replace('AUTO_INCREMENT', 'AUTOINCREMENT', $columnDef);
        
        // Удаляем MySQL-специфичные атрибуты
        $columnDef = preg_replace('/CHARACTER SET [a-zA-Z0-9_]+/i', '', $columnDef);
        $columnDef = preg_replace('/COLLATE [a-zA-Z0-9_]+/i', '', $columnDef);
        
        // Удаляем запятую в конце, если это последний столбец
        if (substr(trim($columnDef), -1) === ',') {
            return trim($columnDef);
        }
        
        return trim($columnDef) . ',';
    }
    
    /**
     * Конвертирует CREATE TABLE из MySQL в SQLite.
     */
    protected function convertCreateTableToSqlite(string $createTable): string
    {
        // Удаляем MySQL-специфичные атрибуты
        $createTable = preg_replace('/ENGINE\s*=\s*[a-zA-Z0-9_]+/i', '', $createTable);
        $createTable = preg_replace('/AUTO_INCREMENT\s*=\s*\d+/i', '', $createTable);
        $createTable = preg_replace('/DEFAULT CHARSET\s*=\s*[a-zA-Z0-9_]+/i', '', $createTable);
        $createTable = preg_replace('/COLLATE\s*=\s*[a-zA-Z0-9_]+/i', '', $createTable);
        $createTable = preg_replace('/ROW_FORMAT\s*=\s*[a-zA-Z0-9_]+/i', '', $createTable);
        $createTable = preg_replace('/CHARACTER SET\s+[a-zA-Z0-9_]+/i', '', $createTable);
        $createTable = preg_replace('/COLLATE\s+[a-zA-Z0-9_]+/i', '', $createTable);
        
        return $createTable;
    }
    
    /**
     * Конвертирует индекс из MySQL в SQLite.
     */
    protected function convertIndexToSqlite(string $alterTable): string
    {
        // Извлекаем имя таблицы
        preg_match('/ALTER TABLE\s+`?([a-zA-Z0-9_]+)`?/i', $alterTable, $tableMatches);
        $tableName = $tableMatches[1] ?? '';
        
        // Извлекаем имя индекса и столбцы
        if (strpos($alterTable, 'ADD PRIMARY KEY') !== false) {
            preg_match('/ADD PRIMARY KEY\s+\(([^)]+)\)/i', $alterTable, $indexMatches);
            $indexType = 'PRIMARY KEY';
            $indexName = 'pk_' . $tableName;
        } elseif (strpos($alterTable, 'ADD UNIQUE INDEX') !== false) {
            preg_match('/ADD UNIQUE INDEX\s+`?([a-zA-Z0-9_]+)`?\s+\(([^)]+)\)/i', $alterTable, $indexMatches);
            $indexType = 'UNIQUE';
            $indexName = $indexMatches[1] ?? ('idx_' . $tableName . '_unique');
            $columns = $indexMatches[2] ?? '';
        } else {
            preg_match('/ADD INDEX\s+`?([a-zA-Z0-9_]+)`?\s+\(([^)]+)\)/i', $alterTable, $indexMatches);
            $indexType = 'INDEX';
            $indexName = $indexMatches[1] ?? ('idx_' . $tableName);
            $columns = $indexMatches[2] ?? '';
        }
        
        $columns = $indexMatches[2] ?? $indexMatches[1] ?? '';
        $columns = str_replace('`', '', $columns);
        
        // Создаем SQLite индекс
        if ($indexType === 'PRIMARY KEY') {
            // Для PRIMARY KEY нужно модифицировать CREATE TABLE, но мы уже обработали это
            return "-- PRIMARY KEY для $tableName($columns) уже должен быть в CREATE TABLE";
        } else {
            return "CREATE $indexType IF NOT EXISTS $indexName ON $tableName ($columns);";
        }
    }
    
    /**
     * Конвертирует внешний ключ из MySQL в SQLite.
     */
    protected function convertForeignKeyToSqlite(string $alterTable): string
    {
        // SQLite не поддерживает ALTER TABLE для добавления внешних ключей после создания таблицы
        // Мы должны создать новую таблицу с внешними ключами и скопировать данные
        // Для простоты, просто добавим комментарий
        return "-- Внешний ключ: $alterTable";
    }
    
    /**
     * Конвертирует INSERT INTO из MySQL в SQLite.
     */
    protected function convertInsertToSqlite(string $insert): string
    {
        // SQLite в целом совместим с MySQL INSERT INTO, но может потребоваться некоторая корректировка
        // Например, замена NULL значений и т.д.
        return $insert;
    }
}
