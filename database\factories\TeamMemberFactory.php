<?php

namespace Database\Factories;

use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TeamMember>
 */
class TeamMemberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'team_id' => Team::factory(),
            'role' => fake()->randomElement(['captain', 'member']),
        ];
    }

    /**
     * Indicate that the team member is a captain.
     */
    public function captain(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'captain',
        ]);
    }

    /**
     * Indicate that the team member is a regular member.
     */
    public function member(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'member',
        ]);
    }
}