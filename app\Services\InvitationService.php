<?php

namespace App\Services;

use App\Services\Interfaces\InvitationManagerInterface;
use App\Services\Interfaces\TeamManagerInterface; // Добавляем
use App\Models\TeamInvitation;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class InvitationService implements InvitationManagerInterface
{
    protected TeamManagerInterface $teamService;

    public function __construct(TeamManagerInterface $teamService)
    {
        $this->teamService = $teamService;
    }

    /**
     * Отправляет приглашение игроку в команду.
     *
     * @param int $teamId ID команды
     * @param int $playerId ID игрока, которого приглашают
     * @param int $inviterId ID пользователя, который отправляет приглашение (капитан)
     * @return TeamInvitation
     * @throws \Exception
     */
    public function sendInvitation(int $teamId, int $playerId, int $inviterId): TeamInvitation
    {
        // Проверка на капитана
        if (!$this->teamService->isUserCaptainOfTeam($inviterId, $teamId)) {
            throw new \Exception('Только капитан может приглашать игроков.');
        }

        $team = Team::find($teamId);
        if (!$team) {
            throw new \Exception('Команда не найдена.');
        }

        // Проверка, состоит ли игрок уже в этой команде
        // Проверка, не является ли приглашаемый игрок капитаном команды
        if ($playerId === $team->captain_id) {
            throw new \Exception('Игрок уже является капитаном этой команды.');
        }

        // Проверка, состоит ли игрок уже в этой команде (как обычный член)
        if (TeamMember::where('team_id', $teamId)->where('player_id', $playerId)->exists()) {
            throw new \Exception('Игрок уже состоит в этой команде.');
        }

        // Проверка, есть ли уже ожидающее приглашение для этого игрока в эту команду
        if (TeamInvitation::where('team_id', $teamId)->where('player_id', $playerId)->where('status', 'pending')->exists()) {
            throw new \Exception('Приглашение этому игроку уже отправлено и ожидает ответа.');
        }

        // Проверка, не состоит ли игрок уже в другой команде для этой игры
        if ($this->teamService->hasUserTeamForGame($playerId, $team->game_id)) {
            throw new \Exception('Игрок уже состоит в команде для этой игры.');
        }

        // Проверка, не полна ли команда
        if ($this->teamService->isTeamFull($teamId)) {
            throw new \Exception('Команда уже полная.');
        }

        // Удаляем старые отклоненные приглашения для избежания конфликта уникального ключа
        TeamInvitation::where('team_id', $teamId)
            ->where('player_id', $playerId)
            ->where('status', 'rejected')
            ->delete();

        $invitation = TeamInvitation::create([
            'team_id' => $teamId,
            'player_id' => $playerId,
            'invited_by' => $inviterId,
            'status' => 'pending'
        ]);

        // Событие TeamInvitationSent теперь отправляется из Livewire компонента InvitePlayerModal
        // DB::afterCommit(function () use ($invitation) {
        //     event(new \App\Events\TeamInvitationSent($invitation->team_id, $invitation->player_id));
        // });

        return $invitation;
    }

    /**
     * Отменяет отправленное приглашение.
     *
     * @param int $invitationId ID приглашения
     * @param int $captainId ID капитана, который отменяет приглашение
     * @return bool
     */
    public function cancelInvitation(int $invitationId, int $captainId): bool
    {
        $invitation = TeamInvitation::find($invitationId);

        if (!$invitation) {
            throw new \Exception('Приглашение не найдено.');
        }

        if (!$this->teamService->isUserCaptainOfTeam($captainId, $invitation->team_id)) {
            throw new \Exception('Только капитан может отменять приглашения.');
        }

        $teamId = $invitation->team_id;
        $playerId = $invitation->player_id;

        $result = $invitation->delete();

        if ($result) {
            // Очищаем кэш команд для капитана
            $team = Team::find($teamId);
            if ($team) {
                Cache::forget("user_teams_{$captainId}_{$team->game_id}");
                Cache::forget("user_teams_{$captainId}_all_games");

                // Очищаем кэш для приглашенного игрока
                Cache::forget("user_teams_{$playerId}_{$team->game_id}");
                Cache::forget("user_teams_{$playerId}_all_games");
            }
            // Отправляем событие об отмене приглашения
            event(new \App\Events\TeamInvitationCancelled($invitationId, $playerId));
        }

        return $result;
    }

    /**
     * Принимает приглашение в команду.
     *
     * @param int $invitationId ID приглашения
     * @param int $playerId ID игрока, который принимает приглашение
     * @return bool
     */
    public function acceptInvitation(int $invitationId, int $playerId): bool
    {
        try {
            DB::beginTransaction();

            $invitation = TeamInvitation::find($invitationId);

            if (!$invitation) {
                throw new \Exception('Приглашение не найдено.');
            }

            if ($invitation->player_id !== $playerId) {
                throw new \Exception('У вас нет прав на это действие.');
            }

            $team = Team::find($invitation->team_id);
            if (!$team) {
                throw new \Exception('Команда не найдена.');
            }

            $teamId = $team->id;
            $captainId = $team->captain_id;

            // Проверяем, не состоит ли игрок уже в команде для этой игры
            if ($this->teamService->hasUserTeamForGame($playerId, $team->game_id)) {
                throw new \Exception('Вы уже состоите в команде для этой игры.');
            }

            // Проверяем, не полная ли команда
            if ($this->teamService->isTeamFull($team->id)) {
                throw new \Exception('Команда уже заполнена.');
            }

            // Проверяем, не является ли игрок капитаном команды
            if ($invitation->player_id === $team->captain_id) {
                throw new \Exception('Капитан не может быть добавлен как обычный член команды.');
            }

            TeamMember::create([
                'team_id' => $invitation->team_id,
                'player_id' => $invitation->player_id,
                'role' => 'member'
            ]);

            $invitation->status = 'accepted';
            $invitation->save();

            // Удаляем все остальные ожидающие приглашения для этого игрока в этой игре
            \App\Models\TeamInvitation::where('player_id', $playerId)
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->delete();

            // Отклоняем все ожидающие заявки от этого игрока в другие команды в этой игре
            \App\Models\JoinRequest::where('player_id', $playerId)
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->update(['status' => 'rejected']);
                
            // Отправляем событие о том, что новый участник присоединился
            event(new \App\Events\TeamMemberJoined($teamId));

            // Отправляем событие капитану о принятии приглашения
            $team = Team::find($teamId);
            if ($team) {
                Log::info('InvitationService: Отправка события InvitationAccepted', [
                    'team_id' => $teamId,
                    'captain_id' => $team->captain_id,
                    'player_id' => $playerId // Добавляем ID игрока, который принял приглашение
                ]);
                event(new \App\Events\InvitationAccepted($teamId, $team->captain_id, $playerId)); // Передаем playerId
            }

            DB::commit();

            // Очищаем кэш для команд пользователя
            Cache::forget("user_teams_{$playerId}_{$team->game_id}");
            Cache::forget("user_teams_{$playerId}_all_games"); // Добавляем очистку для всех игр

            // Очищаем кэш для капитана команды
            Cache::forget("user_teams_{$team->captain_id}_{$team->game_id}");
            Cache::forget("user_teams_{$team->captain_id}_all_games"); // Добавляем очистку для всех игр

            // Отправляем событие о том, что новый участник присоединился
            event(new \App\Events\TeamMemberJoined($teamId));

            // Отправляем событие капитану о принятии приглашения
            Log::info('InvitationService: Отправка события InvitationAccepted', [
                'team_id' => $teamId,
                'captain_id' => $captainId,
                'player_id' => $playerId
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при принятии приглашения: ' . $e->getMessage());
            throw $e; // Перебрасываем исключение, чтобы компонент мог его поймать и показать сообщение
        }
    }

    /**
     * Отклоняет приглашение в команду.
     *
     * @param int $invitationId ID приглашения
     * @param int $playerId ID игрока, который отклоняет приглашение
     * @return bool
     */
    public function declineInvitation(int $invitationId, int $playerId): bool
    {
        $invitation = TeamInvitation::find($invitationId);

        if (!$invitation) {
            throw new \Exception('Приглашение не найдено.');
        }

        if ($invitation->player_id !== $playerId) {
            throw new \Exception('У вас нет прав на это действие.');
        }

        // Удаляем приглашение вместо изменения статуса
        // чтобы избежать проблем с уникальным ключом
        $teamId = $invitation->team_id;
        $captainId = $invitation->team->captain_id;
        
        $invitation->delete();

        DB::afterCommit(function () use ($teamId, $playerId, $captainId) {
            event(new \App\Events\TeamInvitationCancelled($teamId, $playerId));
            // Уведомляем капитана об отклонении
            event(new \App\Events\InvitationDeclined($teamId, $captainId));
        });

        return true;
    }

    /**
     * Получает список ожидающих приглашений, отправленных командой.
     *
     * @param int $teamId ID команды
     * @return \Illuminate\Support\Collection
     */
    public function getPendingInvitationsForTeam(int $teamId): Collection
    {
        return TeamInvitation::where('team_id', $teamId)
            ->where('status', 'pending')
            ->with(['user:id,client_nick,avatar', 'inviter:id,client_nick,avatar']) // inviter здесь, чтобы показать кто пригласил
            ->get();
    }

    /**
     * Получает список приглашений, полученных игроком.
     *
     * @param int $playerId ID игрока
     * @return \Illuminate\Support\Collection
     */
    public function getReceivedInvitationsForPlayer(int $playerId): Collection
    {
        return TeamInvitation::where('player_id', $playerId)
            ->where('status', 'pending')
            ->with(['team', 'team.captain:id,client_nick,avatar']) // Загружаем команду и её капитана
            ->get();
    }
}



