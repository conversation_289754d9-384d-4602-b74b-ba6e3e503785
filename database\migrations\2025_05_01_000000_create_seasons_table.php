<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seasons', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->dateTime('start_date');
            $table->dateTime('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
        
        // Добавляем поле season_id в таблицу ratings, если его еще нет
        if (!Schema::hasColumn('ratings', 'season_id')) {
            Schema::table('ratings', function (Blueprint $table) {
                $table->foreignId('season_id')->nullable()->constrained('seasons')->onDelete('set null');
            });
        }
        
        // Обновляем индексы
        Schema::table('ratings', function (Blueprint $table) {
            // Проверяем существование старого индекса перед его удалением
            if (Schema::hasIndex('ratings', 'ratings_user_id_game_id_club_id_unique')) {
                // Создаем новый уникальный индекс, включающий season_id
                $table->unique(['user_id', 'game_id', 'club_id', 'season_id'], 'ratings_user_game_club_season_unique');
                
                // И только после этого удаляем старый индекс
                $table->dropUnique('ratings_user_id_game_id_club_id_unique');
            } else if (!Schema::hasIndex('ratings', 'ratings_user_game_club_season_unique')) {
                // Если старого индекса нет, но и новый еще не создан
                $table->unique(['user_id', 'game_id', 'club_id', 'season_id'], 'ratings_user_game_club_season_unique');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ratings', function (Blueprint $table) {
            // Проверяем существование нового индекса перед его удалением
            if (Schema::hasIndex('ratings', 'ratings_user_game_club_season_unique')) {
                // Сначала восстанавливаем старый уникальный индекс, если его нет
                if (!Schema::hasIndex('ratings', 'ratings_user_id_game_id_club_id_unique')) {
                    $table->unique(['user_id', 'game_id', 'club_id'], 'ratings_user_id_game_id_club_id_unique');
                }
                
                // Затем удаляем новый индекс
                $table->dropUnique('ratings_user_game_club_season_unique');
            }
            
            // Удаляем внешний ключ и колонку, если она существует
            if (Schema::hasColumn('ratings', 'season_id')) {
                $table->dropForeign(['season_id']);
                $table->dropColumn('season_id');
            }
        });
        
        Schema::dropIfExists('seasons');
    }
};

