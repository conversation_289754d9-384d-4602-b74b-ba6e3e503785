<!DOCTYPE html>
<html>
<head>
    <title>Test Match Notifications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <div class="container-fluid mt-3">
        <h2 class="text-center mb-4">Тест уведомлений между командами</h2>
        
        <div class="row">
            <!-- Команда 1 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="ri-team-line me-2"></i>
                            Команда 1
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <iframe src="{{ route('find.match', 1) }}" width="100%" height="700" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
            
            <!-- Команда 2 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="ri-team-line me-2"></i>
                            Команда 2
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <iframe src="{{ route('find.match', 2) }}" width="100%" height="700" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Панель управления -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="ri-settings-line me-2"></i>
                            Панель тестирования
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="btn-group-vertical w-100" role="group">
                                    <button type="button" class="btn btn-success mb-2" onclick="simulateTeamAccepted(1)">
                                        <i class="ri-check-line me-1"></i> Команда 1 принимает матч
                                    </button>
                                    <button type="button" class="btn btn-success mb-2" onclick="simulateTeamAccepted(2)">
                                        <i class="ri-check-line me-1"></i> Команда 2 принимает матч
                                    </button>
                                    <button type="button" class="btn btn-danger mb-2" onclick="simulateMatchDeclined(1)">
                                        <i class="ri-close-line me-1"></i> Команда 1 отклоняет матч
                                    </button>
                                    <button type="button" class="btn btn-danger mb-2" onclick="simulateMatchDeclined(2)">
                                        <i class="ri-close-line me-1"></i> Команда 2 отклоняет матч
                                    </button>
                                    <button type="button" class="btn btn-warning mb-2" onclick="simulateMapBanned(1)">
                                        <i class="ri-map-pin-line me-1"></i> Команда 1 банит карту
                                    </button>
                                    <button type="button" class="btn btn-warning mb-2" onclick="simulateMapBanned(2)">
                                        <i class="ri-map-pin-line me-1"></i> Команда 2 банит карту
                                    </button>
                                    <button type="button" class="btn btn-info mb-2" onclick="simulateVoterSwitch()">
                                        <i class="ri-arrow-left-right-line me-1"></i> Переключить ход
                                    </button>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="matchId" class="form-label">ID матча:</label>
                                    <input type="number" class="form-control" id="matchId" value="1">
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="mapId" class="form-label">ID карты:</label>
                                    <input type="number" class="form-control" id="mapId" value="1">
                                </div>
                                
                                <div class="alert alert-info">
                                    <small>
                                        <strong>Инструкция:</strong><br>
                                        1. Откройте обе команды<br>
                                        2. Нажмите "Создать тестовый матч" в одной из команд<br>
                                        3. Используйте кнопки для симуляции событий<br>
                                        4. Наблюдайте уведомления в реальном времени
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function simulateTeamAccepted(teamId) {
            const matchId = document.getElementById('matchId').value;
            sendEvent('/test-event/team-accepted', {
                match_id: parseInt(matchId),
                team_id: teamId
            }, `Команда ${teamId} приняла матч`);
        }
        
        function simulateMatchDeclined(teamId) {
            const matchId = document.getElementById('matchId').value;
            sendEvent('/test-event/match-declined', {
                match_id: parseInt(matchId),
                team_id: teamId
            }, `Команда ${teamId} отклонила матч`);
        }
        
        function simulateMapBanned(teamId) {
            const matchId = document.getElementById('matchId').value;
            const mapId = document.getElementById('mapId').value;
            sendEvent('/test-event/map-banned', {
                match_id: parseInt(matchId),
                map_id: parseInt(mapId),
                team_id: teamId,
                banned_maps: [parseInt(mapId)]
            }, `Команда ${teamId} забанила карту ${mapId}`);
        }
        
        function simulateVoterSwitch() {
            const matchId = document.getElementById('matchId').value;
            const currentVoter = Math.random() > 0.5 ? 'team1' : 'team2';
            sendEvent('/test-event/voter-switched', {
                match_id: parseInt(matchId),
                current_voter: currentVoter
            }, `Ход переключен на ${currentVoter}`);
        }
        
        function sendEvent(url, data, description) {
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                console.log(`✅ ${description}:`, result);
                showToast('success', description, 'Событие отправлено успешно');
            })
            .catch(error => {
                console.error(`❌ Ошибка при отправке ${description}:`, error);
                showToast('error', 'Ошибка', `Не удалось отправить событие: ${description}`);
            });
        }
        
        function showToast(type, title, message) {
            // Простое уведомление
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const toast = document.createElement('div');
            toast.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <strong>${title}</strong><br>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
