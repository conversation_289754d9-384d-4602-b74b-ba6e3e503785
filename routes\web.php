<?php

use App\Models\GameMatch;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Events\MatchFound;
use App\Http\Controllers\MatchController;

Route::get('/', App\Livewire\Home::class)->name('home');

Route::get('/login', App\Livewire\Auth\Login::class)->name('login');
Route::get('/register', App\Livewire\Auth\Register::class)->name('register');
Route::get('/forgot-password', App\Livewire\Auth\ForgotPassword::class)->name('password.request');
Route::get('/reset-password/{token}', App\Livewire\Auth\ResetPassword::class)->name('password.reset');

// Защищенные маршруты
Route::middleware(['auth', 'last_user_activity'])->group(function () {
    Route::get('/dashboard', App\Livewire\Dashboard::class)->name('dashboard');
    Route::post('/logout', function () {
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();
        return redirect('/login');
    })->name('logout');

    Route::get('profile', App\Livewire\Auth\Profile::class)->name('profile');
    Route::get('team', App\Livewire\Team::class)->name('team');
    Route::get('create/team/{gameId?}', App\Livewire\CreateTeam::class)->name('create.team');
    Route::get('/find-team/{gameId?}', \App\Livewire\FindTeam::class)->name('find.team');

    // Маршрут для управления токенами
    Route::get('tokens', App\Livewire\Auth\TokenManager::class)->name('tokens');

    // Маршруты для MatchController
    Route::post('/match/{gameType}/start', [MatchController::class, 'startMatch']);
    Route::post('/match/{gameType}/result', [MatchController::class, 'addMatchResult']);

    // Маршрут для отображения результатов матчей через Livewire
    Route::get('matches', App\Livewire\MatchResults::class)->name('matches');
    
    // Новый маршрут для отображения матчей через новый Livewire компонент
    Route::get('game-matches', App\Livewire\Matches::class)->name('game-matches');

    Route::get('/rating/{type?}', App\Livewire\Rating::class)->name('rating');

    Route::get('/player/{player}', App\Livewire\Player\Show::class)->name('player.show');

    Route::get('/club/{club:club_id}', App\Livewire\Club\Show::class)->name('club.show');
    Route::get('/club-rating/{clubId}', \App\Livewire\ClubRating::class)->name('club.rating');

    Route::get('/match/{match}/ban-maps', App\Livewire\MapBan::class)->name('match.ban-maps');

    // Маршрут для поиска матча
    Route::get('/find-match/{teamId?}', \App\Livewire\FindMatch::class)->name('find.match');
    // Route::get('find-match-pusher/{teamId?}', \App\Livewire\FindMatchWithPusher::class)->name('find.match.pusher');

    Route::post('/broadcasting/auth', [\Illuminate\Broadcasting\BroadcastController::class, 'authenticate'])->middleware(['auth']);
});

// Route::get('/test-pusher/{teamId}', function ($teamId) {
//     event(new \App\Events\TeamMemberJoined($teamId));
//     return "Событие отправлено для команды $teamId";
// });

// Маршрут для авторизации по токену через Livewire
Route::get('auth-token', App\Livewire\Auth\TokenAuth::class)->name('auth.token');

// Маршрут для ошибок токенной авторизации через Livewire
Route::get('auth-token-error', App\Livewire\Auth\TokenError::class)->name('auth.token.error');

// AJAX маршрут для проверки обновлений матча

Route::get('/pusher-test', App\Livewire\PusherTest::class)->name('pusher.test');
