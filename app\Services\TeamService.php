<?php

namespace App\Services;

use App\Services\Interfaces\TeamManagerInterface;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use App\Models\Game;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth; // Возможно, понадобится для некоторых методов, но лучше передавать userId

class TeamService implements TeamManagerInterface
{
    /**
     * Получает список команд пользователя для указанной игры.
     * Если $gameId не указан, возвращает команды пользователя для всех игр.
     *
     * @param int $userId ID пользователя
     * @param int|null $gameId ID игры (может быть null)
     * @return \Illuminate\Support\Collection
     */
    public function getUserTeamsForGame(int $userId, ?int $gameId = null): Collection
    {
        // Ключ кэша должен учитывать, загружаем ли мы команды для конкретной игры или для всех
        $cacheKey = "user_teams_{$userId}" . ($gameId !== null ? "_{$gameId}" : "_all_games");

        // Кэшируем данные команд на 5 минут
        return Cache::remember($cacheKey, 300, function() use ($userId, $gameId) {
            $query = Team::where(function($q) use ($userId) {
                $q->whereHas('members', function($subQ) use ($userId) {
                    $subQ->where('player_id', $userId);
                })
                ->orWhere('captain_id', $userId);
            });

            // Условно добавляем фильтр по game_id, если он был передан
            if ($gameId !== null) {
                $query->where('game_id', $gameId);
            }

            return $query->with([
                'captain:id,client_nick,avatar',
                'game:id,name,team_size',
                'members.user:id,client_nick,avatar',
                'invitations' => function($query) {
                    $query->where('status', 'pending')
                        ->with('user:id,client_nick,avatar');
                },
                'requests' => function($query) {
                    $query->where('status', 'pending')
                        ->with('user:id,client_nick,avatar');
                }
            ])
            ->withCount([
                'matches',
                'matches as wins_count' => function($query) {
                    $query->where('victory', true);
                },
                'matches as losses_count' => function($query) {
                    $query->where('victory', false);
                }
            ])
            ->get();
        });
    }

    /**
     * Расформировывает команду.
     *
     * @param int $teamId ID команды
     * @param int $userId ID пользователя (капитана)
     * @return bool True в случае успеха, false в случае ошибки
     */
    public function disbandTeam(int $teamId, int $userId): bool
    {
        try {
            DB::beginTransaction();

            $team = Team::find($teamId);

            if (!$team) {
                throw new \Exception('Команда не найдена');
            }

            if ($team->captain_id !== $userId) {
                throw new \Exception('Только капитан может расформировать команду');
            }

            $gameId = $team->game_id;

            \App\Models\JoinRequest::where('team_id', $teamId)->delete();
            \App\Models\TeamInvitation::where('team_id', $teamId)->delete();
            TeamMember::where('team_id', $teamId)->delete();
            $team->delete();

            DB::commit();

            // Очищаем кэш
            Cache::forget("user_teams_{$userId}_{$gameId}");
            Cache::forget("team_stats_{$teamId}"); // Если есть отдельный кэш статистики

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при расформировании команды: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Исключает участника из команды.
     *
     * @param int $teamId ID команды
     * @param int $memberId ID исключаемого участника
     * @param int $captainId ID капитана, который выполняет действие
     * @return bool True в случае успеха, false в случае ошибки
     */
    public function kickMember(int $teamId, int $memberId, int $captainId): bool
    {
        try {
            DB::beginTransaction();

            if (!$this->isUserCaptainOfTeam($captainId, $teamId)) {
                throw new \Exception('Только капитан может исключать участников');
            }

            if ($memberId === $captainId) {
                throw new \Exception('Вы не можете исключить себя из команды');
            }

            $teamMember = TeamMember::where('team_id', $teamId)
                ->where('player_id', $memberId)
                ->first();

            if (!$teamMember) {
                throw new \Exception('Участник не найден в этой команде');
            }

            $teamMember->delete();

            // Также можно удалить принятые приглашения, если они есть
            \App\Models\TeamInvitation::where('team_id', $teamId)
                ->where('player_id', $memberId)
                ->where('status', 'accepted')
                ->delete();

            $team = Team::find($teamId);
            if ($team) {
                Cache::forget("user_teams_{$captainId}_{$team->game_id}");
                Cache::forget("user_teams_{$memberId}_{$team->game_id}");
                Cache::forget("team_stats_{$teamId}");
            }

            DB::commit();

            // Отправляем событие исключенному игроку
            Log::info('TeamService: Отправляем событие TeamMemberKicked', [
                'team_id' => $teamId,
                'member_id' => $memberId,
                'team_name' => $team ? $team->name : 'Unknown',
                'channel' => 'player.' . $memberId
            ]);
            
            event(new \App\Events\TeamMemberKicked($teamId, $memberId));
            
            Log::info('TeamService: Событие TeamMemberKicked отправлено успешно');

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при исключении участника: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, является ли пользователь капитаном команды.
     *
     * @param int $userId ID пользователя
     * @param int $teamId ID команды
     * @return bool
     */
    public function isUserCaptainOfTeam(int $userId, int $teamId): bool
    {
        return Team::where('id', $teamId)->where('captain_id', $userId)->exists();
    }

    /**
     * Проверяет, состоит ли пользователь в команде для указанной игры.
     *
     * @param int $userId ID пользователя
     * @param int $gameId ID игры
     * @return bool
     */
    public function hasUserTeamForGame(int $userId, int $gameId): bool
    {
        // Проверяем без кэша для точности
        return TeamMember::whereHas('team', function($query) use ($gameId) {
            $query->where('game_id', $gameId);
        })->where('player_id', $userId)->exists() ||
        Team::where('captain_id', $userId)->where('game_id', $gameId)->exists();
    }

    /**
     * Получает требуемый размер команды для игры.
     */
    public function getRequiredTeamSize(int $gameId): int
    {
        $game = Game::find($gameId);
        if ($game && $game->team_size) {
            Log::info('Team size from game', ['game_id' => $gameId, 'team_size' => $game->team_size]);
            return $game->team_size;
        }

        // Fallback для игр, у которых нет явного размера команды
        $fallbackSize = match ((int)$gameId) {
            3 => 4, // PUBG
            default => 5, // CS2 и Dota2
        };
        
        Log::info('Team size fallback', ['game_id' => $gameId, 'fallback_size' => $fallbackSize]);
        return $fallbackSize;
    }

    /**
     * Проверяет, полна ли команда.
     */
    public function isTeamFull(int $teamId): bool
    {
        $team = Team::find($teamId);
        
        if (!$team) {
            return false;
        }

        // Принудительно перезагружаем отношения 'members' и 'game'
        $team->load('members', 'game');

        // Очищаем кэш, связанный с этой командой и капитаном, чтобы гарантировать актуальность
        Cache::forget("team_stats_{$teamId}");
        Cache::forget("user_teams_{$team->captain_id}_{$team->game_id}");
        Cache::forget("user_teams_{$team->captain_id}_all_games");
        
        
        if (!$team) {
            return false;
        }
        
        // Подсчитываем всех членов команды, включая капитана, если он есть в списке членов
        $currentMembersCount = $team->members->count();
        // Если капитан не является членом команды, добавляем его к счетчику
        if (!$team->members->contains('player_id', $team->captain_id)) {
            $currentMembersCount++;
        }
        $requiredTeamSize = $this->getRequiredTeamSize($team->game_id);
        
        Log::info('Team full check corrected', [
            'team_id' => $teamId,
            'team_name' => $team->name,
            'all_members_count' => $team->members->count(),
            'captain_id' => $team->captain_id,
            'current_members_total' => $currentMembersCount,
            'required_size' => $requiredTeamSize,
            'is_full' => $currentMembersCount >= $requiredTeamSize
        ]);
        
        return $currentMembersCount >= $requiredTeamSize;
    }
}








