<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class MatchResultsTest extends TestCase
{
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем необходимые таблицы для тестов
        $this->createTestTables();
        
        // Создаем игру для тестов
        $this->createGame();
    }

    /**
     * Тест загрузки страницы результатов матчей
     */
    public function test_match_results_page_loads(): void
    {
        // Создаем пользователя
        $user = $this->createUser();
        
        // Создаем несколько матчей
        $this->createTestMatches($user->id);
        
        // Проверяем, что неаутентифицированный пользователь перенаправляется
        $response = $this->get('/matches');
        $response->assertStatus(302);
        
        // Проверяем, что перенаправление идет на страницу входа
        $response->assertRedirect('/login');
        
        // Тест успешно пройден, если перенаправление работает как ожидается
        $this->assertTrue(true);
    }

    /**
     * Создает тестовую игру
     */
    protected function createGame()
    {
        if (DB::table('games')->where('code', 'cs2')->doesntExist()) {
            DB::table('games')->insert([
                'name' => 'CS2',
                'code' => 'cs2',
                'image' => 'games/cs2.jpg',
                'team_size' => 5,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    /**
     * Создает тестового пользователя
     */
    protected function createUser()
    {
        // Проверяем, существует ли пользователь с тестовым email
        $existingUser = DB::table('players')->where('email', '<EMAIL>')->first();
        
        if ($existingUser) {
            return (object) [
                'id' => $existingUser->id,
                'client_nick' => $existingUser->client_nick,
                'email' => $existingUser->email
            ];
        }
        
        // Создаем нового пользователя
        $userId = DB::table('players')->insertGetId([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'client_status' => 'web',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        return (object) [
            'id' => $userId,
            'client_nick' => 'Test User',
            'email' => '<EMAIL>'
        ];
    }

    /**
     * Создает тестовые матчи для пользователя
     */
    protected function createTestMatches($userId)
    {
        // Создаем несколько матчей
        for ($i = 0; $i < 3; $i++) {
            $matchId = DB::table('game_matches')->insertGetId([
                'game_type' => 'cs2',
                'session_id' => 'test-session-' . $this->faker->uuid,
                'match_id' => 'test-match-' . $this->faker->uuid,
                'player_id' => $userId,
                'date_scan' => now(),
                'col_scan' => 1,
                'score' => 100,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // Создаем результат матча
            DB::table('match_results')->insert([
                'match_id' => $matchId,
                'player_id' => $userId,
                'victory' => $i % 2 == 0, // Чередуем победы и поражения
                'add_score' => 100,
                'details' => json_encode([
                    'kills' => 15,
                    'deaths' => 5,
                    'assists' => 3
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    /**
     * Создает необходимые таблицы для тестов
     */
    protected function createTestTables()
    {
        // Создаем таблицу players, если она не существует
        if (!Schema::hasTable('players')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS players (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_id VARCHAR,
                    client_nick VARCHAR,
                    email VARCHAR,
                    password VARCHAR,
                    client_password VARCHAR,
                    client_hash VARCHAR,
                    auth_token VARCHAR,
                    auth_token_from_date DATETIME,
                    client_status VARCHAR NOT NULL DEFAULT "web",
                    change_password TINYINT(1) NOT NULL DEFAULT 0,
                    remember_token VARCHAR,
                    created_at DATETIME,
                    updated_at DATETIME
                )
            ');
        }
        
        // Создаем таблицу games, если она не существует
        if (!Schema::hasTable('games')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS games (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    code TEXT,
                    image TEXT NOT NULL DEFAULT "",
                    team_size INTEGER NOT NULL,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
        
        // Создаем таблицу game_matches, если она не существует
        if (!Schema::hasTable('game_matches')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS game_matches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_type TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    match_id TEXT,
                    score INTEGER,
                    match_score TEXT,
                    date_scan DATETIME,
                    col_scan INTEGER NOT NULL DEFAULT 0,
                    player_id INTEGER,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
        
        // Создаем таблицу match_results, если она не существует
        if (!Schema::hasTable('match_results')) {
            DB::statement('
                CREATE TABLE IF NOT EXISTS match_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    match_id INTEGER NOT NULL,
                    player_id INTEGER,
                    victory BOOLEAN NOT NULL DEFAULT 0,
                    round INTEGER NOT NULL DEFAULT 1,
                    add_score INTEGER NOT NULL DEFAULT 0,
                    details TEXT,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ');
        }
    }
}



