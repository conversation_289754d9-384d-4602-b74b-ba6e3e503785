<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int|null $club_id
 * @property string|null $club_name
 * @property-read mixed $name
 * @method static \Database\Factories\ClubFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Club newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Club newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Club query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Club whereClubId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Club whereClubName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Club whereId($value)
 * @mixin \Eloquent
 */
class Club extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'club_id',
        'club_name',
    ];
    
    // Добавляем аксессор для поля name
    public function getNameAttribute()
    {
        return $this->club_name;
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'club_id';
    }
}



