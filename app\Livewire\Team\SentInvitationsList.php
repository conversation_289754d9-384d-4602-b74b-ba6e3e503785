<?php

namespace App\Livewire\Team;

use App\Models\Game;
use App\Models\TeamInvitation;
use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache; // Добавляем импорт Cache
use App\Services\Interfaces\InvitationManagerInterface;

class SentInvitationsList extends Component
{
    public array $sentInvitations = [];
    public $gameId;

    protected InvitationManagerInterface $invitationService;

    // protected $listeners = [
    //     'invitation-declined' => 'loadSentInvitations',
    //     'invitation-accepted' => 'loadSentInvitations',
    //     'invitationSent' => 'loadSentInvitations',
    //     "echo-private:player.{$userId},InvitationAccepted" => 'handleInvitationAccepted',
    // ];

    public function getListeners(): array
    {
        $userId = Auth::id();
        return [
            'invitation-declined' => 'loadSentInvitations',
            // 'invitation-accepted' => 'loadSentInvitations',
            'invitationSent' => 'loadSentInvitations',
            "echo-private:player.{$userId},InvitationAccepted" => 'handleInvitationAccepted',
            'teamMemberLeft' => 'loadSentInvitations',
            "echo-private:player.{$userId},team.member.self.left" => 'loadSentInvitations', // Прослушиваем приватный канал капитана
        ];
    }


    public function boot(InvitationManagerInterface $invitationService)
    {
        $this->invitationService = $invitationService;
    }

    public function mount(?int $gameId = null)
    {
        // Приоритет: 1) переданный gameId, 2) значение из сессии, 3) первая игра
        if ($gameId) {
            $this->gameId = $gameId;
        } 
        elseif (session()->has('selectedGameId')) {
            $this->gameId = session('selectedGameId');
        }
        else {
            $this->gameId = Game::first()->id;
        }
        
        $this->loadSentInvitations();        
    }

    #[On('refreshSentInvitations')] // Добавляем слушатель для события Livewire
    #[On('invitationSent')] // Добавляем слушатель для события Livewire
    #[On('invitation-accepted')]
    #[On('teamMemberSelfLeft')] // Добавляем слушатель для нового события
    public function loadSentInvitations($event = null)
    {
        Log::info('SentInvitationsList: loadSentInvitations вызван', ['event_data' => $event, 'current_gameId' => $this->gameId]);

        // Если событие содержит teamId, используем его для определения gameId
        if (is_array($event) && isset($event['teamId'])) {
            $team = \App\Models\Team::find($event['teamId']);
            if ($team) {
                $this->gameId = $team->game_id;
            }
        } elseif (is_int($event)) { // Если передан gameId напрямую
            $this->gameId = $event;
        }

        Log::info('SentInvitationsList: Проверка условий перед загрузкой', [
            'Auth::check()' => Auth::check(),
            '$this->gameId' => $this->gameId
        ]);

        if (!Auth::check() || $this->gameId === null) {
            $this->sentInvitations = [];
            Log::info('SentInvitationsList: Пользователь не авторизован или gameId не установлен, приглашения не загружены.');
            return;
        }

        // Принудительно очищаем кэш перед загрузкой
        if (Auth::check()) {
            Cache::forget("team_pending_invitations_" . Auth::id() . "_{$this->gameId}");
        }

        Auth::user()->refresh();
        $captainTeam = Auth::user()->captainedTeams()->where('game_id', $this->gameId)->first();
        
        
        Log::info('SentInvitationsList: captainTeam', ['team' => $captainTeam ? $captainTeam->toArray() : null]);

        if ($captainTeam) {
            $this->sentInvitations = $this->invitationService->getPendingInvitationsForTeam($captainTeam->id)->toArray();
            Log::info('SentInvitationsList: Загружены приглашения для команды', [
                'invited_by' => $captainTeam->captain_id,
                'team_id' => $captainTeam->id,
                'count' => count($this->sentInvitations),
                'data' => $this->sentInvitations
            ]);
        } else {
            $this->sentInvitations = [];
            Log::info('SentInvitationsList: У пользователя нет команды капитана для текущей игры, приглашения не загружены.');
        }
    }
    
    /**
     * Отменить отправленное приглашение
     */
    public function cancelInvitation(int $invitationId)
    {
        try {
            // Получаем данные приглашения перед удалением
            $invitation = \App\Models\TeamInvitation::findOrFail($invitationId);
            $playerId = $invitation->player_id;
            $teamId = $invitation->team_id;

            // Отменяем приглашение
            $this->invitationService->cancelInvitation($invitationId, Auth::id());
            
            // Отправляем уведомление
            $this->dispatch('showNotification', [ 
                'type' => 'success',
                'message' => 'Приглашение отменено!'
            ]);

            // Отправляем Pusher событие
            if ($playerId) {
                event(new \App\Events\TeamInvitationCancelled($invitationId, $playerId));
            }

            // Обновляем список приглашений
            $this->loadSentInvitations();

        } catch (\Exception $e) {
            Log::error('Ошибка при отмене приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при отмене приглашения'
            ]);
        }
    }

    public function render()
    {
        $captainId = null;
        if (Auth::check()) {
            $captainTeam = Auth::user()->captainedTeams()->where('game_id', $this->gameId)->first();
            if ($captainTeam) {
                $captainId = $captainTeam->captain_id;
            }
        }

        return view('livewire.team.sent-invitations-list', [
            'sentInvitations' => $this->sentInvitations,
            'captainId' => $captainId,
        ]);
    }

    #[On('invitation-accepted')]
    public function handleInvitationAccepted()
    {
        Log::info('SentInvitationsList: handleInvitationAccepted вызван');
        $this->loadSentInvitations();
    }
}

