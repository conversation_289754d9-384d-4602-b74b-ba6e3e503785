<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $match_id
 * @property int $map_id
 * @property int $team_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Cs2Map|null $map
 * @property-read \App\Models\LiveMatch|null $match
 * @property-read \App\Models\Team|null $team
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap whereMapId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap whereMatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LiveMatchBannedMap whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class LiveMatchBannedMap extends Model
{
    protected $table = 'live_match_banned_maps';
    
    protected $fillable = [
        'match_id',
        'map_id',
        'team_id'
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Связь с матчем
     */
    public function match()
    {
        return $this->belongsTo(LiveMatch::class, 'match_id');
    }
    
    /**
     * Связь с картой CS2
     */
    public function map()
    {
        return $this->belongsTo(Cs2Map::class, 'map_id');
    }
    
    /**
     * Связь с командой, которая забанила карту
     */
    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }
}
