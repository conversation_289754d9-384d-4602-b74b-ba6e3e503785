<!DOCTYPE html>
<html>
<head>
    <title>Match Notifications Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="ri-notification-line me-2"></i>
                            Система уведомлений матчей
                        </h3>
                    </div>
                    <div class="card-body">
                        <h5>Как работают уведомления:</h5>
                        
                        <div class="alert alert-info">
                            <h6><i class="ri-information-line me-1"></i> Принятие матча</h6>
                            <p class="mb-0">Когда одна команда принимает матч, вторая команда получает уведомление: <strong>"Соперник принял матч!"</strong></p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="ri-error-warning-line me-1"></i> Отклонение матча</h6>
                            <p class="mb-0">Когда команда отклоняет матч, соперник получает уведомление: <strong>"Соперник отклонил матч"</strong></p>
                        </div>
                        
                        <div class="alert alert-success">
                            <h6><i class="ri-map-pin-line me-1"></i> Бан карт</h6>
                            <p class="mb-0">При бане карты соперник видит обновление в реальном времени и получает звуковое уведомление</p>
                        </div>
                        
                        <div class="alert alert-primary">
                            <h6><i class="ri-arrow-left-right-line me-1"></i> Смена хода</h6>
                            <p class="mb-0">Когда наступает ваш ход в бане карт, вы получаете уведомление: <strong>"Ваш ход!"</strong></p>
                        </div>
                        
                        <hr>
                        
                        <h5>Индикатор подключения:</h5>
                        <div class="d-flex align-items-center mb-3">
                            <span class="badge bg-success me-2">
                                <i class="ri-wifi-line me-1"></i> Подключено
                            </span>
                            <span class="text-muted">- Real-time уведомления работают</span>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <span class="badge bg-warning me-2">
                                <i class="ri-wifi-off-line me-1"></i> Отключено
                            </span>
                            <span class="text-muted">- Уведомления могут не работать</span>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <span class="badge bg-danger me-2">
                                <i class="ri-error-warning-line me-1"></i> Ошибка
                            </span>
                            <span class="text-muted">- Проблемы с подключением</span>
                        </div>
                        
                        <hr>
                        
                        <h5>Звуковые уведомления:</h5>
                        <ul>
                            <li><strong>Успех</strong> - при принятии матча соперником</li>
                            <li><strong>Ошибка</strong> - при отклонении матча</li>
                            <li><strong>Информация</strong> - при смене хода или бане карт</li>
                        </ul>
                        
                        <div class="alert alert-light">
                            <h6><i class="ri-lightbulb-line me-1"></i> Совет</h6>
                            <p class="mb-0">Для лучшего опыта разрешите воспроизведение звука в браузере</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="{{ route('find.match', 1) }}" class="btn btn-primary me-2">
                                <i class="ri-search-line me-1"></i> Найти матч
                            </a>
                            <a href="{{ route('test.notifications') }}" class="btn btn-outline-secondary me-2">
                                <i class="ri-test-tube-line me-1"></i> Тестировать уведомления
                            </a>
                            <a href="{{ route('test.pusher') }}" class="btn btn-outline-info">
                                <i class="ri-wifi-line me-1"></i> Тест подключения
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
