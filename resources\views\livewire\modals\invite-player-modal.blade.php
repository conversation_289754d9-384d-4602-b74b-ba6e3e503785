<div>
    <div x-data="{ 
            open: false,
            init() {
                window.addEventListener('open-invite-player-modal', () => {
                    this.open = true;
                });
                window.addEventListener('closeInviteModal', () => {
                    this.open = false;
                });
            }
        }"
        @keydown.escape.window="open = false"
        x-show="open"
        x-transition
        class="modal fade"
        :class="{ 'show d-block': open }"
        tabindex="-1"
        aria-labelledby="invitePlayerModalLabel"
        aria-hidden="true"
        wire:ignore.self>
        
        <div class="modal-backdrop fade" 
             :class="{ 'show': open }"
             @click="open = false"
             style="z-index: 1040;"></div>
             
        <div class="modal-dialog" style="z-index: 1050;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="invitePlayerModalLabel">Пригласить игрока</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">                    
                    <form wire:submit.prevent="sendInvitation">
                        <div class="mb-3">
                            <label class="form-label">Имя игрока <span class="text-danger">*</span></label>
                            
                            <div x-data="{ openDropdown: @entangle('showDropdown') }" @click.away="openDropdown = false" class="relative">
                                <div class="form-control cursor-pointer @error('playerName') is-invalid @enderror"
                                     @click="openDropdown = !openDropdown"
                                     :class="{'border-primary': openDropdown}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-truncate" :class="{'text-muted': !@js($playerName)}" x-text="@js($selectedPlayerName)"></span>
                                        <i class="ri-arrow-down-s-line" :class="{'rotate-180': openDropdown}"></i>
                                    </div>
                                </div>
                                
                                <div x-show="openDropdown" x-transition class="position-absolute w-100 mt-1 bg-white border rounded shadow-sm z-index-dropdown">
                                    <div class="p-2">
                                        <input type="text"
                                               class="form-control form-control-sm"
                                               placeholder="Поиск..."
                                               wire:model.live.debounce.300ms="search"
                                               @click.stop>
                                    </div>
                                    
                                    <div class="dropdown-menu-items ps-3" style="max-height: 200px; overflow-y: auto;">
                                        <div wire:loading wire:target="search, nextPage" class="text-center p-2">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Загрузка...</span>
                                            </div>
                                        </div>
                                        
                                        @if(strlen($search) > 1 && $this->playerOptions->count() > 0)
                                            @foreach($this->playerOptions as $player)
                                                <div wire:click="selectPlayer({{ $player->id }})"
                                                    @click.stop="openDropdown = false"
                                                    class="dropdown-item cursor-pointer @if($playerName == $player->id) active @endif">
                                                    <div class="d-flex align-items-center">
                                                        {{-- Проверяем, существует ли аватар --}}
                                                        @if($player->avatar)
                                                            <img src="{{ asset('images/avatars/' . $player->avatar) }}" alt="{{ $player->client_nick }}" class="rounded-circle me-2" style="width: 24px; height: 24px;" loading="lazy">
                                                        @else
                                                            <svg class="rounded-circle me-2 bg-secondary"
                                                                style="width: 24px; height: 24px;" fill="currentColor"
                                                                viewBox="0 0 24 24">
                                                                <path
                                                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                                            </svg>
                                                        @endif
                                                        <span>{{ $player->client_nick }}</span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @elseif(strlen($search) > 1)
                                            <div class="dropdown-item text-muted">
                                                Ничего не найдено
                                            </div>
                                        @endif
                                        
                                        @if(strlen($search) > 1 && $this->playerOptions->hasMorePages())
                                            <div class="dropdown-item">
                                                <button wire:click.prevent="nextPage" class="btn btn-link w-100 text-center">
                                                    Загрузить ещё
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            @error('playerName') <div class="invalid-feedback d-block">{{ $message }}</div> @enderror
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" @click="open = false">Отмена</button>
                            <button type="submit" class="btn btn-primary" @if(!$playerName) disabled @endif>Отправить приглашение</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <style>
        .modal.show {
            overflow-x: hidden;
            overflow-y: auto;
        }
        .modal-backdrop.show {
            opacity: .5;
        }
        .z-index-dropdown {
            z-index: 1000;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .rotate-180 {
            transform: rotate(180deg);
        }
    </style>
</div>