<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

class TokenService
{
    /**
     * Генерирует безопасный токен для пользователя
     */
    public function generateToken(User $user, int $clubId, int $expiryHours = 24): string
    {
        if (!$clubId) {
            throw new \Exception('Club ID is required to generate a token.');
        }
        // Создаем уникальный токен
        $token = Str::random(32);
        
        $user->update([
            'auth_token' => $token,
            'auth_token_from_date' => Carbon::now()->addHours($expiryHours),
            'club_id' => $clubId
        ]);
        
        // Логируем создание токена
        Log::info('Token generated', [
            'user_id' => $user->id,
            'club_id' => $clubId,
            'expires_at' => $user->auth_token_from_date,
        ]);
        
        return $token;
    }
    
    /**
     * Валидирует токен и возвращает информацию о пользователе
     */
    public function validateToken(string $token, int $clubId)
    {
        try {
            $token = trim($token);

            Log::debug('Token validation started', [
                'club_id' => $clubId,
                'token_preview' => substr($token, 0, 8) . '...',
                'current_user_id' => Auth::id(),
                'current_user_club' => Auth::user()?->club_id
            ]);

            // Ищем пользователя по токену
            $user = User::where('auth_token', $token)->first();

            if (!$user) {
                Log::warning('Token not found in database', ['token_preview' => substr($token, 0, 8) . '...']);
                return 'invalid_token';
            }

            // ВАЖНО: Проверяем club_id - пользователь не может авторизоваться в другом клубе
            if ($user->club_id != $clubId) {
                Log::warning('Club ID mismatch - user cannot login to different club', [
                    'user_club_id' => $user->club_id,
                    'requested_club_id' => $clubId,
                    'user_id' => $user->id
                ]);
                return 'invalid_club';
            }

            // Проверяем срок действия токена
            if (!$user->auth_token_from_date) {
                return 'token_expired';
            }

            if (!Carbon::parse($user->auth_token_from_date)->isFuture()) {
                Log::warning('Token expired', [
                    'user_id' => $user->id,
                    'expired_at' => $user->auth_token_from_date
                ]);
                return 'token_expired';
            }

            Log::info('Token validation successful - switching to user', [
                'user_id' => $user->id,
                'user_club' => $user->club_id,
                'user_nick' => $user->client_nick
            ]);
            
            return $user;

        } catch (\Exception $e) {
            Log::error('Token validation error', [
                'error' => $e->getMessage(),
                'club_id' => $clubId,
                'token_preview' => substr($token, 0, 8) . '...'
            ]);
            return 'exception';
        }
    }
    /**
     * Отзывает токен пользователя
     */
    public function invalidateToken(User $user): void
    {
        $user->update([
            'auth_token' => null,
            'auth_token_from_date' => null
        ]);
        
        // Добавляем в список отозванных токенов
        $this->revokeToken($user->id);
        
        Log::info('Token invalidated', ['user_id' => $user->id]);
    }
    
    /**
     * Отзывает токен по ID пользователя
     */
    public function invalidateTokenByUserId(int $userId): void
    {
        $user = User::find($userId);
        if ($user) {
            $this->invalidateToken($user);
        }
    }
    
    /**
     * Проверяет, отозван ли токен
     */
    private function isTokenRevoked(int $userId): bool
    {
        return Cache::has("revoked_token_{$userId}");
    }
    
    /**
     * Добавляет токен в список отозванных
     */
    private function revokeToken(int $userId): void
    {
        $key = "revoked_token_{$userId}";
        Cache::put($key, true, now()->addDays(7));
        
        // Сохраняем ключ в списке для возможности очистки
        $keys = Cache::get('revoked_token_keys', []);
        if (!in_array($key, $keys)) {
            $keys[] = $key;
            Cache::put('revoked_token_keys', $keys, now()->addDays(30));
        }
    }
    
    /**
     * Очищает все отозванные токены
     */
    public function clearRevokedTokens(): void
    {
        $keys = Cache::get('revoked_token_keys', []);
        
        if (!empty($keys)) {
            foreach ($keys as $key) {
                Cache::forget($key);
            }
        }
        
        Cache::forget('revoked_token_keys');
        Log::info('Revoked tokens cache cleared', ['count' => count($keys)]);
    }
    
    /**
     * Обновляет срок действия токена
     */
    public function extendToken(User $user, int $additionalHours = 24): bool
    {
        if (!$user->auth_token) {
            return false;
        }
        
        $user->update([
            'auth_token_from_date' => Carbon::now()->addHours($additionalHours)
        ]);
        
        Log::info('Token extended', [
            'user_id' => $user->id,
            'new_expires_at' => $user->auth_token_from_date
        ]);
        
        return true;
    }
    
    /**
     * Получает информацию о токене пользователя
     */
    public function getTokenInfo(User $user): array
    {
        return [
            'auth_token' => $user->auth_token ? '[Скрыт]' : null,
            'created_at' => $user->created_at,
            'expires_at' => $user->auth_token_from_date,
            'is_expired' => !$user->auth_token_from_date || Carbon::parse($user->auth_token_from_date)->isPast(),
            'is_valid' => $user->auth_token && $user->auth_token_from_date && Carbon::parse($user->auth_token_from_date)->isFuture(),
            'club_id' => $user->club_id
        ];
    }

    /**
     * Получает информацию о текущем токене авторизованного пользователя
     */
    public function getCurrentTokenInfo(): ?array
    {
        $user = Auth::user();
        if (!$user) {
            return null;
        }

        return $this->getTokenInfo($user);
    }
}

