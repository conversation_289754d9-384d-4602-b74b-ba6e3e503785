<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Game>
 */
class GameFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $games = [
            ['name' => 'CS2', 'team_size' => 5, 'image' => 'games/cs2.jpg'],
            ['name' => 'Dota 2', 'team_size' => 5, 'image' => 'games/dota2.jpg'],
            ['name' => 'PUBG', 'team_size' => 4, 'image' => 'games/pubg.jpg'],
        ];

        return $this->faker->randomElement($games);
    }
}


