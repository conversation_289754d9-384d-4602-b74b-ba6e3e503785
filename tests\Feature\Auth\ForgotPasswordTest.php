<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\Notification;
use Livewire\Livewire;
use Tests\TestCase;

class ForgotPasswordTest extends TestCase
{
    // Не используем RefreshDatabase

    public function test_forgot_password_page_contains_livewire_component(): void
    {
        $this->get('/forgot-password')
            ->assertSuccessful();
            //->assertSeeLivewire('auth.forgot-password');
    }

    public function test_can_request_password_reset_link(): void
    {
        Notification::fake();

        $user = User::create([
            'name' => 'resetuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        Livewire::test('auth.forgot-password')
            ->set('email', $user->email)
            ->call('sendResetLink');

        Notification::assertSentTo($user, ResetPassword::class);
    }

    public function test_shows_error_if_email_not_found(): void
    {
        Notification::fake();

        Livewire::test('auth.forgot-password')
            ->set('email', '<EMAIL>')
            ->call('sendResetLink')
            ->assertHasErrors(['email']);

        Notification::assertNothingSent();
    }

    public function test_email_is_required(): void
    {
        Livewire::test('auth.forgot-password')
            ->set('email', '')
            ->call('sendResetLink')
            ->assertHasErrors(['email' => 'required']);
    }
}
