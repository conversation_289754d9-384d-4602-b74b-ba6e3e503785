<?php

namespace App\Livewire\Auth;

use Illuminate\Support\Facades\Password;
use Livewire\Component;

class ForgotPassword extends Component
{
    public $email;

    protected $rules = [
        'email' => 'required|email',
    ];

    public function sendResetLink()
    {
        $this->validate([
            'email' => 'required|email',
        ], [
            'email.required' => 'Пожалуйста, введите email',
            'email.email' => 'Пожалуйста, введите корректный email',
        ]);

        $status = Password::sendResetLink(
            ['email' => $this->email]
        );

        if ($status === Password::RESET_LINK_SENT) {
            session()->flash('status', 'Ссылка для сброса пароля отправлена на ваш email');
            $this->reset('email');
        } else {
            $this->addError('email', 'Не удалось отправить ссылку для сброса пароля');
        }
    }

    public function render()
    {
        return view('livewire.auth.forgot-password');
    }
}