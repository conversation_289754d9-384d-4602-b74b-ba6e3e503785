<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\UsesSqliteTestDatabase;
use App\Models\LiveMatch;
use App\Models\Team;
use App\Models\User;
use App\Models\TeamMember;
use App\Models\Cs2Map;
use App\Models\LiveMatchBannedMap;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use App\Models\LiveMatchReady;

class MapBanTest extends TestCase
{
    use UsesSqliteTestDatabase, WithFaker;

    protected $team1;
    protected $team2;
    protected $captain1;
    protected $captain2;
    protected $match;
    protected $maps;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpSqliteDatabase();
        $this->createTestData();
    }

    protected function tearDown(): void
    {
        $this->tearDownSqliteDatabase();
        parent::tearDown();
    }

    protected function createTestData()
    {
        // Создаем карты CS2
        $this->maps = [
            Cs2Map::create(['name' => 'de_dust2', 'image_url' => 'dust2.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_mirage', 'image_url' => 'mirage.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_inferno', 'image_url' => 'inferno.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_nuke', 'image_url' => 'nuke.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_overpass', 'image_url' => 'overpass.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_train', 'image_url' => 'train.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_ancient', 'image_url' => 'ancient.jpg', 'is_active' => true]),
        ];

        // Создаем пользователей-капитанов
        $this->captain1 = User::create([
            'client_nick' => 'Captain1',
            'email' => '<EMAIL>',
            'client_password' => bcrypt('password'),
            'club_id' => '584'
        ]);

        $this->captain2 = User::create([
            'client_nick' => 'Captain2',
            'email' => '<EMAIL>',
            'client_password' => bcrypt('password'),
            'club_id' => '584'
        ]);

        // Создаем команды
        $this->team1 = Team::create([
            'name' => 'Team Alpha',
            'game_id' => 1, // CS2
            'captain_id' => $this->captain1->id,
            'rating' => 1000
        ]);

        $this->team2 = Team::create([
            'name' => 'Team Beta',
            'game_id' => 1, // CS2
            'captain_id' => $this->captain2->id,
            'rating' => 1000
        ]);

        // Добавляем капитанов в команды
        TeamMember::create([
            'team_id' => $this->team1->id,
            'player_id' => $this->captain1->id,
            'role' => 'captain'
        ]);

        TeamMember::create([
            'team_id' => $this->team2->id,
            'player_id' => $this->captain2->id,
            'role' => 'captain'
        ]);

        // Создаем матч
        $this->match = LiveMatch::create([
            'team1_id' => $this->team1->id,
            'team2_id' => $this->team2->id,
            'status' => MatchStatus::MAP_VOTING->value,
            'current_voter' => CurrentVoter::TEAM1->value,
            'current_voting_team' => $this->team1->id
        ]);
    }

    /** @test */
    public function test_map_ban_component_loads_with_match()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        $this->actingAs($this->captain1);
        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->assertSet('match.id', $this->match->id)
            ->assertSet('teamId', $this->team1->id)
            ->assertSet('maxBans', 6)
            ->assertSee('Бан карт для матча')
            ->assertSee('Доступные карты')
            ->assertSee('Забаненные карты');
    }

    /** @test */
    public function test_captain_can_ban_map_on_their_turn()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        $this->actingAs($this->captain1);
        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id)
            ->assertSet('bannedMaps', [$this->maps[0]->id])
            ->assertSee('Забанено карт: 1');
    }

    /** @test */
    public function test_captain_cannot_ban_map_on_opponent_turn()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        $this->match->update([
            'current_voting_team' => $this->team2->id
        ]);
        $this->actingAs($this->captain1);
        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id)
            ->assertSee('ожидайте');
    }

    /** @test */
    public function test_cannot_ban_already_banned_map()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        $this->actingAs($this->captain1);
        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component->call('banMap', $this->maps[0]->id);
        $component->call('banMap', $this->maps[0]->id)
            ->assertSee('Забанено карт: 1');
    }

    /** @test */
    public function test_cannot_ban_map_when_only_one_remaining()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        
        // Баним карты по очереди между командами
        $this->actingAs($this->captain1);
        $component1 = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component1->call('banMap', $this->maps[0]->id); // Team1 банит
        
        $this->actingAs($this->captain2);
        $component2 = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component2->call('banMap', $this->maps[1]->id); // Team2 банит
        
        $this->actingAs($this->captain1);
        $component1->call('banMap', $this->maps[2]->id); // Team1 банит
        
        $this->actingAs($this->captain2);
        $component2->call('banMap', $this->maps[3]->id); // Team2 банит
        
        $this->actingAs($this->captain1);
        $component1->call('banMap', $this->maps[4]->id); // Team1 банит
        
        $this->actingAs($this->captain2);
        $component2->call('banMap', $this->maps[5]->id); // Team2 банит - остается 1 карта
        
        $component2->call('refreshMatchData');
        $component2->assertSee('Выбранная карта');
    }

    /** @test */
    public function test_voting_switches_after_ban()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        $this->actingAs($this->captain1);
        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $this->assertEquals($this->team1->id, $this->match->current_voting_team);
        $component->call('banMap', $this->maps[0]->id);
        $this->match->refresh();
        $this->assertEquals($this->team2->id, $this->match->current_voting_team);
    }

    /** @test */
    public function test_final_map_selected_when_only_one_remaining()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        
        // Баним карты по очереди между командами
        $this->actingAs($this->captain1);
        $component1 = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component1->call('banMap', $this->maps[0]->id); // Team1 банит
        
        $this->actingAs($this->captain2);
        $component2 = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component2->call('banMap', $this->maps[1]->id); // Team2 банит
        
        $this->actingAs($this->captain1);
        $component1->call('banMap', $this->maps[2]->id); // Team1 банит
        
        $this->actingAs($this->captain2);
        $component2->call('banMap', $this->maps[3]->id); // Team2 банит
        
        $this->actingAs($this->captain1);
        $component1->call('banMap', $this->maps[4]->id); // Team1 банит
        
        $this->actingAs($this->captain2);
        $component2->call('banMap', $this->maps[5]->id); // Team2 банит - остается 1 карта
        
        $this->match->refresh();
        $this->assertNotNull($this->match->selected_map_id);
        $component2->assertSee('Выбранная карта');
    }

    /** @test */
    public function test_ban_map_creates_database_record()
    {
        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id);

        // Проверяем, что запись создана в базе
        $bannedMap = LiveMatchBannedMap::where('match_id', $this->match->id)
            ->where('map_id', $this->maps[0]->id)
            ->where('team_id', $this->team1->id)
            ->first();

        $this->assertNotNull($bannedMap);
    }

    /** @test */
    public function test_unban_map_removes_database_record()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Баним карту
        $component->call('banMap', $this->maps[0]->id);
        
        // Проверяем, что карта забанена
        $this->assertTrue($this->match->bannedMaps()->where('map_id', $this->maps[0]->id)->exists());
        
        // Разбаниваем карту
        $component->call('unbanMap', $this->maps[0]->id);
        
        // Проверяем, что карта разбанена
        $this->assertFalse($this->match->bannedMaps()->where('map_id', $this->maps[0]->id)->exists());
    }

    /** @test */
    public function test_available_maps_loaded_correctly()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Проверяем, что загружены все активные карты
        $this->assertCount(7, $component->get('availableMaps'));
        
        // Проверяем, что все карты активны
        foreach ($component->get('availableMaps') as $map) {
            $this->assertTrue($map->is_active);
        }
    }

    /** @test */
    public function test_banned_maps_synced_correctly()
    {
        $this->actingAs($this->captain1);

        // Создаем бан в базе данных
        LiveMatchBannedMap::create([
            'match_id' => $this->match->id,
            'map_id' => $this->maps[0]->id,
            'team_id' => $this->team1->id
        ]);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Проверяем, что забаненная карта синхронизирована
        $this->assertContains($this->maps[0]->id, $component->get('bannedMaps'));
    }

    /** @test */
    public function test_opponent_team_determined_correctly()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Проверяем, что определена команда соперника
        $this->assertEquals($this->team2->id, $component->get('opponents')->id);
    }

    /** @test */
    public function test_refresh_match_data_updates_component()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Баним карту в базе данных напрямую
        LiveMatchBannedMap::create([
            'match_id' => $this->match->id,
            'map_id' => $this->maps[0]->id,
            'team_id' => $this->team1->id
        ]);
        
        // Обновляем данные матча
        $component->call('refreshMatchData');
        
        // Проверяем, что забаненная карта появилась в компоненте
        $this->assertContains($this->maps[0]->id, $component->get('bannedMaps'));
    }

    /** @test */
    public function test_handle_map_banned_event()
    {
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain1->id, 'is_ready' => true]);
        LiveMatchReady::create(['match_id' => $this->match->id, 'player_id' => $this->captain2->id, 'is_ready' => true]);
        $this->match->update(['status' => MatchStatus::MAP_VOTING->value]);
        $this->actingAs($this->captain1);
        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Сначала забанится карта через banMap
        $component->call('banMap', $this->maps[0]->id);
        
        // Потом симулируем событие для обновления стейта
        $event = [
            'matchId' => $this->match->id,
            'mapId' => $this->maps[0]->id,
            'fromCaptainId' => $this->captain1->id,
            'toCaptainId' => $this->captain2->id,
            'nextVotingTeamId' => $this->team2->id
        ];
        $component->call('handleMapBanned', $event);
        $component->match->refresh();
        $component->call('refreshMatchData');
        $this->assertContains($this->maps[0]->id, $component->get('bannedMaps'));
    }

    /** @test */
    public function test_component_renders_without_errors()
    {
        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->assertStatus(200);
    }

    /** @test */
    public function test_blade_template_exists_and_contains_required_elements()
    {
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $this->assertFileExists($templatePath);

        $content = file_get_contents($templatePath);
        $this->assertStringContainsString('wire:click="banMap', $content);
        $this->assertStringContainsString('currentVotingTeam', $content);
        $this->assertStringContainsString('bannedMaps', $content);
    }
}
