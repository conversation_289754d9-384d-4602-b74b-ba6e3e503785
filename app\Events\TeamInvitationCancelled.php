<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\TeamInvitation;

class TeamInvitationCancelled implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $invitationId;
    public $playerId;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(int $invitationId, int $playerId = null)
    {
        $this->invitationId = $invitationId;

        // Если playerId не передан, пытаемся получить его из приглашения
        if ($playerId === null) {
            $invitation = TeamInvitation::find($invitationId);
            $this->playerId = $invitation ? $invitation->player_id : null;
        } else {
            $this->playerId = $playerId;
        }
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        if (!$this->playerId) {
            return new PrivateChannel('team.error'); // Или другой канал для ошибок
        }
        \Illuminate\Support\Facades\Log::info('TeamInvitationCancelled: broadcasting on player.' . $this->playerId);
        return new PrivateChannel('player.' . $this->playerId);
    }

    public function broadcastAs()
    {
        return 'team.invitation.cancelled';
    }
}