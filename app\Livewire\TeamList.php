<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Livewire\Modals\CreateTeamModal;
use App\Models\Team; // Импортируем модель Team
use App\Models\TeamMember; // Импортируем модель TeamMember
use App\Models\Game; // Импортируем модель Game

// Импортируем сервисы
use App\Services\Interfaces\TeamManagerInterface;
use App\Services\Interfaces\InvitationManagerInterface;
use App\Services\Interfaces\RequestManagerInterface;

class TeamList extends Component
{
    // ID выбранной игры, который будет передаваться из родительского компонента CreateTeam
    public ?int $gameId = null;

    // Свойства для хранения данных, которые будут отображаться
    public array $userTeams = [];
    public array $teamMembers = []; // Члены команды
    public array $pendingInvitations = []; // Приглашения, отправленные командой
    public array $pendingRequests = []; // Заявки, полученные командой
    public array $teamStats = []; // Статистика команды

    // Инъекция сервисов
    protected TeamManagerInterface $teamService;
    protected InvitationManagerInterface $invitationService;
    protected RequestManagerInterface $requestService;

    public function boot(
        TeamManagerInterface $teamService,
        InvitationManagerInterface $invitationService,
        RequestManagerInterface $requestService
    ) {
        // Livewire 3 автоматически внедряет зависимости в метод boot, если вы не используете __construct
        $this->teamService = $teamService;
        $this->invitationService = $invitationService;
        $this->requestService = $requestService;
    }

    /**
     * Mount lifecycle hook. Инициализируем gameId, если он передан.
     * @param int|null $gameId
     */
    public function mount(?int $gameId = null)
    {
        $this->gameId = $gameId;
        $this->loadTeamData();
    }

    /**
     * Слушатель события 'refreshTeamList' из родительского компонента (или любого другого)
     * для обновления списка команд при смене игры или других событиях.
     * @param int $gameId
     */
    #[On('refreshTeamList')]
    public function refreshTeamList(int $gameId = null)
    {
        if ($gameId) {
            $this->gameId = $gameId;
        }

        // Принудительно очищаем кэш перед загрузкой
        if (Auth::check()) {
            Cache::forget("user_teams_" . Auth::id() . "_{$this->gameId}");
            Cache::forget("user_teams_" . Auth::id() . "_all_games");
        }

        $this->loadTeamData();
        
        Log::info('TeamList: refreshTeamList выполнен', [
            'game_id' => $this->gameId,
            'user_id' => Auth::id()
        ]);
    }

    #[On('teamCreated')] // Событие создания новой команды
    #[On('teamDisbanded')] // Событие расформирования команды
    #[On('memberKicked')] // Событие исключения участника
    #[On('joinRequestAccepted')] // Событие принятия заявки на вступление
    #[On('joinRequestRejected')] // Событие отклонения заявки на вступление
    public function handleTeamUpdate($gameId = null)
    {
        Log::info('TeamList: handleTeamUpdate вызван', ['game_id_param' => $gameId, 'current_game_id' => $this->gameId]);
        // Если передан gameId и он отличается от текущего, обновляем
        if ($gameId && $gameId != $this->gameId) {
            $this->gameId = $gameId;
        }

        // При любом из этих событий, вызываем refreshTeamList для принудительной очистки кэша и перезагрузки данных
        $this->refreshTeamList($this->gameId);
    }

    /**
     * Загружает все данные, связанные с командами пользователя для текущей игры.
     */
    public function loadTeamData(): void
    {
        if (!Auth::check() || $this->gameId === null) {
            $this->userTeams = [];
            $this->teamMembers = [];
            $this->pendingInvitations = [];
            $this->pendingRequests = [];
            $this->teamStats = [];
            return;
        }

        // Используем TeamService для получения команд
        // Убедимся, что eager loading для members.user включен
        $teams = $this->teamService->getUserTeamsForGame(Auth::id(), $this->gameId)->load('members.user');

        $this->userTeams = $teams->toArray();
        $this->teamMembers = [];
        $this->pendingInvitations = [];
        $this->pendingRequests = [];
        $this->teamStats = [];

        foreach ($teams as $team) {
            $this->teamMembers[$team->id] = $team->members->map(function ($member) {
                $memberArray = $member->toArray();
                $memberArray['user']['is_online'] = $member->user->isOnline();
                return $memberArray;
            })->toArray();

            $this->teamStats[$team->id] = [
                'matches' => $team->matches_count,
                'wins' => $team->wins_count,
                'losses' => $team->losses_count,
                'draws' => 0,
                'rating' => $team->rating ?? 0,
            ];
        }
        
        Log::info('TeamList: loadTeamData завершен', [
            'user_id' => Auth::id(),
            'game_id' => $this->gameId,
            'teams_count' => count($this->userTeams),
            'pending_requests' => $this->pendingRequests,
            'team_members_count' => count($this->teamMembers) // Добавляем логирование количества членов команды
        ]);
    }

    /**
     * Расформировать команду
     */
    public function disbandTeam(int $teamId)
    {
        try {
            $this->teamService->disbandTeam($teamId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Команда успешно расформирована!'
            ]);
            $this->dispatch('teamDisbanded'); // Оповещаем родителя или другие компоненты
        } catch (\Exception $e) {
            Log::error('Ошибка при расформировании команды: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Исключить участника из команды
     */
    public function kickMember(int $teamId, int $memberId)
    {
        try {
            $this->teamService->kickMember($teamId, $memberId, Auth::id());
            
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Участник успешно исключен из команды!'
            ]);
            $this->dispatch('memberKicked');
        } catch (\Exception $e) {
            Log::error('Ошибка при исключении участника: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Пригласить игрока в команду (открывает модальное окно)
     */
    public function invitePlayer(int $teamId)
    {
        // Проверяем, является ли пользователь капитаном команды
        if (!$this->teamService->isUserCaptainOfTeam(Auth::id(), $teamId)) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Только капитан может приглашать игроков.'
            ]);
            return;
        }

        $this->dispatch('open-invite-player-modal', ['teamId' => $teamId]);
    }

    /**
     * Отменить отправленное приглашение
     */
    public function cancelInvitation(int $invitationId)
    {
        try {
            // Получаем данные приглашения перед удалением для Pusher события
            $invitation = \App\Models\TeamInvitation::find($invitationId);
            $playerId = $invitation ? $invitation->player_id : null;

            $this->invitationService->cancelInvitation($invitationId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение отменено!'
            ]);

            // Отправляем Pusher событие об отмене приглашения
            if ($playerId) {
                Log::info('TeamList: Отправляем событие отмены приглашения', [
                    'invitation_id' => $invitationId,
                    'player_id' => $playerId
                ]);

                try {
                    // Получаем teamId из приглашения, так как событие TeamInvitationCancelled не принимает teamId напрямую в конструкторе
                    $teamId = $invitation->team_id;
                    event(new \App\Events\TeamInvitationCancelled($invitationId, $playerId));
                    Log::info('TeamList: Pusher событие отмены отправлено успешно', [
                        'invitation_id' => $invitationId,
                        'player_id' => $playerId,
                        'team_id' => $teamId // Добавляем teamId для логирования
                    ]);
                } catch (\Exception $e) {
                    Log::error('TeamList: Ошибка отправки Pusher события отмены: ' . $e->getMessage());
                }
            }

            // Обновляем данные команд
            $this->loadTeamData();

        } catch (\Exception $e) {
            Log::error('Ошибка при отмене приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
            session()->flash('error', 'Ошибка при отмене приглашения.');
        }
    }

    /**
     * Принять заявку на вступление в команду
     */
    public function acceptRequest(int $requestId)
    {
        try {
            $this->requestService->acceptRequest($requestId, Auth::id());
            
            // Принудительно очищаем кэш для обновления данных
            if (Auth::check()) {
                Cache::forget("user_teams_" . Auth::id() . "_{$this->gameId}");
                Cache::forget("user_teams_" . Auth::id() . "_all_games");
            }
            
            // Перезагружаем данные команды
            $this->loadTeamData();
            
            session()->flash('success', 'Заявка принята! Игрок добавлен в команду.');
            
        } catch (\Exception $e) {
            Log::error('Ошибка при принятии заявки: ' . $e->getMessage());
            session()->flash('error', 'Ошибка при принятии заявки');
        }
    }

    /**
     * Отклонить заявку на вступление в команду
     */
    public function rejectRequest(int $requestId)
    {
        try {
            $this->requestService->rejectRequest($requestId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Заявка отклонена.'
            ]);
            $this->dispatch('joinRequestRejected');
            session()->flash('error', 'Заявка отклонена.');
        } catch (\Exception $e) {
            Log::error('Ошибка при отклонении заявки: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
            session()->flash('error', 'Ошибка при отклонении заявки');
        }
    }

    /**
     * Метод для настройки прослушивания каналов
     */
    public function getListeners(): array
    {
        $userId = Auth::id();
        return [
            'refreshTeamList' => 'loadTeamData',
            'teamCreated' => '$refresh',
            'teamDisbanded' => '$refresh',
            'memberKicked' => '$refresh',
            // Удаляем внутренний слушатель для teamMemberSelfLeft, так как он будет обрабатываться через Pusher
            'joinRequestAccepted' => 'loadTeamData',
            'joinRequestRejected' => 'loadTeamData',
            'teamMemberJoined' => 'loadTeamData',
            "echo-private:player.{$userId},InvitationAccepted" => 'handleInvitationAccepted',
            "echo-private:player.{$userId},.team.member.self.left" => 'handleTeamMemberLeft', // Слушаем Pusher событие
        ];
    }

    /**
     * Обработчик события принятия приглашения.
     */
    public function handleInvitationAccepted($event): void
    {
        Log::info('TeamList: handleInvitationAccepted вызван', [
            'event_data' => $event,
            'user_id' => Auth::id()
        ]);

        // Принудительно очищаем кэш
        if (Auth::check()) {
            Cache::forget("user_teams_" . Auth::id() . "_{$this->gameId}");
            Cache::forget("user_teams_" . Auth::id() . "_all_games");
        }

        $this->loadTeamData();
        
        $this->dispatch('showNotification', [
            'type' => 'success',
            'message' => 'Приглашение принято. Игрок вступил в команду!'
        ]);

        session()->flash('success', 'Приглашение принято. Игрок вступил в команду!');
    }

    /**
     * Обработчик выхода участника из команды
     */
    public function handleTeamMemberLeft($event): void
    {
        Log::info('TeamList: handleTeamMemberLeft вызван', [
            'event_data' => $event,
            'user_id' => Auth::id(),
            'current_game_id' => $this->gameId
        ]);

        // Получаем teamId из события
        $teamId = $event['teamId'] ?? null;

        if ($teamId) {
            $team = Team::find($teamId);
            if ($team) {
                $gameId = $team->game_id;
                // Вызываем refreshTeamList с gameId команды, из которой вышел игрок
                $this->refreshTeamList($gameId);
            } else {
                // Если команда не найдена, просто обновляем текущий список
                $this->refreshTeamList($this->gameId);
            }
        } else {
            // Если teamId не доступен в событии, обновляем текущий список
            $this->refreshTeamList($this->gameId);
        }

        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Участник покинул команду.'
        ]);
        // Отправляем событие для обновления списка отправленных приглашений
        $this->dispatch('refreshSentInvitations');
        $this->dispatch('$refresh'); // Принудительное обновление компонента
    }

    public function handleTeamInvitationCancelled(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Приглашение отменено!'
        ]);
    }

    public function handleTeamInvitationSent(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Новое приглашение отправлено!'
        ]);
    }

    public function handleTeamMemberJoined(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Новый участник присоединился к вашей команде!'
        ]);
        session()->flash('success', 'Приглашение принято. Игрок вступил в команду!');
    }

    /**
     * Найти соперника для команды
     */
    public function findOpponent(int $teamId)
    {
        try {
            // Проверяем, является ли пользователь капитаном команды
            if (!$this->teamService->isUserCaptainOfTeam(Auth::id(), $teamId)) {
                throw new \Exception('Только капитан может искать соперника.');
            }

            // Получаем команду для проверки game_id
            $team = Team::find($teamId);
            if (!$team) {
                throw new \Exception('Команда не найдена.');
            }

            // Проверяем количество участников команды
            $requiredSize = $this->teamService->getRequiredTeamSize($team->game_id);
            $currentMembersCount = count($this->teamMembers[$teamId] ?? []); // Используем загруженные данные

            if ($currentMembersCount < $requiredSize) {
                throw new \Exception("Для поиска соперника необходимо минимум {$requiredSize} участников в команде.");
            }

            // Сохраняем ID команды в сессии для использования на странице поиска матча
            session(['searching_team_id' => $teamId]);

            // Перенаправляем на страницу поиска матча
            return $this->redirect(route('find.match', ['teamId' => $teamId]), navigate: true);

        } catch (\Exception $e) {
            Log::error('Ошибка при поиске соперника: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Создать новый матч для команды (вызывается из кнопки)
     */
    public function createNewMatch(int $teamId)
    {
        try {
            // Проверяем, является ли пользователь капитаном команды
            if (!$this->teamService->isUserCaptainOfTeam(Auth::id(), $teamId)) {
                throw new \Exception('Только капитан может создавать новые матчи.');
            }

            // Получаем команду для проверки game_id
            $team = Team::find($teamId);
            if (!$team) {
                throw new \Exception('Команда не найдена.');
            }

            // Проверяем количество участников команды
            $requiredSize = $this->teamService->getRequiredTeamSize($team->game_id);
            $currentMembersCount = count($this->teamMembers[$teamId] ?? []);

            if ($currentMembersCount < $requiredSize) {
                throw new \Exception("Для создания матча необходимо минимум {$requiredSize} участников в команде.");
            }

            // Отправляем событие для открытия модального окна CreateMatchModal
            $this->dispatch('open-create-match-modal', ['teamId' => $teamId]);

        } catch (\Exception $e) {
            Log::error('Ошибка при создании нового матча: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Покинуть команду
     */
    public function leaveTeam(int $teamId)
    {
        try {
            // Проверяем, состоит ли пользователь в этой команде
            $teamMember = TeamMember::where('team_id', $teamId)
                ->where('player_id', Auth::id())
                ->first();
            
            if (!$teamMember) {
                $this->dispatch('showNotification', [
                    'type' => 'error',
                    'message' => 'Вы не состоите в этой команде'
                ]);
                return;
            }
            
            // Проверяем, не является ли пользователь капитаном
            if ($teamMember->role === 'captain') {
                $this->dispatch('showNotification', [
                    'type' => 'error',
                    'message' => 'Капитан не может покинуть команду. Сначала передайте права капитана другому участнику или расформируйте команду.'
                ]);
                return;
            }
            
            // Удаляем пользователя из команды
            $teamMember->delete();
            
            // Удаляем принятые приглашения для этой команды
            \App\Models\TeamInvitation::where('team_id', $teamId)
                ->where('player_id', Auth::id())
                ->where('status', 'accepted')
                ->delete();
            
            // Очищаем ВСЕ кэши для пользователя
            $team = Team::find($teamId);
            if ($team) {
                Cache::forget("user_teams_" . Auth::id() . "_{$team->game_id}");
                Cache::forget("user_teams_" . Auth::id() . "_all_games");
                
                // Дополнительно очищаем кэш для всех игр
                $games = \App\Models\Game::all();
                foreach ($games as $game) {
                    Cache::forget("user_teams_" . Auth::id() . "_{$game->id}");
                }
            }
            
            // Отправляем событие об изменении состава команды
            Log::info('TeamList: Отправляем событие TeamMemberSelfLeft', [
                'team_id' => $teamId,
                'player_id' => Auth::id()
            ]);
            event(new \App\Events\TeamMemberSelfLeft($teamId, Auth::id()));
            
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Вы успешно покинули команду'
            ]);
            
            // Обновляем данные
            $this->loadTeamData();
            session()->flash('success', 'Вы успешно покинули команду');
            
        } catch (\Exception $e) {
            Log::error('Ошибка при выходе из команды: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    public function triggerCreateTeamModal()
    {
        $this->dispatch('prepareCreateTeamModal', gameId: $this->gameId)->to(CreateTeamModal::class);
    }

    /**
     * Загружает ожидающие заявки для всех команд пользователя
     */
    protected function loadPendingRequests(): void
    {
        foreach ($this->userTeams as $team) {
            $this->pendingRequests[$team['id']] = $this->requestService->getPendingRequestsForTeam($team['id'])->toArray();
        }
    }
    
    public function render()
    {
        return view('livewire.team-list', [
            'hasTeamInGame' => count($this->userTeams) > 0,
            'currentUserId' => Auth::id(), // Передаем ID текущего пользователя для проверок в Blade
            'gameName' => $this->gameId ? (Game::find($this->gameId)->name ?? 'Неизвестная игра') : 'Не выбрана',
        ]);
    }
}






